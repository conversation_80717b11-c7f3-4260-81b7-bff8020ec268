import { config, library } from "@fortawesome/fontawesome-svg-core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
// This is important, we are going to let <PERSON><PERSON>t worry about the CSS
config.autoAddCss = false;

/* import specific icons */
import {
  faAngleDown,
  faAngleRight,
  faAnglesLeft,
  faAnglesRight,
  faArrowLeft,
  faArrowRight,
  faArrowUp,
  faCalendarDays,
  faCaretDown,
  faCheck,
  faChevronLeft,
  faChevronRight,
  faComments,
  faHandPointDown,
  faLongArrowRight,
  faMagnifyingGlass,
  faMinus,
  faPlay,
  faPlus,
  faSearch,
  faSpinner,
  faStar,
  faTimes,
  faUserSecret,
} from "@fortawesome/free-solid-svg-icons";

/* add icons to the library */
library.add(
  faComments,
  faAnglesLeft,
  faAnglesRight,
  faArrowRight,
  faArrowLeft,
  faMagnifyingGlass,
  faPlus,
  faMinus,
  faAngleDown,
  faHandPointDown,
  faUserSecret,
  faAngleRight,
  faCalendarDays,
  faChevronRight,
  faChevronLeft,
  faLongArrowRight,
  faSpinner,
  faTimes,
  faCaretDown,
  faPlay,
  faStar,
  faArrowUp,
  faSearch,
  faCheck
);

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.component("fa", FontAwesomeIcon);
});

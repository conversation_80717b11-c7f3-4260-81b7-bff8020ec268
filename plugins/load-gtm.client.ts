// plugins/load-gtm.client.ts
// import { defineNuxtPlugin } from '#app'
// import { useCookieControl } from '@dargmuesli/nuxt-cookie-control'

export default defineNuxtPlugin((nuxtApp) => {
  const { cookiesEnabledIds } = useCookieControl();

  // let { enable, loadScript, id } = nuxtApp.$config.public.gtm
  const { enableAnalytics, disableAnalytics, initialize } = useGtag();

  const loadGtmScript = () => {
    initialize();
  };

  const removeGtmScript = () => {
    disableAnalytics();
    // enable = false;
    // loadScript = false;
    // let gtmScript = document.querySelector(
    //   "script[src*='googletagmanager.com/gtm.js']"
    // );
    // if (gtmScript) {
    //   gtmScript.parentNode.removeChild(gtmScript);
    // }
  };

  if (
    cookiesEnabledIds.value &&
    cookiesEnabledIds.value.includes("google_analytics")
  ) {
    loadGtmScript();
  } else if (
    cookiesEnabledIds.value &&
    !cookiesEnabledIds.value.includes("google_analytics")
  ) {
    removeGtmScript();
  }

  watch(
    () => cookiesEnabledIds.value,
    (current, previous) => {
      if (
        !previous?.includes("google_analytics") &&
        current?.includes("google_analytics")
      ) {
        // cookie with id `google-analytics` got added
        loadGtmScript();
      } else {
        removeGtmScript();
      }
    },
    { deep: true }
  );
});

<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
  <meta charset="utf8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="x-apple-disable-message-reformatting">
  <title>Devxhub</title>
  {% load static %}
  <!--[if !mso]><!-->
  <link href="https://fonts.googleapis.com/css?family=Pacifico" rel="stylesheet"><!--<![endif]-->
  <!--[if mso]>
  <xml>
    <o:OfficeDocumentSettings>
      <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
  </xml>
  <style>
    table {border-collapse: collapse;}
    td,th,div,p,a,h1,h2,h3,h4,h5,h6 {font-family: "Segoe UI", sans-serif; mso-line-height-rule: exactly;}
  </style>
  <![endif]-->
  <style>
    .hover-bg-indigo-700:hover {
      background-color: #4C51BF !important;
    }

    .hover-text-gray-800:hover {
      color: #2D3748 !important;
    }

    .sub-logo-contect {
      margin-left: 2px;
      font-size: 10px;
      color: #221e1e;
    }


    @media screen {
      img {
        max-width: 100%;
      }

      td,
      th {
        box-sizing: border-box;
      }

      /* u~div .wrapper {
        min-width: 100vw;
      } */

      a[x-apple-data-detectors] {
        color: inherit;
        text-decoration: none;
      }

      .all-font-sans {
        font-family: -apple-system, "Segoe UI", sans-serif !important;
      }
    }

    @media (max-width: 600px) {
      .sm-block {
        display: block !important;
      }

      .sm-hidden {
        display: none !important;
      }

      .sm-h-12 {
        height: 12px !important;
      }

      .sm-h-32 {
        height: 32px !important;
      }

      .sm-leading-48 {
        line-height: 48px !important;
      }

      .sm-p-16 {
        padding: 16px !important;
      }

      .sm-px-0 {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }

      .sm-py-32 {
        padding-top: 32px !important;
        padding-bottom: 32px !important;
      }

      .sm-pl-12 {
        padding-left: 12px !important;
      }

      .sm-pb-16 {
        padding-bottom: 16px !important;
      }

      .sm-text-base {
        font-size: 16px !important;
      }

      .sm-text-xl {
        font-size: 20px !important;
      }

      .sm-text-5xl {
        font-size: 48px !important;
      }

      .sm-w-full {
        width: 100% !important;
      }
    }
  </style>
  <style>
    @media screen {
      .font-pacifico {
        font-family: 'Pacifico', cursive;
      }
    }

    @media (max-width: 600px) {
      .sm-bg-quote {
        background-repeat: no-repeat;
        background-size: 77px;
      }
    }
  </style>
</head>

<body
  style="box-sizing: border-box; margin: 0; padding: 0; width: 100%; word-break: break-word; -webkit-font-smoothing: antialiased;">
  <table class="wrapper all-font-sans" width="100%" cellpadding="0" cellspacing="0" role="presentation">
    <tr>
      <td align="center" class="sm-p-16" style="padding-top: 40px; padding-bottom: 40px;" width="100%">
        <table class="sm-w-full" width="600" cellpadding="0" cellspacing="0" role="presentation">
          <tr>
            <td>
              <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
                <tr>
                  <td width="50%">
                    <h1 class="sans-serif"
                      style="margin: 0; color: #667EEA; font-size: 36px; line-height: 0.8; white-space: nowrap;">Devxhub
                    </h1>
                    <small class="sub-logo-contect" style="white-space: nowrap;">Where Dreams Become True</small>
                    <!-- <div style="width: fit-content; padding: 5px; border-radius: 8px;">
                      <a href="https://devxhub.com/" target="_blank"><img
                         src="{% static 'image/devxhub-logo-black.png' %}" alt="devxhub-logo" style="height: 60px;"
                          border="0"></a>
                    </div> -->
                  </td>
                  <td align="right" width="50%">
                    <table cellpadding="0" cellspacing="0" role="presentation">
                      <tr>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td class="sm-py-32" style="padding-bottom: 40px; padding-top: 80px; text-align: center;" align="center">
              <h2 class="sm-text-5xl sm-leading-48"
                style="font-family: -apple-system, 'Segoe UI', sans-serif; line-height: 64px; margin: 0; color: #000000; font-size: 45px;">
                {{title}}</h2>
              <div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1500 650" width="1500" height="650"
                  preserveAspectRatio="xMidYMid meet"
                  style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px);">
                  <defs>
                    <clipPath id="__lottie_element_4">
                      <rect width="1500" height="650" x="0" y="0"></rect>
                    </clipPath>
                  </defs>
                  <g clip-path="url(#__lottie_element_4)">
                    <g transform="matrix(1,0,0,1,750,325)" opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"
                          stroke-dasharray=" 8" stroke-dashoffset="0" stroke="rgb(80,0,255)" stroke-opacity="1"
                          stroke-width="2"
                          d=" M-764.9669799804688,15.680000305175781 C-764.9669799804688,15.680000305175781 -654.301025390625,-64.83599853515625 -593.2020263671875,80.53700256347656 C-572.5330200195312,129.71499633789062 -592.489013671875,139.6929931640625 -606.031005859375,136.8419952392578 C-619.572998046875,133.99099731445312 -645.2310180664062,72.6969985961914 -522.6430053710938,45.61399841308594 C-400.05499267578125,18.5310001373291 -374.3970031738281,122.58799743652344 -320.9429931640625,40.625 C-286.7650146484375,-11.781999588012695 -245.302001953125,-93.3270034790039 -164.8820037841797,-145.13699340820312">
                        </path>
                      </g>
                    </g>
                    <g class="ai"
                      transform="matrix(0.7988165616989136,-0.043498288840055466,0.043498288840055466,0.7988165616989136,519.5535278320312,-86.84696960449219)"
                      opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,45.49100112915039,333.37701416015625)">
                        <path fill="rgb(118,119,43)" fill-opacity="1"
                          d=" M-35.374000549316406,-0.6269999742507935 C-35.374000549316406,-0.6269999742507935 -34.5620002746582,20.625999450683594 -34.5620002746582,20.625999450683594 C-34.5620002746582,20.625999450683594 -19.31999969482422,15.36400032043457 -19.31999969482422,15.36400032043457 C-19.31999969482422,15.36400032043457 35.37300109863281,-20.625999450683594 35.37300109863281,-20.625999450683594 C35.37300109863281,-20.625999450683594 -35.374000549316406,-0.6269999742507935 -35.374000549316406,-0.6269999742507935z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,51.49100112915039,339.7879943847656)">
                        <path fill="rgb(156,115,247)" fill-opacity="1"
                          d=" M-29.37299919128418,0.4830000102519989 C-29.37299919128418,0.4830000102519989 -16.66900062561035,27.038000106811523 -16.66900062561035,27.038000106811523 C-16.66900062561035,27.038000106811523 29.37299919128418,-27.038000106811523 29.37299919128418,-27.038000106811523 C29.37299919128418,-27.038000106811523 -29.37299919128418,0.4830000102519989 -29.37299919128418,0.4830000102519989z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,41.189998626708984,322.75)">
                        <path fill="rgb(156,115,247)" fill-opacity="1"
                          d=" M-39.67399978637695,-9.397000312805176 C-39.67399978637695,-9.397000312805176 -31.07200050354004,10 -31.07200050354004,10 C-31.07200050354004,10 39.67399978637695,-9.99899959564209 39.67399978637695,-9.99899959564209 C39.67399978637695,-9.99899959564209 -39.67399978637695,-9.397000312805176 -39.67399978637695,-9.397000312805176z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,18.548999786376953,347.1369934082031)">
                        <path fill="rgb(60,61,0)" fill-opacity="1"
                          d=" M3.569000005722046,-6.866000175476074 C3.569000005722046,-6.866000175476074 7.620999813079834,1.6039999723434448 7.620999813079834,1.6039999723434448 C7.620999813079834,1.6039999723434448 -7.620999813079834,6.866000175476074 -7.620999813079834,6.866000175476074 C-7.620999813079834,6.866000175476074 3.569000005722046,-6.866000175476074 3.569000005722046,-6.866000175476074z">
                        </path>
                      </g>
                    </g>
                    <g transform="matrix(1,0,0,1,750,325)" opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"
                          stroke-dasharray=" 8" stroke-dashoffset="0" stroke="rgb(80,0,255)" stroke-opacity="1"
                          stroke-width="2"
                          d=" M-764.9669799804688,15.680000305175781 C-764.9669799804688,15.680000305175781 -654.301025390625,-64.83599853515625 -593.2020263671875,80.53700256347656 C-572.5330200195312,129.71499633789062 -592.489013671875,139.6929931640625 -606.031005859375,136.8419952392578 C-619.572998046875,133.99099731445312 -645.2310180664062,72.6969985961914 -522.6430053710938,45.61399841308594 C-400.05499267578125,18.5310001373291 -374.3970031738281,122.58799743652344 -320.9429931640625,40.625 C-286.5369873046875,-12.130999565124512 -244.7449951171875,-95.00399780273438 -163.26400756835938,-146.79800415039062">
                        </path>
                      </g>
                    </g>
                    <g class="ai"
                      transform="matrix(0.799045979976654,-0.03905880078673363,0.03905880078673363,0.799045979976654,522.6213989257812,-88.84752655029297)"
                      opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,45.49100112915039,333.37701416015625)">
                        <path fill="rgb(118,119,43)" fill-opacity="1"
                          d=" M-35.374000549316406,-0.6269999742507935 C-35.374000549316406,-0.6269999742507935 -34.5620002746582,20.625999450683594 -34.5620002746582,20.625999450683594 C-34.5620002746582,20.625999450683594 -19.31999969482422,15.36400032043457 -19.31999969482422,15.36400032043457 C-19.31999969482422,15.36400032043457 35.37300109863281,-20.625999450683594 35.37300109863281,-20.625999450683594 C35.37300109863281,-20.625999450683594 -35.374000549316406,-0.6269999742507935 -35.374000549316406,-0.6269999742507935z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,51.49100112915039,339.7879943847656)">
                        <path fill="rgb(156,115,247)" fill-opacity="1"
                          d=" M-29.37299919128418,0.4830000102519989 C-29.37299919128418,0.4830000102519989 -16.66900062561035,27.038000106811523 -16.66900062561035,27.038000106811523 C-16.66900062561035,27.038000106811523 29.37299919128418,-27.038000106811523 29.37299919128418,-27.038000106811523 C29.37299919128418,-27.038000106811523 -29.37299919128418,0.4830000102519989 -29.37299919128418,0.4830000102519989z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,41.189998626708984,322.75)">
                        <path fill="rgb(156,115,247)" fill-opacity="1"
                          d=" M-39.67399978637695,-9.397000312805176 C-39.67399978637695,-9.397000312805176 -31.07200050354004,10 -31.07200050354004,10 C-31.07200050354004,10 39.67399978637695,-9.99899959564209 39.67399978637695,-9.99899959564209 C39.67399978637695,-9.99899959564209 -39.67399978637695,-9.397000312805176 -39.67399978637695,-9.397000312805176z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,18.548999786376953,347.1369934082031)">
                        <path fill="rgb(60,61,0)" fill-opacity="1"
                          d=" M3.569000005722046,-6.866000175476074 C3.569000005722046,-6.866000175476074 7.620999813079834,1.6039999723434448 7.620999813079834,1.6039999723434448 C7.620999813079834,1.6039999723434448 -7.620999813079834,6.866000175476074 -7.620999813079834,6.866000175476074 C-7.620999813079834,6.866000175476074 3.569000005722046,-6.866000175476074 3.569000005722046,-6.866000175476074z">
                        </path>
                      </g>
                    </g>
                    <g transform="matrix(1,0,0,1,750,325)" opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"
                          stroke-dasharray=" 8" stroke-dashoffset="0" stroke="rgb(80,0,255)" stroke-opacity="1"
                          stroke-width="2"
                          d=" M-764.9669799804688,15.680000305175781 C-764.9669799804688,15.680000305175781 -654.301025390625,-64.83599853515625 -593.2020263671875,80.53700256347656 C-572.5330200195312,129.71499633789062 -592.489013671875,139.6929931640625 -606.031005859375,136.8419952392578 C-619.572998046875,133.99099731445312 -645.2310180664062,72.6969985961914 -522.6430053710938,45.61399841308594 C-400.05499267578125,18.5310001373291 -374.3970031738281,122.58799743652344 -320.9429931640625,40.625 C-286.7619934082031,-11.78600025177002 -245.29200744628906,-93.91899871826172 -164.86099243164062,-145.7760009765625">
                        </path>
                      </g>
                    </g>
                    <g class="ai"
                      transform="matrix(0.7987802028656006,-0.044161438941955566,0.044161438941955566,0.7987802028656006,519.2160034179688,-87.3706283569336)"
                      opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,45.49100112915039,333.37701416015625)">
                        <path fill="rgb(118,119,43)" fill-opacity="1"
                          d=" M-35.374000549316406,-0.6269999742507935 C-35.374000549316406,-0.6269999742507935 -34.5620002746582,20.625999450683594 -34.5620002746582,20.625999450683594 C-34.5620002746582,20.625999450683594 -19.31999969482422,15.36400032043457 -19.31999969482422,15.36400032043457 C-19.31999969482422,15.36400032043457 35.37300109863281,-20.625999450683594 35.37300109863281,-20.625999450683594 C35.37300109863281,-20.625999450683594 -35.374000549316406,-0.6269999742507935 -35.374000549316406,-0.6269999742507935z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,51.49100112915039,339.7879943847656)">
                        <path fill="rgb(156,115,247)" fill-opacity="1"
                          d=" M-29.37299919128418,0.4830000102519989 C-29.37299919128418,0.4830000102519989 -16.66900062561035,27.038000106811523 -16.66900062561035,27.038000106811523 C-16.66900062561035,27.038000106811523 29.37299919128418,-27.038000106811523 29.37299919128418,-27.038000106811523 C29.37299919128418,-27.038000106811523 -29.37299919128418,0.4830000102519989 -29.37299919128418,0.4830000102519989z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,41.189998626708984,322.75)">
                        <path fill="rgb(156,115,247)" fill-opacity="1"
                          d=" M-39.67399978637695,-9.397000312805176 C-39.67399978637695,-9.397000312805176 -31.07200050354004,10 -31.07200050354004,10 C-31.07200050354004,10 39.67399978637695,-9.99899959564209 39.67399978637695,-9.99899959564209 C39.67399978637695,-9.99899959564209 -39.67399978637695,-9.397000312805176 -39.67399978637695,-9.397000312805176z">
                        </path>
                      </g>
                      <g opacity="1" transform="matrix(1,0,0,1,18.548999786376953,347.1369934082031)">
                        <path fill="rgb(60,61,0)" fill-opacity="1"
                          d=" M3.569000005722046,-6.866000175476074 C3.569000005722046,-6.866000175476074 7.620999813079834,1.6039999723434448 7.620999813079834,1.6039999723434448 C7.620999813079834,1.6039999723434448 -7.620999813079834,6.866000175476074 -7.620999813079834,6.866000175476074 C-7.620999813079834,6.866000175476074 3.569000005722046,-6.866000175476074 3.569000005722046,-6.866000175476074z">
                        </path>
                      </g>
                    </g>
                    <g transform="matrix(1,0,0,1,754.2659912109375,325)" opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,-741.7830200195312,4.578999996185303)">
                        <path fill="rgb(255,255,255)" fill-opacity="1"
                          d=" M0,-12.482999801635742 C6.889367580413818,-12.482999801635742 12.482999801635742,-6.889367580413818 12.482999801635742,0 C12.482999801635742,6.889367580413818 6.889367580413818,12.482999801635742 0,12.482999801635742 C-6.889367580413818,12.482999801635742 -12.482999801635742,6.889367580413818 -12.482999801635742,0 C-12.482999801635742,-6.889367580413818 -6.889367580413818,-12.482999801635742 0,-12.482999801635742z">
                        </path>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
            </td>
          </tr>
          <!-- <tr>
            <td style="padding-bottom: 40px;">
              <a href="https://example.com" style="text-decoration: none;">
                <img src="{% static '/cya-quitting-time.jpg' %}"
                  width="600" alt="Quitting Time by Katerina Limpitsouni on undraw.co"
                  style="border: 0; line-height: 100%; vertical-align: middle;">
              </a>
            </td>
          </tr> -->
          <tr>
            <td>
              <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
                <tr>
                  <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
                    <img src="#" width="54"
                      style="border: 0; line-height: 100%; vertical-align: middle;">
                  </td> -->
                  <td class="sm-block sm-w-full" width="83.33333%">
                    <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                      Job Type:</h3>
                    <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{type}}</p>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="sm-h-32" height="25"></td>
                </tr>
                <tr>
                  <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
                    <img src="#" width="54"
                      style="border: 0; line-height: 100%; vertical-align: middle;">
                  </td> -->
                  <td class="sm-block sm-w-full" width="83.33333%">
                    <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                      Job Nature :</h3>
                    <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{location}}</p>
                  </td>
                <tr>
                  <td colspan="2" class="sm-h-32" height="25"></td>
                </tr>
          </tr>
          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                Office Time: </h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{working_hour}}</p>
            </td>
          </tr>
          <tr>
            <td colspan="2" class="sm-h-32" height="25"></td>
          </tr>
          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                Weekend: </h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{working_day}}</p>
            </td>
          </tr>
          <tr>
            <td colspan="2" class="sm-h-32" height="25"></td>
          </tr>
          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                Salary: </h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{salary}}</p>
            </td>
          </tr>
          <tr>
            <td colspan="2" class="sm-h-32" height="25"></td>
          </tr>
          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                Experience:</h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{experience}}</p>
            </td>
          </tr>
          <tr>
            <td colspan="2" class="sm-h-32" height="25"></td>
          </tr>
          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">Vacancy:
              </h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{vacancy}}</p>
            </td>
          </tr>
          <tr>
            <td colspan="2" class="sm-h-32" height="25"></td>
          </tr>
          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">Date
                Posted:</h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{created_at}}</p>
            </td>
          </tr>
          <tr>
            <td colspan="2" class="sm-h-32" height="25"></td>
          </tr>

          <tr>
            <!-- <td class="sm-block sm-w-full sm-pb-16" style="vertical-align: top;" width="16.66667%" valign="top">
              <img src="#" width="54"
                style="border: 0; line-height: 100%; vertical-align: middle;">
            </td> -->
            <td class="sm-block sm-w-full" width="83.33333%">
              <h3 style="line-height: 100%; margin: 0; margin-bottom: 16px; color: #000000; font-size: 28px;">
                Application Deadline:</h3>
              <p style="line-height: 28px; margin: 0; color: #718096; font-size: 18px;">{{deadline}}</p>
            </td>
          </tr>

        </table>
      </td>
    </tr>
  </table>
  <table class="sm-w-full" width="700" cellpadding="0" cellspacing="0" role="presentation">
    <tr>
      <td class="sm-h-32" colspan="2" height="48"></td>
    </tr>
    <tr>
    </tr>
    <tr>
      <td class="sm-px-0 sm-py-32" colspan="2"
        style="padding-left: 48px; padding-right: 48px; padding-top: 64px; padding-bottom: 64px;">
        <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
          <tr>
            <th class="hover-bg-indigo-700"
              style="mso-padding-alt: 24px 48px; border-radius: 4px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);"
              bgcolor="#5A67D8">
              <a href="https://www.devxhub.com/career" class="sm-text-xl cursor-pointer"
                style="display: block; font-weight: 600; line-height: 100%; padding-top: 24px; padding-bottom: 24px; padding-left: 48px; padding-right: 48px; color: #FFFFFF; font-size: 24px; text-decoration: none; white-space: nowrap;">Apply
                Now</a>
            </th>
          </tr>
        </table>
      </td>
    </tr>
  </table>
  </td>
  </tr>
  </table>
</body>
</html>
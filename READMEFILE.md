/**
 * StarRating: A reusable component for displaying star ratings (0-5)
 * Props:
 * - rating (Number): Rating value
 * Usage:
 * <StarRating :rating="50" size="base" />
 * 
 * Common use:
 * - RatingSection
 */

/**
 * RatingSection: Displays Clutch rating with star rating component
 * Props:
 * - isMobile (Boolean): Toggle mobile/desktop layout
 * Usage:
 * <RatingSection :is-mobile="false" />
 * 
 * Common use:
 * - Landing Page
 */

 /**
 * HomeHeroFeatureIcon: Displays Full Transparency, Expert Team, Daily & Weekly Reports, Flexible & On-Demand
 * Props:
 * - isMobile (Boolean): Toggle mobile/desktop layout
 * Usage:
 * <HomeHeroFeatureIcon
 *          v-for="feature in features"
 *          :key="feature.id"
 *         :icon="feature.icon"
 *         :title="feature.title"
 *         size="mobile"
    />
 * 
 * Common use:
 * - Landing Page
 * - About us
 * - ai/ml
 * - it staff
 * - ...
 */
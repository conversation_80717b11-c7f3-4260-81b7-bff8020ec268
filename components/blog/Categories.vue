<script setup lang="ts">
import { useBlogStore } from "~/stores/pages/blog/blog";
import type { Categories } from "~/types/posts";
const { setSearchPost } = useBlogStore();
const route = useRoute();

const { data: categories } = useFetch<Categories>("/api/blogs/categories");

const searchBySlug = (searchText: string) => {
  setSearchPost(searchText);
};
const categoryHeight = ref("0px");
const categoryList = ref(null);
const getCategoryHeight = () => {
  setTimeout(() => {
    if (categoryList.value) {
      categoryHeight.value = categoryList.value.offsetHeight;
    }
  }, 1000);
};
onMounted(() => {
  if (route.fullPath.includes("/blog/?category")) {
    searchBySlug(route.fullPath.replace("/blog/?category=", ""));
  }
  window.addEventListener("resize", getCategoryHeight);
  categoryList.value = document.getElementById("categoryList") as HTMLElement;
  getCategoryHeight();
});
const seeMore = ref<boolean>(false);
</script>

<template>
  <div>
    <div
      class="overflow-hidden"
      :class="seeMore ? 'see_more' : 'less_more'"
      :style="{ '--height': `${categoryHeight + 28 + 24 + 32 + 10}px` }"
    >
      <h1
        class="text-lg sm:text-lg md:text-xl lg:text-[26px] xl:text-[32px] font-bold mt-[32px] sm:mt-[40px] md:mt-[32px]"
      >
        <span class="text-primary border-b-2 border-light-white"
          >Categories</span
        >
      </h1>
      <div
        id="categoryList"
        class="flex flex-wrap whitespace-nowrap mt-[24px] text-light-white transition-all duration-500 ease-in-out"
      >
        <div
          v-for="(category, index) in categories"
          :key="index"
          @click="searchBySlug(category.slug)"
          class="nav-link"
        >
          <NuxtLink
            :to="{
              path: '/blog/',
              query: { category: category?.slug },
            }"
            class="flex justify-center items-center"
          >
            {{ category?.name }}
          </NuxtLink>
        </div>
      </div>
    </div>
    <button class="text-primary" @click="seeMore = !seeMore">
      {{ !seeMore ? "See More" : "Less More" }}
    </button>
  </div>
</template>

<style scoped>
.nav-link {
  @apply text-white bg-button text-sm sm:text-sm md:text-sm lg:text-base xl:text-lg px-2 py-0.5 mr-3 mb-4 flex justify-center items-center rounded-md cursor-pointer;
}
.bg-button {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
}
.see_more {
  /* interpolate-size: allow-keywords; */
  height: var(--height);
  transition: all 0.5s ease-out;
}
.less_more {
  @apply h-[240px] md:h-[240px] lg:h-[260px] xl:h-[275px];
  /* height: 275px; */
  transition: all 0.5s ease-out;
}
</style>

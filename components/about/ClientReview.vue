<script setup>
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { Carousel, Slide } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

const breakpoints = useBreakpoints(breakpointsTailwind);

const isDesktop = breakpoints.greaterOrEqual("lg");
const isTab = breakpoints.greaterOrEqual("md");
const companies = ref([
  {
    companyName: "WashBurn",
    ownerName: "Forhad Hasan",
    image: "/home/<USER>/diago_Duarte.webp",
    position: "IT Manager",
    title: "",
    description:
      "Developer eXperience Hub ( DEVxHUB ) helped the client increase the transaction processing speed by 30%. The client's operational efficiency also improved by 25%. The team was responsive to the client's needs and flexible to changing requirements. They were reliable, proactive, and client-focused.",
  },
  {
    companyName: "Up Brasil",
    ownerName: "Anonymous",
    image: "/home/<USER>/user_profile.svg",
    position: "Former Executive",
    title: "",
    description:
      "Thanks to Devxhub's support, the client's project was developed and made available to end users quickly. The team was highly responsive, supportive, and punctual. Moreover, they communicated clearly via in-person meetings. Their agile process, management, and pricing pleased the client.",
  },
  {
    companyName: "Haas Pro Shipping LLC",
    ownerName: "Hasaan Tammuz",
    image: "/home/<USER>/Fabien_keller.webp",
    position: "CEO",
    title: "",
    description:
      "Developer eXperience Hub ( DEVxHUB ) developed exactly what the client needed. The team delivered the outputs on time and could have finished earlier if the client had more technical knowledge. They also provided the client with video updates and login credentials to track the project's progress.",
  },
  {
    companyName: "Ebb & Flowz",
    ownerName: "Aabriella Devika",
    image: "/home/<USER>/chad_gordon.webp",
    position: "CE0",
    title: "",
    description:
      "Thanks to Developer eXperience Hub ( DEVxHUB ), the client's website had increased traffic and received positive user feedback. The team quickly responded to inquiries and communicated consistently via virtual meetings, email, and messages. Their knowledge and skills impressed the client.",
  },
  {
    companyName: "Now Meta Io",
    ownerName: "Avery Evans",
    image: "/home/<USER>/chad_gordon.webp",
    position: "CTO",
    title: "",
    description:
      "Developer eXperience Hub ( DEVxHUB )'s work has achieved 100,000 user registrations, 10 daily interactions per user, 99.9% server uptime, a 70% retention rate, and a user satisfaction score of 4.5 out of 5. The team showcases technical expertise, a collaborative spirit, and problem-solving skills.",
  },
]);
const myCarousel = ref(null);
const viewPortEnter = () => {
  const sliderUpAnimation = document.querySelectorAll(".slider-up-animation");
  const titleAnimation = document.querySelectorAll(".title-animation");

  const sliderUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  sliderUpAnimation.forEach((item) => {
    sliderUpAnimationObserver.observe(item);
  });

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div>
    <h2 class="title-animation font-bold text-[#f1f1f2] sixth-h-tag">
      <span class="text-[#999]">What </span>
      <span class="instrument-italic">Our Client says</span>
    </h2>

    <div class="mt-8 slider-up-animation">
      <carousel
        ref="myCarousel"
        :autoplay="0"
        :wrap-around="true"
        :items-to-show="isDesktop ? 3 : isTab ? 2 : 1"
        :pauseAutoplayOnHover="true"
        snapAlign="start"
        class=""
      >
        <slide
          class="flex flex-col !justify-start"
          v-for="(company, index) in companies"
          :key="index"
        >
          <div class="w-full lg:max-w-[100%] max-w-[96%] lg:pr-5">
            <div class="w-full">
              <div class="flex relative testimonialCard py-5 px-[30px]">
                <div class="flex flex-col">
                  <img
                    width="40"
                    height="40"
                    src="/public/about-us/invertcomma.svg"
                    alt="invertcomma"
                  />
                  <div class="text-[#1D1A20] mt-[30px] text-left text-lg">
                    <p class="" v-html="company.description"></p>
                  </div>
                  <div class="mt-[50px] flex space-x-2.5 items-center">
                    <img
                      class="rounded-full"
                      width="50"
                      height="50"
                      src="/public/images/assets/img/blank-profile-picture.png"
                      :alt="company.ownerName"
                    />
                    <div class="text-[#1D1A20] font-bold text-left text-lg">
                      <p>{{ company.ownerName }}</p>
                      <p class="font-normal">
                        {{ company.position }}, {{ company.companyName }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </slide>
      </carousel>
    </div>
    <div class="flex justify-between mt-8">
      <div
        class="flex md:flex-row items-center flex-col md:space-x-10 md:space-y-0 space-y-5"
      >
        <div class="flex space-x-5 items-center">
          <div
            class="bg-[#F1F1F2] rounded-[10px] w-[60px] h-[60px] flex justify-center items-center"
          >
            <img
              width="43"
              height="12"
              src="/public/landing/icons/clutch_black.svg"
              alt="clutch"
            />
          </div>
          <div class="flex space-x-2.5 items-center">
            <p class="text-2xl text-[#F1F1F2] font-bold">5.0</p>
            <div class="flex space-x-[5px] text-[#FFD700] text-base">
              <ClientOnly>
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
              </ClientOnly>
            </div>
          </div>
        </div>
        <div class="flex space-x-5 items-center">
          <div
            class="bg-[#F1F1F2] rounded-[10px] w-[60px] h-[60px] flex justify-center items-center"
          >
            <img
              width="32"
              height="23"
              src="/public/landing/icons/up.svg"
              alt="upwork"
            />
          </div>
          <div class="flex space-x-2.5 items-center">
            <p class="text-2xl text-[#F1F1F2] font-bold">5.0</p>
            <div class="flex space-x-[5px] text-[#FFD700] text-base">
              <ClientOnly>
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
                <fa :icon="['fas', 'star']" />
              </ClientOnly>
            </div>
          </div>
        </div>
      </div>
      <div class="hidden md:flex items-center space-x-5">
        <div class="arrow-icon cursor-pointer" @click="myCarousel.prev()">
          <ClientOnly>
            <fa class="text-2xl" :icon="['fas', 'arrow-left']" />
          </ClientOnly>
        </div>
        <div class="arrow-icon cursor-pointer" @click="myCarousel.next()">
          <ClientOnly>
            <fa class="text-2xl" :icon="['fas', 'arrow-right']" />
          </ClientOnly>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.testimonialCard {
  border-radius: 10px;
  background: #f1efec;
}
.title-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.title-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
.slider-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.slider-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
.arrow-icon {
  @apply rounded-full flex justify-center items-center;
  width: 40px;
  height: 40px;
  background-color: white;
  color: black;
  box-shadow: 6px 10px 20px #22283133;
}
@media (max-width: 767px) {
  .arrow-icon {
    width: 40px;
    height: 40px;
  }
}
</style>

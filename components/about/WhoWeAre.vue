<script setup>
const viewPortEnter = () => {
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 2% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div
    class="w-full flex flex-col items-start justify-start md:mt-[140px] mt-[80px]"
  >
    <h2
      class="text-up-animation max-w-[744px] sixth-h-tag font-[700] text-[#F1F1F2]"
    >
      <span class="text-[#999]">Devxhub Empowering Innovation Through </span>
      <span class="instrument-italic">Technology and Expertise</span>
    </h2>
    <div class="mt-8 w-full text-[#999] text-xl">
      <p class="text-up-animation">
        At Devxhub, our core services are
        <span class="text-[#f1f1f2] border-b-[3px] border-[#FFD700]"
          >Team Augmentation</span
        >
        Service and Innovative
        <span class="text-[#f1f1f2] border-b-[3px] border-[#FFD700]"
          >Software Solutions</span
        >. What’s the secret behind our impact and success? It’s the extra
        vetted hiring process, highly skilled expertise, a passion for
        technology, and an unrelenting drive to push boundaries!
      </p>
      <p class="mt-[30px] text-up-animation">
        We place utmost importance on integrity and transparency in every aspect
        of our operations. Clients place their trust in us for clear
        communication, honest collaboration, and delivering results that truly
        matter. This foundation of trust has fueled our growth, fostered
        long-lasting partnerships, and continues to propel us toward enduring
        success.
      </p>
      <NuxtLink
        to="/our-working-process"
        class="text-up-animation w-[180px] h-10 text-base flex justify-center items-center text-[#1D1A20] mt-10 bg-[#FFD700] rounded-full self-start"
        >Working Process</NuxtLink
      >
    </div>
  </div>
</template>

<style lang="scss" scoped>
.text-up-animation {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.text-up-animation.final {
  transform: translateY(0px);
}
</style>

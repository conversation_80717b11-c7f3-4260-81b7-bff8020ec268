<script setup>
const excellenceImages = ref([
  {
    id: 1,
    href: "https://selectedfirms.co/companies/software-development?utm_source=devxhub&utm_medium=badge&utm_campaign=software_development&utm_term=top_software_development_company&utm_content=agency_badge&utm_marketing_tactic=badge_organic",
    src: "/images/about_us/selected_firm.png",
    alt: "Software Development Company",
  },
  {
    id: 2,
    href: "https://www.topdevelopers.co/profile/devxhub-limited?utm_source=devxhub",
    src: "/images/about_us/top_developer.png",
    alt: "Top Developer",
  },
  {
    id: 3,
    href: "https://topfirms.co/companies/software-development/usa",
    src: "/images/about_us/top_firm.png",
    alt: "Software Development Company",
  },
]);

const viewPortEnter = () => {
  const textUpAnimation = document.querySelectorAll(".text-up-animation");
  // const selected_firm =
  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 2% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>
<template>
  <div
    class="w-full md:mt-[180px] mt-[80px] flex flex-col items-start text-center"
  >
    <h2 class="text-up-animation w-full sixth-h-tag font-[700] text-[#F1F1F2]">
      <span class="text-[#999]">Excellence </span>
      <span class="instrument-italic">Sets Our Standard</span>
    </h2>
    <div class="my-[32px] text-[#999] text-xl max-w-[1112px] mx-auto">
      <p class="text-up-animation text-center">
        Excellency the foundation upon which we build innovative solutions and
        exceed expectations
      </p>
    </div>
    <div class="mt-10 max-w-[680px] mx-auto w-full">
      <div
        class="flex md:flex-row flex-col gap-6 lg:gap-10 items-center justify-center w-full"
      >
        <div
          v-for="image in excellenceImages"
          :key="image.id"
          class="w-36 h-36 sm:h-48 sm:w-48 xl:w-60 xl:h-60"
        >
          <a class="w-full h-full" :href="image.href" target="_blank">
            <img
              class="w-full h-full rounded-full object-cover"
              :src="image.src"
              :alt="image.alt"
            />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-up-animation {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.text-up-animation.final {
  transform: translateY(0px);
}
</style>

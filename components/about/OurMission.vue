<script setup>
const viewPortEnter = () => {
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 2% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>
<template>
  <div class="w-full md:mt-[180px] mt-[80px] flex flex-col items-start">
    <h2
      class="text-up-animation w-full text-left sixth-h-tag font-[700] text-[#F1F1F2]"
    >
      <p class="text-[#999]">Partnering</p>
      <p class="instrument-italic">Through Success</p>
    </h2>
    <div class="mt-[32px] text-[#999] text-xl max-w-[1112px]">
      <p class="text-up-animation">
        We partner with Fortune 500 companies, early-stage startups, and
        AI-driven ventures, offering expertise across diverse industries. At
        Devxhub, we are dedicated to turning clients' visions into reality
        through strategic technological innovation, ensuring scalability,
        security, and exceptional user experiences.
      </p>
    </div>
    <div class="mt-10 flex items-center justify-start text-[#999] w-full">
      <div
        class="flex md:flex-row flex-col md:space-y-0 space-y-[20px] lg:space-x-[65px] md:space-x-[30px] text-center items-center justify-between w-full"
      >
        <!-- First Stat -->
        <div class="flex items-center space-x-2">
          <span
            class="xl:text-[72px] lg:text-[48px] text-[38px] font-bold text-gradient"
            >200+</span
          >
          <span class="xl:text-2xl lg:text-xl text-lg mt-[50px]">Business</span>
        </div>

        <!-- Divider -->
        <div
          class="md:h-[42px] md:w-[1px] w-full h-[1px] md:bg-[#999] bg-[#ffffff1a]"
        ></div>

        <!-- Second Stat -->
        <div class="flex items-center space-x-2 !mt-0">
          <div class="mt-[50px]">
            <span class="block xl:text-2xl lg:text-xl text-lg">With</span>
          </div>
          <span
            class="xl:text-[72px] lg:text-[48px] text-[38px] font-bold text-gradient"
            >0%</span
          >
          <div class="mt-[50px]">
            <span class="block xl:text-2xl lg:text-xl text-lg">turnover</span>
          </div>
        </div>

        <!-- Divider -->
        <div
          class="md:h-[42px] md:w-[1px] w-full h-[1px] md:bg-[#999] bg-[#ffffff1a]"
        ></div>

        <!-- Third Stat -->
        <div class="flex items-center space-x-2 !mt-0">
          <span
            class="xl:text-[72px] lg:text-[48px] text-[38px] font-bold text-gradient"
            >Millions</span
          >
          <span class="xl:text-2xl lg:text-xl text-lg mt-[50px]">Valued</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-gradient {
  background: linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-up-animation {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.text-up-animation.final {
  transform: translateY(0px);
}
</style>

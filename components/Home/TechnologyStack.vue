<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import BaseIconHtml from "~/components/base/icon/Html.vue";
import BaseIconCss3 from "~/components/base/icon/Css3.vue";
import BaseIconTailwind from "~/components/base/icon/Tailwind.vue";
import BaseIconBootstrap from "~/components/base/icon/Bootstrap.vue";
import BaseIconJavascript from "~/components/base/icon/JS.vue";
import BaseIconTypescript from "~/components/base/icon/Typescript.vue";
import BaseIconReactJs from "~/components/base/icon/ReactJs.vue";
import BaseIconVueJs from "~/components/base/icon/VueJs.vue";
import BaseIconAngularJs from "~/components/base/icon/AngularJs.vue";
import BaseIconEmberJs from "~/components/base/icon/EmberJs.vue";
import BaseIconSvelte from "~/components/base/icon/Svelte.vue";
import BaseIconPhp from "~/components/base/icon/Php.vue";
import BaseIconPython from "~/components/base/icon/Python.vue";
import BaseIconNodeJs from "~/components/base/icon/NodeJs.vue";
import BaseIconLaravel from "~/components/base/icon/Laravel.vue";
import BaseIcondJango from "~/components/base/icon/Django.vue";
import BaseIconFlask from "~/components/base/icon/Flask.vue";
import BaseIconLumen from "~/components/base/icon/Lumen.vue";
import BaseIconAspNet from "~/components/base/icon/AspNet.vue";
import BaseIconSpring from "~/components/base/icon/Spring.vue";
import BaseIconSql from "~/components/base/icon/Sql.vue";
import BaseIconMySql from "~/components/base/icon/MySql.vue";
import BaseIconSqlite from "~/components/base/icon/Sqlite.vue";
import BaseIconPostgreSql from "~/components/base/icon/PostgreSql.vue";
import BaseIconMariaDb from "~/components/base/icon/MariaDb.vue";
import BaseIconMongoDb from "~/components/base/icon/MongoDb.vue";
import BaseIconRedis from "~/components/base/icon/Redis.vue";
import BaseIconAmazonAurora from "~/components/base/icon/AmazonAurora.vue";
import BaseIconMiscrosoftSql from "~/components/base/icon/MiscrosoftSql.vue";
import BaseIconAmazonDynamoDb from "~/components/base/icon/AmazonDynamoDb.vue";
import BaseIconAws from "~/components/base/icon/Aws.vue";
import BaseIconAmazonDigitalOcean from "~/components/base/icon/DigitalOcean.vue";
import BaseIconGoogleCloud from "~/components/base/icon/GoogleCloud.vue";
import BaseIconHeroku from "~/components/base/icon/Heroku.vue";
import BaseIconNetlify from "~/components/base/icon/Netlify.vue";
import BaseIconLinux from "~/components/base/icon/Linux.vue";
import BaseIconShared from "~/components/base/icon/Shared.vue";
import BaseIconDedicated from "~/components/base/icon/Dedicated.vue";
import BaseIconVps from "~/components/base/icon/Vps.vue";
import BaseIconDocker from "~/components/base/icon/Docker.vue";
import BaseIconJenkins from "~/components/base/icon/Jenkins.vue";
import BaseIconAnsible from "~/components/base/icon/Ansible.vue";
import BaseIconKubernates from "~/components/base/icon/Kubernates.vue";
import BaseIconGit from "~/components/base/icon/Git.vue";
import BaseIconGitHub from "~/components/base/icon/GitHub.vue";
import BaseIconGitLab from "~/components/base/icon/GitLab.vue";
import BaseIconBitbucket from "~/components/base/icon/Bitbucket.vue";
import BaseIconSlack from "~/components/base/icon/Slack.vue";
import BaseIconMicrosoftTeam from "~/components/base/icon/MicrosoftTeam.vue";
import BaseIconYouTrack from "~/components/base/icon/YouTrack.vue";
import BaseIconTrello from "~/components/base/icon/Trello.vue";
import BaseIconJira from "~/components/base/icon/Jira.vue";
import BaseIconAsana from "~/components/base/icon/Asana.vue";
import BaseIconApi from "~/components/base/icon/Api.vue";
import BaseIconGraphQlApi from "~/components/base/icon/GraphQlApi.vue";
import BaseIconFlutter from "~/components/base/icon/Flutter.vue";
import BaseIconDart from "~/components/base/icon/Dart.vue";
import BaseIconKotlin from "~/components/base/icon/Kotlin.vue";
import BaseIconReactNative from "~/components/base/icon/ReactNative.vue";
import BaseIconSwift from "~/components/base/icon/Swift.vue";
import BaseIconObjectiveC from "~/components/base/icon/ObjectiveC.vue";
import BaseIconAzure from "~/components/base/icon/Azure.vue";
import BaseIconWindowsServer from "~/components/base/icon/WindowsServer.vue";
import BaseIconTerraform from "~/components/base/icon/Terraform.vue";
import BaseIconCICD from "~/components/base/icon/CICD.vue";
import BaseIconRestfulApi from "~/components/base/icon/RestfulApi.vue";
import BaseIconSOAP from "~/components/base/icon/SOAP.vue";
import BaseIconGraphQL from "~/components/base/icon/GraphQL.vue";
import BaseIconGRPC from "~/components/base/icon/GRPC.vue";
import BaseIconAI from "~/components/base/icon/AI.vue";
// import BaseIconOdoo from "~/components/base/icon/Odoo.vue";
// import BaseIconWordPress from "~/components/base/icon/WordPress.vue";
import BaseIconChatGPT from "~/components/base/icon/ChatGPT.vue";
import BaseIconSQA from "~/components/base/icon/SQA.vue";
import BaseIconSEOFriendly from "~/components/base/icon/SEOFriendly.vue";
import BaseIconPixelPerfect from "~/components/base/icon/PixelPerfect.vue";
import BaseIconCleanCode from "~/components/base/icon/CleanCode.vue";
import BaseIconOptimized from "~/components/base/icon/Optimized.vue";
import BaseIconHardenSecurity from "~/components/base/icon/HardenSecurity.vue";
import BaseIconNextJs from "~/components/base/icon/NextJs.vue";
import BaseIconNuxtJs from "~/components/base/icon/NuxtJs.vue";
import BaseIconBackboneJs from "~/components/base/icon/BackboneJs.vue";
import BaseIconJQuery from "~/components/base/icon/JQuery.vue";
import BaseIconBulma from "~/components/base/icon/Bulma.vue";
import BaseIconAlpineJs from "~/components/base/icon/AlpineJs.vue";
// import BaseIconRuby from "~/components/base/icon/Ruby.vue"
import BaseIconGo from "~/components/base/icon/Go.vue";
import BaseIconRust from "~/components/base/icon/Rust.vue";
import BaseIconScala from "~/components/base/icon/Scala.vue";
import BaseIconOracle from "~/components/base/icon/Oracle.vue";
import BaseIconMachineLearning from "~/components/base/icon/MachineLearning.vue";
import BaseIconOpenAl from "~/components/base/icon/OpenAl.vue";
// import { TechnologyStack, Technologies } from "~/types/post";
// const { data: TechnologyStacks } = useFetch<TechnologyStack[]>(
//   "/api/posts/technologyStack"
// );
// const { data: Technologies } = useFetch<Technologies[]>(
//   "/api/posts/technologies"
// );

// breakpoints
const breakpoints = useBreakpoints(breakpointsTailwind);
const isDesktop = breakpoints.greaterOrEqual("lg");
// const techStack = TechnologyStacks.value;
// const techCard = Technologies.value;
const techStack = ref([
  {
    id: 1,
    name: "Frontend",
    stack: "Frontend",
  },
  {
    id: 2,
    name: "Backend",
    stack: "Backend",
  },
  {
    id: 3,
    name: "Database",
    stack: "Database",
  },
  {
    id: 4,
    name: "APIs",
    stack: "APIs",
  },
  {
    id: 5,
    name: "Mobile Apps",
    stack: "Mobile App",
  },
  {
    id: 6,
    name: "DevOps",
    stack: "DevOps",
  },
  {
    id: 7,
    name: "Automation",
    stack: "Automation",
  },

  {
    id: 8,
    name: "AI/ML",
    stack: "AI/ML",
  },
]);
const techCard = ref([
  {
    name: "HTML",
    image: BaseIconHtml,
    stack: "Frontend",
  },
  {
    name: "CSS3",
    image: BaseIconCss3,
    stack: "Frontend",
  },
  {
    name: "Tailwind",
    image: BaseIconTailwind,
    stack: "Frontend",
  },
  {
    name: "Bootstrap",
    image: BaseIconBootstrap,
    stack: "Frontend",
  },
  {
    name: "JavaScript",
    image: BaseIconJavascript,
    stack: "Frontend",
  },
  {
    name: "Typescript",
    image: BaseIconTypescript,
    stack: "Frontend",
  },
  {
    name: "jQuery",
    image: BaseIconJQuery,
    stack: "Frontend",
  },
  {
    name: "ReactJS",
    image: BaseIconReactJs,
    stack: "Frontend",
  },
  {
    name: "Next.js",
    image: BaseIconNextJs,
    stack: "Frontend",
  },
  {
    name: "VueJS",
    image: BaseIconVueJs,
    stack: "Frontend",
  },
  {
    name: "Nuxt.js",
    image: BaseIconNuxtJs,
    stack: "Frontend",
  },
  {
    name: "AngularJS",
    image: BaseIconAngularJs,
    stack: "Frontend",
  },
  {
    name: "Backbone.js",
    image: BaseIconBackboneJs,
    stack: "Frontend",
  },
  {
    name: "EmberJS",
    image: BaseIconEmberJs,
    stack: "Frontend",
  },
  {
    name: "Svelte",
    image: BaseIconSvelte,
    stack: "Frontend",
  },
  {
    name: "Bulma",
    image: BaseIconBulma,
    stack: "Frontend",
  },
  {
    name: "Alpine.js",
    image: BaseIconAlpineJs,
    stack: "Frontend",
  },
  {
    name: "PHP",
    image: BaseIconPhp,
    stack: "Backend",
  },
  {
    name: "Python",
    image: BaseIconPython,
    stack: "Backend",
  },
  {
    name: "NodeJS",
    image: BaseIconNodeJs,
    stack: "Backend",
  },
  {
    name: "Laravel",
    image: BaseIconLaravel,
    stack: "Backend",
  },
  {
    name: "Django",
    image: BaseIcondJango,
    stack: "Backend",
  },
  {
    name: "Flask",
    image: BaseIconFlask,
    stack: "Backend",
  },
  {
    name: "Lumen",
    image: BaseIconLumen,
    stack: "Backend",
  },
  {
    name: "C# [ASP.NET]",
    image: BaseIconAspNet,
    stack: "Backend",
  },
  {
    name: "Java [Spring Boot]",
    image: BaseIconSpring,
    stack: "Backend",
  },
  // {
  //   name: "Ruby",
  //   image: BaseIconRuby,
  //   stack: "Backend",
  // },
  {
    name: "Go",
    image: BaseIconGo,
    stack: "Backend",
  },
  {
    name: "Rust",
    image: BaseIconRust,
    stack: "Backend",
  },
  {
    name: "Scala",
    image: BaseIconScala,
    stack: "Backend",
  },
  {
    name: "Kotlin",
    image: BaseIconKotlin,
    stack: "Backend",
  },
  {
    name: "Restful",
    image: BaseIconRestfulApi,
    stack: "APIs",
  },
  {
    name: "GraphQL",
    image: BaseIconGraphQL,
    stack: "APIs",
  },
  {
    name: "OpenAI API",
    image: BaseIconOpenAl,
    stack: "APIs",
  },
  {
    name: "SOAP",
    image: BaseIconSOAP,
    stack: "APIs",
  },
  {
    name: "gRPC",
    image: BaseIconGRPC,
    stack: "APIs",
  },
  {
    name: "Flutter",
    image: BaseIconFlutter,
    stack: "Mobile App",
  },
  {
    name: "Dart",
    image: BaseIconDart,
    stack: "Mobile App",
  },
  // {
  //   name: "Kotlin",
  //   image: BaseIconKotlin,
  //   stack: "Mobile App",
  // },
  {
    name: "React Native",
    image: BaseIconReactNative,
    stack: "Mobile App",
  },
  {
    name: "Swift",
    image: BaseIconSwift,
    stack: "Mobile App",
  },
  {
    name: "Objective-C",
    image: BaseIconObjectiveC,
    stack: "Mobile App",
  },
  {
    name: "SQL",
    image: BaseIconSql,
    stack: "Database",
  },
  {
    name: "MySQL",
    image: BaseIconMySql,
    stack: "Database",
  },
  {
    name: "SQLite",
    image: BaseIconSqlite,
    stack: "Database",
  },
  {
    name: "PostgreSQL",
    image: BaseIconPostgreSql,
    stack: "Database",
  },
  {
    name: "MariaDB",
    image: BaseIconMariaDb,
    stack: "Database",
  },
  {
    name: "MongoDb",
    image: BaseIconMongoDb,
    stack: "Database",
  },
  {
    name: "Redis",
    image: BaseIconRedis,
    stack: "Database",
  },
  {
    name: "Oracle",
    image: BaseIconOracle,
    stack: "Database",
  },
  {
    name: "Amazon Aurora",
    image: BaseIconAmazonAurora,
    stack: "Database",
  },
  {
    name: "Miscrosoft SQL Server",
    image: BaseIconMiscrosoftSql,
    stack: "Database",
  },
  {
    name: "Amazon DynamoDB",
    image: BaseIconAmazonDynamoDb,
    stack: "Database",
  },
  {
    name: "AWS",
    image: BaseIconAws,
    stack: "DevOps",
  },
  {
    name: "Digital Ocean",
    image: BaseIconAmazonDigitalOcean,
    stack: "DevOps",
  },
  {
    name: "Google Cloud",
    image: BaseIconGoogleCloud,
    stack: "DevOps",
  },
  {
    name: "Azure",
    image: BaseIconAzure,
    stack: "DevOps",
  },
  {
    name: "Windows Server",
    image: BaseIconWindowsServer,
    stack: "DevOps",
  },
  {
    name: "Heroku",
    image: BaseIconHeroku,
    stack: "DevOps",
  },
  {
    name: "Netlify",
    image: BaseIconNetlify,
    stack: "DevOps",
  },
  {
    name: "Linux",
    image: BaseIconLinux,
    stack: "DevOps",
  },
  {
    name: "Shared Hosting",
    image: BaseIconShared,
    stack: "DevOps",
  },
  {
    name: "Dedicated server",
    image: BaseIconDedicated,
    stack: "DevOps",
  },
  {
    name: "VPS Server and hosting",
    image: BaseIconVps,
    stack: "DevOps",
  },
  {
    name: "Docker",
    image: BaseIconDocker,
    stack: "Automation",
  },
  {
    name: "Jenkins",
    image: BaseIconJenkins,
    stack: "Automation",
  },
  {
    name: "Ansible",
    image: BaseIconAnsible,
    stack: "Automation",
  },
  {
    name: "Kubernates",
    image: BaseIconKubernates,
    stack: "Automation",
  },
  {
    name: "Git",
    image: BaseIconGit,
    stack: "Automation",
  },
  {
    name: "GitHub",
    image: BaseIconGitHub,
    stack: "Automation",
  },
  {
    name: "GitLab",
    image: BaseIconGitLab,
    stack: "Automation",
  },
  {
    name: "Bitbucket",
    image: BaseIconBitbucket,
    stack: "Automation",
  },
  {
    name: "CI/CD",
    image: BaseIconCICD,
    stack: "Automation",
  },
  {
    name: "Terraform",
    image: BaseIconTerraform,
    stack: "Automation",
  },
  {
    name: "Slack",
    image: BaseIconSlack,
    stack: "Project Managemenet",
  },
  {
    name: "Microsoft Team",
    image: BaseIconMicrosoftTeam,
    stack: "Project Managemenet",
  },
  {
    name: "YouTrack",
    image: BaseIconYouTrack,
    stack: "Project Managemenet",
  },
  {
    name: "Trello",
    image: BaseIconTrello,
    stack: "Project Managemenet",
  },
  {
    name: "Jira",
    image: BaseIconJira,
    stack: "Project Managemenet",
  },
  {
    name: "Asana",
    image: BaseIconAsana,
    stack: "Project Managemenet",
  },
  // {
  //   name: "ML/AI",
  //   image: BaseIconAI,
  //   stack: "AI/ML",
  // },
  // {
  //   name: "Odoo",
  //   image: BaseIconOdoo,
  //   stack: "AI/ML",
  // },
  // {
  //   name: "WordPress",
  //   image: BaseIconWordPress,
  //   stack: "AI/ML",
  // },
  {
    name: "Artificial Intelligence",
    image: BaseIconAI,
    stack: "AI/ML",
  },
  {
    name: "Machine learning",
    image: BaseIconMachineLearning,
    stack: "AI/ML",
  },
  {
    name: "OpenAL API Implementation",
    image: BaseIconOpenAl,
    stack: "AI/ML",
  },
  {
    name: "ChatGPT Implementation",
    image: BaseIconChatGPT,
    stack: "AI/ML",
  },

  {
    name: "SQA Automation",
    image: BaseIconSQA,
    stack: "Automation",
  },
  {
    name: "SEO-Friendly",
    image: BaseIconSEOFriendly,
    stack: "More",
  },
  {
    name: "Pixel-Perfect Design",
    image: BaseIconPixelPerfect,
    stack: "More",
  },
  {
    name: "Clean Code",
    image: BaseIconCleanCode,
    stack: "More",
  },
  {
    name: "100% Optimized",
    image: BaseIconOptimized,
    stack: "More",
  },
  {
    name: "Harden Security",
    image: BaseIconHardenSecurity,
    stack: "More",
  },
]);

const activeStack = ref("Frontend");
const activeStackDemo = ref("Frontend");
const stackClickCount = ref(0);
const activeTrue = ref(false);
const handleActiveStack = (selectedStack: string) => {
  stackClickCount.value++;
  setTimeout(() => {
    stackClickCount.value = 0;
  }, 300);

  if (window.innerWidth < 1024 && activeStackDemo.value === selectedStack) {
    if (
      view.value &&
      activeStack.value === selectedStack &&
      stackClickCount.value === 2 &&
      !activeTrue.value
    ) {
      view.value = false;
      start.value = 0;
      end.value = 6;
      activeStack.value = "";
      setTimeout(() => {
        activeStackDemo.value = "";
      }, 480);
    } else if (!view.value) {
      activeTrue.value = true;
      setTimeout(() => {
        activeTrue.value = false;
      }, 500);

      view.value = true;
      start.value = 0;
      end.value = 6;
      activeStack.value = selectedStack;
      setTimeout(() => {
        activeStackDemo.value = selectedStack;
      }, 480);
    }
  } else if (activeStackDemo.value !== selectedStack) {
    view.value = false;
    setTimeout(() => {
      view.value = true;
    }, 450);
    start.value = 0;
    end.value = 6;
    activeStack.value = selectedStack;
    setTimeout(() => {
      activeStackDemo.value = selectedStack;
    }, 480);
  } else if (
    window.innerWidth >= 1024 &&
    activeStackDemo.value === selectedStack
  ) {
    view.value = false;
    setTimeout(() => {
      view.value = true;
    }, 450);
    start.value = 0;
    end.value = 6;
    activeStack.value = selectedStack;
    setTimeout(() => {
      activeStackDemo.value = selectedStack;
    }, 480);
  }
};
// data
const totalCardLength = ref(0);
const finalTeckCard = computed(() => {
  const tempArray = techCard.value.filter((item) => {
    if (item.stack === activeStack.value) {
      return item;
    }
    // else if (item.stack === activeStack.value === "FullStack") {
    //   return item;
    // }
  });

  totalCardLength.value = tempArray.length;
  return tempArray;
  // return tempArray.slice(start.value, end.value);
});
const start = ref(0);
const end = ref(6);
const view = ref(true);
const browserName = ref<string>("chrome");
const navbar = ref<string>("");
const leftArrow = ref<boolean>(false);
const rightArrow = ref<boolean>(false);
const getscrollPosition = () => {
  if (navbar.value.scrollLeft === 0) {
    console.log("At the beginning");
    leftArrow.value = false;
    rightArrow.value = true;
  }
  // Check if at the end
  else if (
    navbar.value.scrollLeft + navbar.value.clientWidth >=
    navbar.value.scrollWidth
  ) {
    console.log("At the end");
    rightArrow.value = false;
    leftArrow.value = true;
  } else {
    leftArrow.value = true;
    rightArrow.value = true;
    console.log("At the middle");
  }
};
const changeScrollPosition = (value) => {
  if (navbar.value.scrollLeft === 0) {
    navbar.value.scrollLeft = navbar.value.scrollLeft + Number(value);
  } else if (
    navbar.value.scrollLeft + navbar.value.clientWidth >=
    navbar.value.scrollWidth
  ) {
    navbar.value.scrollLeft = navbar.value.scrollLeft + Number(value);
  } else {
    navbar.value.scrollLeft = navbar.value.scrollLeft + Number(value);
  }
};
onMounted(() => {
  const userAgent = navigator.userAgent;
  if (userAgent.match(/firefox|fxios/i)) {
    browserName.value = "firefox";
  } else {
    browserName.value = "chrome";
  }
  navbar.value = document.getElementById("technology-navbar");
  console.log(navbar.value, "navbar");
  if (navbar.value) {
    navbar.value.addEventListener("scroll", getscrollPosition);
    getscrollPosition();
  }
});
</script>

<template>
  <div id="technology-stacks" class="">
    <div class="px-0 md:px-4 lg:px-0">
      <h2 class="text-center text-[#FDB21D] md:text-5xl text-3xl font-semibold">
        Technology Stacks
      </h2>
      <div
        class="technologyStackDiv flex flex-col relative items-center w-full md:mt-14 lgx:mt-16 mt-8"
      >
        <div
          class="min-[1450px]:hidden h-9 w-26 min-w-26 !mr-0 items-center cursor-pointer left_arrow_container absolute top-2.5 z-[1] left-0"
          :class="leftArrow ? 'flex' : 'hidden'"
          @click.stop="changeScrollPosition('-100')"
        >
          <ClientOnly>
            <fa
              :icon="['fas', 'angle-right']"
              class="w-2.5 h-4 pr-2 font-bold text-[#FDB21D] transform rotate-180"
            />
          </ClientOnly>
        </div>
        <div
          class="min-[1450px]:hidden h-9 w-26 min-w-26 !ml-0 cursor-pointer self-end right_arrow_container justify-end flex items-center absolute top-2.5 z-[1] right-0"
          :class="rightArrow ? 'flex' : 'hidden'"
          @click.stop="changeScrollPosition(100)"
        >
          <ClientOnly>
            <fa
              :icon="['fas', 'angle-right']"
              class="w-2.5 h-4 pr-2 font-bold text-[#FDB21D]"
            />
          </ClientOnly>
        </div>
        <div
          id="technology-navbar"
          class="w-full relative border-b border-[#E3E3E3] mb-7"
          :class="browserName === 'firefox' ? 'firefox-scroll' : 'scroll'"
        >
          <div
            class="flex items-center space-x-8 md:space-x-14 min-[1450px]:justify-around justify-start font-bold w-full mt-4"
          >
            <ClientOnly>
              <template v-for="(tech, index) in techStack" :key="index">
                <div
                  class="cursor-pointer relative text-left whitespace-nowrap pb-[18px] text-lg xl:text-xl 2xl:text-[22px]"
                  :class="[
                    tech.stack === activeStack
                      ? 'text-primary'
                      : 'text-[#F0F0F0]',
                    index === 0 ? '!ml-0' : '',
                  ]"
                  @click="handleActiveStack(tech.stack)"
                >
                  {{ tech.name }}
                  <div
                    v-if="tech.stack === activeStack"
                    class="w-full h-1 absolute bottom-0 rounded-[13px]"
                    :class="
                      tech.stack === activeStack
                        ? 'bg-primary'
                        : ' bg-transparent'
                    "
                  ></div>
                </div>
              </template>
            </ClientOnly>
          </div>
        </div>
        <ClientOnly>
          <div class="flex-grow">
            <div class="flex flex-wrap justify-center">
              <TransitionGroup name="inout">
                <!-- xl:h-[254px] xl:w-[188px] sm:w-[168px] w-[134px] sm:h-[234px] h-[300px] -->
                <template v-for="(techSingle, index) in finalTeckCard">
                  <div
                    v-if="view"
                    :key="index"
                    class="techCard flex flex-col space-y-8 justify-center xl:p-8 p-4 sm:mx-4 mr-4 !my-4"
                    :class="
                      techSingle.stack === 'FullStack'
                        ? 'w-full h-full'
                        : 'lg:w-[200px] w-[137px] min-h-[132px]'
                    "
                  >
                    <div
                      v-if="techSingle.image"
                      class="cardInnerIconBg flex justify-center items-center"
                    >
                      <div
                        class="w-full h-full flex justify-center bg-transparent"
                      >
                        <!-- <img
                          :src="techSingle.image"
                          alt=""
                          srcset=""
                          class="max-h-[90px]"
                        /> -->
                        <component
                          class="shadow-md"
                          :is="techSingle.image"
                        ></component>
                      </div>
                    </div>
                    <div
                      v-if="techSingle.stack !== 'FullStack'"
                      class="text-center font-bold text-[#fff] text-lg xl:text-xl 2xl:text-[22px]"
                    >
                      {{ techSingle.name }}
                    </div>
                    <div
                      v-else
                      class="text-left text-white font-normal text-base px-4 leading-10"
                      v-html="techSingle.name"
                    ></div>
                  </div>
                </template>
              </TransitionGroup>
            </div>
          </div>
        </ClientOnly>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.inout-enter-active,
.inout-leave-active {
  transition: all 0.5s ease-in-out;
}
.inout-enter-from,
.inout-leave-to {
  opacity: 0;
  scale: 0.5;
}
// .inout-enter-active {
//   transition-delay: 0.5s;
// }
.techCard {
  // background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  // background-color: #fafafa;
  // box-shadow: 0px 16px 42px 0px rgba(0, 30, 108, 0.3);
  // border: 0.5px solid #fdb21d;
  // border-radius: 16px;
}
.cardInnerIconBg {
  // xl:w-[120px] xl:h-[120px] w-[110px] h-[110px]
  @apply w-[54px] h-[54px]  rounded-full mx-auto bg-transparent;
  // background: linear-gradient(
  //   180deg,
  //   rgba(255, 255, 255, 0.6) 0%,
  //   #8cd5b7 100%
  // );
}
.takeCareSingleCard {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  border: 0.5px solid #fdb21d;
  box-shadow: 0px 16px 42px rgba(0, 30, 108, 0.3);
  border-radius: 30px;
}
.scroll {
  overflow-y: hidden;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */

  /* width */
  &::-webkit-scrollbar {
    display: none;
    height: 4px;
  }
  /* Track */
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    background: #e3e3e3;
    border-radius: 10px; /* Increased for better rounding effect */
  }
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #fdb21d;
    border-radius: 10px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #fdb21d;
  }
}
.firefox-scroll {
  overflow-y: hidden;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #fdb21d #e3e3e3; /* Firefox 64 */

  /* width */
  &::-webkit-scrollbar {
    display: none;
    height: 4px;
  }
  /* Track */
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    background: #e3e3e3;
    border-radius: 10px; /* Increased for better rounding effect */
  }
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #fdb21d;
    border-radius: 10px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #fdb21d;
  }
}
.left_arrow_container {
  background: linear-gradient(
    270deg,
    rgba(42, 30, 86, 0) 3.04%,
    rgba(42, 30, 86, 0.9) 48.28%,
    #2a1e56 91.74%
  );
}
.right_arrow_container {
  background: linear-gradient(
    270deg,
    #2a1e56 3.04%,
    rgba(42, 30, 86, 0.9) 48.28%,
    rgba(42, 30, 86, 0) 91.74%
  );
}
</style>

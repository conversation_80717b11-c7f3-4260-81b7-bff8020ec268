<script setup lang="ts">
import { useVuelidate } from "@vuelidate/core";
import { email, maxLength, required } from "@vuelidate/validators";
import InputPhoneNumberInput from "~/components/base/input/PhoneNumberInput.vue";
const config = useRuntimeConfig();
const showGreetings = ref<boolean>(false);
const recaptchaApiKey = config.public.recaptchaApiKey;
useHead({
  script: [
    {
      src: `https://www.google.com/recaptcha/enterprise.js?render=${recaptchaApiKey}`,
      async: true,
      defer: true,
    },
  ],
});

const { $toast } = useNuxtApp();
// types
interface ContactForm {
  name: string;
  email: string;
  phone: string;
  subject: string;
  question: string;
  color: string;
  opacity: number;
}
const contactForm = ref<ContactForm>({
  name: "",
  email: "",
  question: "",
});
const isProcessing = ref<boolean>(false);
const phone = ref<string>("");
const phoneerror = ref<boolean>(true);
const phoneNumberRef = ref<HTMLElement | null>(null);
const rules = {
  name: {
    maxLength: maxLength(40),
  },
  email: {
    required,
    email,
  },
  // subject: {
  //   required,
  //   maxLength: maxLength(40),
  // },
  question: {
    required,
    maxLength: maxLength(500),
  },
};
const v$ = useVuelidate(rules, contactForm);
const route = useRoute();
const getError = ($event) => {
  console.log("$event", $event);
  if ($event) {
    phoneerror.value = $event.error;
    if ($event.phone) {
      phone.value = $event.phone;
    }
    // this.userPartner.phone = this.phone
  }
};

const submitForm = async () => {
  if (phoneNumberRef.value.phone === "") {
    phoneNumberRef.value.error = true;
  }
  v$.value.$touch();
  $toast("clear");

  if (!v$.value.$invalid && !phoneerror.value) {
    isProcessing.value = true;
    grecaptcha.enterprise.ready(async () => {
      try {
        const token = await grecaptcha.enterprise.execute(recaptchaApiKey, {
          action: "LOGIN",
        });

        if (!token) {
          $toast("error", {
            message: "reCAPTCHA verification failed.",
            className: "alert_error",
          });
          isProcessing.value = false;
          return;
        }

        const finalPhone = phone.value.replace(/\s+/g, "");
        const submitToAPI = await $fetch("/api/posts/contacts", {
          method: "POST",
          body: JSON.stringify({
            full_name: contactForm.value.name,
            email: contactForm.value.email,
            phone: finalPhone,
            project_details: contactForm.value.question,
            recaptchaToken: token,
          }),
        });
        console.log(submitToAPI);
        if (submitToAPI) {
          showGreetings.value = true;
          // $toast("success", {
          //   message: submitToAPI.message,
          //   className: "alert_success",
          // });
        } else {
          $toast("error", {
            message: "Please fill the required fields.",
            className: "alert_error",
          });
        }
      } catch (error) {
        $toast("error", {
          message: error,
          className: "alert_error",
        });
      } finally {
        isProcessing.value = false;
      }
    });
  }
};
</script>
<template>
  <div class="bg-[#1A1139] md:pt-[150px] pt-[80px]">
    <div class="container-fluid mx-auto">
      <div
        class="w-full flex xl:flex-row flex-col xl:space-x-[71px] xl:space-y-0 md:space-y-[100px] space-y-8 items-center"
        :class="
          $route.name === 'index'
            ? 'justify-center'
            : 'max-w-[1480px] justify-between'
        "
      >
        <div>
          <h2
            class="text-[#F1F1F2] md:text-[64px] text-[36px] md:leading-[76.8px] leading-[100%] font-bold hidden md:block"
          >
            Have a <span class="instrument-italic">great</span> idea?
          </h2>
          <h2
            class="text-[#F1F1F2] text-[36px] leading-[100%] font-bold md:hidden"
          >
            Have a <span class="instrument-italic">great</span> <br />
            idea?
          </h2>
          <ul class="mt-10 flex flex-col space-y-4">
            <!-- <li class="flex items-start space-x-[10px]">
            <img
              class="mt-2"
              src="/landing/icons/polygon_1.svg"
              alt="Polygon Icon"
            />
            <p class="text-[#999] text-2xl">
              We will respond to you within 24 hours.
            </p>
          </li>
          <li class="flex items-start space-x-[10px]">
            <img
              class="mt-2"
              src="/landing/icons/polygon_1.svg"
              alt="Polygon Icon"
            />
            <p class="text-[#999] text-2xl">We’ll sign an NDA if requested.</p>
          </li> -->
            <li class="flex items-start space-x-[10px]">
              <img
                class="mt-2"
                src="/landing/icons/polygon_1.svg"
                alt="Polygon Icon"
              />
              <p
                class="text-[#999] md:text-2xl text-[20px] leading-[28px] md:leading-[32px]"
              >
                We’ll assign a dedicated manager for you.
              </p>
            </li>
          </ul>
          <div class="md:mt-[70px] mt-8">
            <p
              class="md:text-2xl font-medium text-[#FFD700] text-[20px] leading-[28px] md:leading-[32px]"
            >
              Inquiries
            </p>
            <p class="text-[#999] text-2xl mt-2.5"><EMAIL></p>
          </div>
        </div>
        <div class="lg:w-[737px] md:w-[637px] w-full">
          <!-- <iframe
          class="form rounded-[10px]"
          src="https://share.hsforms.com/1U9RBw6UPRSiATpr8lV6EgAqpg9i"
          frameborder="0"
          title="Hubspot Contact Form"
        ></iframe> -->
          <template v-if="!showGreetings">
            <form
              novalidate
              @submit.prevent="submitForm"
              class="w-full h-auto bg-white rounded-[10px] pt-[60px] pb-10 px-8 sm:px-10"
            >
              <div class="md:mb-[60px] mb-[45px] flex flex-col">
                <label for="name" class="text-[#1D1A20]">Full Name</label>
                <input
                  id="name"
                  v-model="contactForm.name"
                  type="text"
                  class="outline-none text-xl sm:text-2xl text-[#000] pb-2.5 mt-5 border-b-2 border-[#1D1A20]"
                  placeholder="Jone Deo"
                  @blur="v$.name.$touch()"
                />
                <template v-if="v$.name.$error">
                  <p
                    v-if="v$.name.required.$invalid"
                    class="fullName-error text-red-500 text-sm"
                  >
                    Full Name is Required
                  </p>
                  <p
                    v-else-if="v$.name.maxLength.$invalid"
                    class="fullName-max-error text-red-500 text-sm"
                  >
                    Maximum Character is 40
                  </p>
                </template>
              </div>
              <div
                class="flex flex-col md:flex-row space-x-0 md:space-x-[57px] w-full mb-[60px] space-y-[45px] md:space-y-0"
              >
                <div class="w-full md:w-1/2">
                  <label for="phoneNumber" class="text-[#1D1A20]"
                    >Phone Number*</label
                  >
                  <ClientOnly>
                    <InputPhoneNumberInput
                      ref="phoneNumberRef"
                      :id="'phoneNumber'"
                      color="#000"
                      class="phoneNumberInputPartner text-xl sm:text-2xl"
                      background="#fff"
                      input-classes="customPhoneInputClass"
                      @error="getError($event)"
                    ></InputPhoneNumberInput>
                  </ClientOnly>
                </div>
                <div class="w-full md:w-1/2 flex flex-col">
                  <label for="email" class="text-[#1D1A20]">Email*</label>
                  <input
                    id="email"
                    v-model.trim="contactForm.email"
                    type="email"
                    class="outline-none text-xl sm:text-2xl text-[#000] pb-2.5 mt-5 border-b-2 border-[#1D1A20]"
                    placeholder="<EMAIL>"
                    @blur="v$.email.$touch()"
                  />
                  <template v-if="v$.email.$error">
                    <p
                      v-if="v$.email.required.$invalid"
                      class="email-error text-red-500 text-sm"
                    >
                      Email is Required
                    </p>
                    <p
                      v-else-if="v$.email.email.$invalid"
                      class="invalid-email text-red-500 text-sm"
                    >
                      Email is Invalid
                    </p>
                  </template>
                </div>
              </div>
              <div class="flex flex-col">
                <label for="question" class="text-[#1D1A20]"
                  >Project details*</label
                >
                <textarea
                  id="question"
                  v-model="contactForm.question"
                  name="question"
                  rows="2"
                  class="outline-none text-[#000] mt-5 border-b-2 border-[#1D1A20] text-xl sm:text-2xl"
                  placeholder="Tell us more about your idea"
                  @blur="v$.question.$touch()"
                ></textarea>
                <template v-if="v$.question.$error">
                  <p
                    v-if="v$.question.required.$invalid"
                    class="comments-error text-red-500 text-sm"
                  >
                    Project details is Required
                  </p>
                  <p
                    v-else-if="v$.question.maxLength.$invalid"
                    class="comments-max-error text-red-500 text-sm"
                  >
                    Maximum Character is 500
                  </p>
                </template>
              </div>
              <button
                class="md:mt-[70px] mt-[45px] rounded-full bg-[#FFD700] text-lg text-black w-[180px] h-[52px] flex justify-center items-center"
              >
                Send Inquiry
              </button>
            </form>
          </template>
          <template v-if="showGreetings">
            <div class="p-10 bg-[#FFFFFF]">
              <p class="greetingText">
                Thank you! Your submission has been received!
              </p>
            </div>
          </template>

          <p class="secondary-p-tag pt-5">
            Not Interested in submitting the form?
            <NuxtLink
              to="https://calendly.com/devxhub/15min"
              class="text-[#FFD700] hover:text-[#ffd700] underline decoration-[#8CAE33] underline-offset-4"
              target="_black"
              >Book A Call Directly</NuxtLink
            >
          </p>
        </div>
      </div>
      <!-- <div
      class="contact-card w-full lg:max-h-[400px] min-h-[200px] rounded-3xl flex-col px-3.5 mx-auto flex lg:flex-row lg:space-x-4 justify-around items-center text-center py-3"
      :class="[
        $route.name.includes('career')
          ? 'md:px-10 md:py-10'
          : 'md:px-20 md:py-7',
      ]"
    >
      <div v-if="$route.name.includes('career')">
        <img
          class="shadow-lg w-[18vw] h-[18vw] max-w-[150px] max-h-[150px] lg:w-[21.01vw] lg:h-[21.01vw] mx-auto mb-2 md:mb-0"
          src="/home/<USER>/resume_apply.svg"
          alt="devxhub-resume-apply"
        />
      </div>

      <div class="md:py-5 text-center md:text-left">
        <div v-if="!$route.name.includes('career')">
          <h2
            class="pt-3 md:pt-0 pb-1 break-words text-xl md:text-3xl lg:text-4xl text-primary font-semibold"
          >
            Have Any Project For Us 🤔
          </h2>

          <p class="py-8 text-sm md:text-2xl text-primary">
            Lets discuss about your dreamed project. Our experienced global team
            is eager to assist you. Schedule a complimentary consultation to
            discuss your needs.
          </p>
          <div class="mb-5 lg:mb-0 py-5">
            <ClientOnly>
              <NuxtLink
                to="/contact-us"
                class="w-32 h-40 lg:w-40 lg:h-10 xl:w-48 xl:h-12 bg-[#FAAF04] px-6 py-5 font-semibold text-black text-base md:text-2xl rounded-lg whitespace-nowrap"
                aria-label="Contact Us"
              >
                Contact Us

                <fa class="text-black ml-1" :icon="['fas', 'arrow-right']" />
              </NuxtLink>
            </ClientOnly>
          </div>
        </div>

        <div v-else>
          <h2
            class="pb-1 pl-5 text-base md:text-3xl text-primary text-center lg:text-left"
          >
            Even if above opened
            {{
              $route.name.includes("career-slug") ? "position" : "positions"
            }}
            do not match with your profile, we still encourage you to apply.
          </h2>
          <p
            class="pb-1 pl-5 text-base md:text-3xl text-primary text-center lg:text-left"
          >
            Feel free to
            <NuxtLink
              to="https://forms.gle/DymNKcw3Nb6Jamox7"
              target="_blank"
              class="font-bold"
            >
              Apply
            </NuxtLink>
          </p>
        </div>
      </div>

      <div v-if="$route.name.includes('career')" class="mb-5 lg:mb-0 py-5">
        <NuxtLink
          to="https://forms.gle/NHHMEhkgqqntNwoL8"
          target="_blank"
          class="w-32 h-40 lg:w-40 lg:h-10 xl:w-48 xl:h-12 bg-[#FAAF04] px-5 py-3 font-bold text-black text-base rounded-sm whitespace-nowrap"
        >
          Apply Now
        </NuxtLink>
      </div>
    </div> -->
    </div>
  </div>
</template>

<style scoped>
.contact-card {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  box-shadow: 0px 16px 42px rgba(0, 30, 108, 0.3);
  border-radius: 20px;
}
.form {
  @apply w-full md:h-[522px] h-[547px];
}
.greetingText {
  color: #4b5154;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.4em;
  padding: 20px;
  background-color: #dddddd;
}
</style>

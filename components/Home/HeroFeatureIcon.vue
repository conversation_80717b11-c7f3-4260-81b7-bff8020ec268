<script setup>
import { ref } from "vue";

const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  size: {
    type: String,
    default: "mobile", // 'mobile' | 'desktop'
  },
});

const classes = {
  mobile: {
    container: "mx-auto",
    text: "text-[14px] leading-[18px] text-center font-bold pt-[10px]",
  },
  desktop: {
    container: "flex items-center gap-[10px] justify-center",
    text: "text-[16px] leading-[24px] text-center font-bold text-wrap",
  },
};
</script>

<template>
  <div :class="classes[size].container">
    <img :src="icon" loading="lazy" :alt="title" class="mx-auto" />
    <p :class="classes[size].text">{{ title }}</p>
  </div>
</template>

<script setup>
const fullTimes = ref([
  {
    img: "/landing/whatmakesus/Asset_1.svg",
    title: "User-Centric, Data-Driven Solutions",
    description: `We create seamless, user-focused software using real-world data to ensure measurable results and enhanced experiences.`,
  },
  {
    img: "/landing/whatmakesus/Asset_2.svg",
    title: "Innovation & Structured Development ",
    description: `Our creative yet systematic approach ensures reliable, high-quality software delivery on time and within scope.`,
  },
  {
    img: "/landing/whatmakesus/Asset_3.svg",
    title: "End-to-End, Scalable Solutions",
    description: `From development to deployment, we build future-proof, scalable software with seamless integration and ongoing support.`,
  },
  {
    img: "/landing/whatmakesus/Asset_4.svg",
    title: "Data Security & Reliability",
    description: `We prioritize data protection, confidentiality, and system integrity to ensure secure and reliable software solutions.`,
  },
  {
    img: "/landing/whatmakesus/Asset_5.svg",
    title: "Cost-Effective & Timely Delivery",
    description: `High-quality software delivered on time and within budget, maximizing efficiency without compromising performance.`,
  },
  {
    img: "/landing/whatmakesus/Asset_6.svg",
    title: "Long-term Relationships ",
    description: `We go beyond projects, building lasting partnerships to support your business’s long-term success and growth.`,
  },
]);

const sectionViewPort = () => {
  // what makes us difference section animation
  const titleAnimation = document.querySelectorAll(".title-aimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  titleAnimation.forEach((item) => {
    observerThree.observe(item);
  });

  const descripAnimation = document.querySelectorAll(".descrip-aimation");
  const observerFour = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  descripAnimation.forEach((item) => {
    observerFour.observe(item);
  });
};

onMounted(() => {
  sectionViewPort();
});
</script>

<template>
  <div class="relative">
    <!-- <div class="absolute top-0 right-0 left-0 flex justify-center">
          <img
            src="/public/landing//644a1c92b49983ca116cd5f2_Rectangle 17714.webp"
            alt="Why use Full-time Developers & Tech Solutions from Devxhub?"
          />
        </div> -->
    <div class="flex flex-col z-10">
      <!-- <div class="md:mb-[140px] mb-[40px]">
        <div class="overflow-hidden">
          <p
            class="title-aimation md:text-7xl text-4xl md:leading-[93.6px] leading-[46.8px] font-bold text-[#F1F1F2]"
          >
            What <span class="text-[#999]">Makes Us</span>
          </p>
        </div>
        <div class="overflow-hidden">
          <p
            class="title-aimation md:text-7xl text-4xl md:leading-[93.6px] leading-[46.8px] font-bold text-[#F1F1F2]"
          >
            <span class="text-[#999]"> Different</span> from Others?
          </p>
        </div>
      </div> -->
      <div class="three-grid-item">
        <div class="md:max-w-[152px] max-w-[220px]">
          <div class="overflow-hidden">
            <p
              class="title-aimation text-xl font-medium text-[#999] hidden md:block"
            >
              What
              <span class="text-[#f1f1f2] instrument-italic">Makes Us</span>
            </p>
            <p
              class="title-aimation text-overflow text-lg font-medium text-[#999] md:hidden"
            >
              What
              <span class="text-[#f1f1f2] text-lg font-medium instrument-italic"
                >Makes us Different</span
              >
            </p>
          </div>
          <div class="overflow-hidden">
            <p
              class="title-aimation text-xl font-medium text-[#999] hidden md:block"
            >
              <span class="text-[#f1f1f2] instrument-italic"> Different</span>
              from Others
            </p>
            <p class="title-aimation text-lg font-medium text-[#999] md:hidden">
              <span class="text-[#f1f1f2] instrument-italic"> Solutions</span>
              from Others?
            </p>
          </div>
        </div>
        <div class="flex flex-col space-y-8 w-full">
          <div
            v-for="fullTime in fullTimes"
            :key="fullTime.id"
            class="flex md:flex-row flex-col md:space-x-6 text-[#F1F1F2] min-[992px]:items-center w-full border-b border-[#ffffff1a] md:pb-8 pb-5 last:border-0"
          >
            <img
              class="descrip-aimation w-[40px] h-[40px] min-[992px]:mt-0 mt-0.5"
              :src="fullTime.img"
              :alt="fullTime.title"
            />
            <div class="text-grid-item flex-grow justify-start items-center">
              <p
                class="descrip-aimation md:text-2xl text-[22px] leading-[32px] md:leading-[28px] font-bold pt-4 md:pt-0"
              >
                {{ fullTime.title }}
              </p>
              <div class="bg-[#1A1139] z-[1] max-w-[800px] descrip-aimation">
                <p class="md:text-base text-[14px] text-[#999]">
                  {{ fullTime.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.three-grid-item {
  /* z-index: 0; */
  grid-column-gap: 130px;
  grid-row-gap: 0px;
  grid-template-rows: auto auto;
  grid-template-columns: 0.2fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}

.text-grid-item {
  /* z-index: 0; */
  grid-column-gap: 121px;
  /* grid-row-gap: 106px; */
  grid-template-rows: auto auto;
  grid-template-columns: 0.65fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
@media screen and (max-width: 1279px) {
  .three-grid-item {
    /* z-index: 0; */
    grid-column-gap: 40px;
    grid-row-gap: 0px;
    grid-template-rows: auto auto;
    grid-template-columns: 0.2fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
    position: relative;
  }
  .text-grid-item {
    /* z-index: 0; */
    grid-column-gap: 60px;
  }
}
@media screen and (max-width: 991px) {
  .three-grid-item {
    grid-column-gap: 0px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr;
  }
  .text-grid-item {
    /* z-index: 0; */
    grid-column-gap: 0px;
    grid-row-gap: 10px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    display: grid;
    position: relative;
  }
}
@media screen and (max-width: 767px) {
  .three-grid-item {
    /* grid-row-gap: 88px; */
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 479px) {
  .three-grid-item {
    grid-template-columns: 1fr;
    grid-row-gap: 32px;
  }
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
.descrip-aimation {
  transform: translateY(70px);
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.descrip-aimation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

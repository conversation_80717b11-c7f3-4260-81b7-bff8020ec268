<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";

const breakpoints = useBreakpoints(breakpointsTailwind);
const isLarge = breakpoints.greaterOrEqual("lg");

const initialStateStackCard = ref<any>([]);
const Services = ref([
  {
    id: 1,
    background: "linear-gradient(270deg, #166BBC 0%, #3E9DF6 106.25%)",
    image:
      "https://admin.devxhub.com/media/images/services/custom-enterprise-software-development.webp",
    serviceDes: `<p class="md:mt-[40px] mt-4">\r\n          Looking to scale your software development team without the overhead of full-time
 hires?</p>\r\n      <p>\r\n          We connect you with a dedicated remote team of highly skilled top developers from
 Bangladesh at a lower budget. Our IT staff augmentation services provide skilled
 professionals to fill critical gaps, accelerate project timelines, and enhance your
 in-house capabilities. We match you with top talent tailored to your project
 requirements. </p> \r\n      <p>\r\n           With full flexibility, budget efficiency, and a deep dive into a global pool of experts,
 ensure your team stays agile and competitive. </p> \r\n
 <p class="">Optimize productivity, grow your revenue, \r\n and drive success with our reliable and scalable IT staffing solutions <p>`,
    serviceTitle: `<p>IT Staff</p><p class="text-h-gradient">Augmentation</p>`,
    subServiceTitle: " IT Staff Augmentation",
    path: "/services/it-staff-augmentation",
    btnText: "See More",
  },
  {
    id: 2,
    background: "linear-gradient(270deg, #3A1C90 0%, #8B4CF8 100%)",
    image:
      "https://admin.devxhub.com/media/images/services/website-web-application-design-development.webp",
    serviceDes: `<p class="mt-[20px]">
         We specialize in Full-Stack Development, delivering end-to-end solutions customized
 to meet the unique needs of any industry. Our expertise spans Frontend Design,
 Backend and API Development, Database Architecture, and Server Management,
 enabling us to create scalable, dynamic, and efficient web and mobile applications.
      </p><p>
         Our approach focuses on building seamless digital experiences, from intuitive,
 user-friendly interfaces to powerful, secure backend systems that drive business
 growth and innovation.
      </p>`,
    serviceTitle: `<p>Full Stack Development</p><p class="text-h-gradient">UI, Web, Mobile Application</p>`,
    subServiceTitle: "Full Stack Development | Web, Mobile Application",
    path: "/services/full-stack-development",
    btnText: "See More",
  },
  {
    id: 3,
    background: "linear-gradient(270deg, #0C7347 0%, #85F9C2 100%)",
    image:
      "https://admin.devxhub.com/media/images/services/mobile-application-design_development_1Mkt69W.webp",
    serviceDes: `<p class="mt-[20px]">
        Our Custom & Enterprise Software Development services are designed to create
 scalable, secure, and user-friendly large and complex systems and applications that
 drive growth and streamline your operations.
      </p>
      <p>
        We got covered a wide range of industries like healthcare, finance, retail, Govt. & IT.
      </p>
      <p>
         From building custom CRMs, ERPs, HIMs, and HRMs for industries & platforms,
 mobile apps, and AI-powered tools, we deliver solutions tailored to your unique
 needs. Whether you’re modernizing legacy systems or developing innovative modern
 tools, our expertise ensures your software aligns seamlessly with your business
 goals—helping you stay ahead in a competitive market.
      </p>`,
    serviceTitle: `<p>Custom & Enterprise</p><p class="text-h-gradient">Software Development</p>`,
    subServiceTitle: "Custom & Enterprise Software Development",
    path: "/services/custom-software-development",
    btnText: "See More",
  },
  {
    id: 4,
    background: "linear-gradient(270deg, #6B197C 0%, #B6119D 100%)",
    image: "https://admin.devxhub.com/media/images/services/UI-UX_bKfgyQT.webp",
    serviceDes: `<p class="mt-[20px]">
        We specialize in building MVPs to validate your ideas quickly, creating robust SaaS
 platforms for scalable business models, and delivering comprehensive end-to-end
 solutions tailored to your needs. From ideation and design to development,
 deployment, and ongoing support, we handle the entire lifecycle to ensure seamless
 execution.
      </p>
      <p>
         With a focus on innovation, scalability, and user-centric design, our solutions
 empower your business to launch faster, grow smarter, and stay ahead in a
 competitive market.
      </p>`,
    serviceTitle: `<p>MVP, SaaS, End-to-End</p><p class="text-h-gradient">Development</p>`,
    subServiceTitle: "MVP, SaaS, End-to-End Development",
    path: "/services/mvp-saas-end-to-end-development",
    btnText: "See More",
  },
  {
    id: 5,
    background: "linear-gradient(270deg, #F8C343 0%, #FFDF6A 100%)",
    image:
      "https://admin.devxhub.com/media/images/services/devops-solution_aht5pyj.webp",
    serviceDes: `<p class="mt-[20px]">
         We help you utilize the power of artificial intelligence and machine learning to
 automate processes, uncover actionable insights, and deliver personalized user
 experiences, from developing intelligent AI software and chatbots to integrating
 AI-powered tools into your existing or current software.
      </p>
      <p>
        Our AI/ML services are designed to drive scalability, precision, and innovation,
 enabling your business to make informed decisions, boost efficiency, and maintain a
 competitive edge.
      </p>`,
    serviceTitle: `<p>AI/ML Development &</p><p class="text-h-gradient">Integration</p>`,
    subServiceTitle: "AI/ML Development & Integration",
    path: "/services/ai-ml-development-integration",
    btnText: "See More",
  },
  // {
  //   id: 6,
  //   background: "linear-gradient(270deg, #631B7A 0%, #216DDD 100%)",
  //   image:
  //     "https://admin.devxhub.com/media/images/services/digital-marketing_HrgjTPb.webp",
  //   serviceDes: `<p>
  //     This service encompasses a range of strategies and tactics aimed at
  //     promoting businesses, products, or services online.
  //     <span class="yellowBlackHighLight"
  //       >Our goal is to increase brand visibility, drive targeted traffic, and
  //       generate leads and conversions.</span
  //     >
  //     We specialize in various digital marketing channels, such as
  //     <span class="yellowBlackHighLight"
  //       >Search Engine Optimization (SEO), Pay-Per-Click (PPC) advertising,
  //       content marketing, social media marketing, email marketing, and
  //       influencer marketing,</span
  //     >
  //     tailoring campaigns to achieve optimal results and meet clients' unique
  //     objectives.
  //   </p>`,
  //   serviceTitle: "Digital Marketing",
  //   subServiceTitle: "Digital Marketing",
  // },
  //   {
  //     id: 7,
  //     background: "linear-gradient(270deg, #3A1C90 0%, #8B4CF8 100%)",
  //     image:
  //       "https://admin.devxhub.com/media/images/services/maintenance-and-continuous-support_62lbhPV.webp",
  //     serviceDes: `<p>
  //    We craft intuitive, user-centric interfaces that enhance engagement and create
  //  seamless experiences across web and mobile platforms.
  // </p>
  // <p>
  //  Our 3D animations bring concepts to life, adding a dynamic and visually captivating
  //  dimension to your projects. From interactive prototypes to high-quality 3D visuals, we
  //  deliver creative solutions that align with your brand and captivate your audience.
  //  With a focus on aesthetics, functionality, and innovation, our services help your
  //  business stand out in a competitive market.
  // </p>`,
  //     serviceTitle: "UI/UX, 3D Animation",
  //     subServiceTitle: "UI/UX, 3D Animation",
  //   },
  {
    id: 8,
    background: "linear-gradient(270deg, #35898E 0%, #80DBEF 100%)",
    image:
      "https://admin.devxhub.com/media/images/services/software-quality-assurance_G9MnuFD.webp",
    serviceDes: `<p class="mt-[20px]">
   We specialize in automating workflows, enhancing collaboration, and optimizing
 infrastructure to accelerate delivery cycles and ensure scalability. From cloud
 migration and management to CI/CD pipelines and containerization, we deliver
 customized solutions to help your business stay agile and efficient. With a focus on
 reliability, security, and performance, reducing costs and increasing operational
 efficiency.
</p>
<p>
   Using AWS, Google Cloud, Digital Ocean, Linux, Windows Server, Docker, Ansible,
 and Kubernetes.
</p>
<p>We have certified AWS & Cloud Solution Architects. </p>`,
    serviceTitle: `<p>DevOps & Cloud</p><p class="text-h-gradient">Solutions</p>`,
    subServiceTitle: "DevOps & Cloud Solutions",
    path: "/services/devops-cloud-solutions",
    btnText: "See More",
  },
]);
const handleScroll = () => {
  if (
    window.scrollY > 100 &&
    initialStateStackCard.value.length !== Services.value?.length
  ) {
    if (Services.value) {
      Services.value?.forEach((service) => {
        let isIncluded = false;
        if (initialStateStackCard.value.length > 0) {
          initialStateStackCard.value.forEach((singleState: any) => {
            if (singleState?.id === service.id) {
              isIncluded = true;
            }
          });
        }
        if (!isIncluded && service && service.id) {
          const currentDivOffsetTop = document.getElementById(
            service.id.toString()
          )?.offsetTop;

          if (currentDivOffsetTop) {
            initialStateStackCard.value.push({
              id: service.id,
              initialOffsetTop: currentDivOffsetTop - 100,
            });
          }
        }
      });
    }
  }
};
onMounted(() => {
  window.addEventListener("scroll", handleScroll);

  setTimeout(() => {
    stackCardAnimation();
  }, 700);

  window.addEventListener("resize", function (e) {
    handleScroll();
    stackCardAnimation();
    setTimeout(() => {
      stackCardAnimation();
      window.scrollTo(0, window.scrollY + 100);
    }, 700);
  });
});

const getOffsetTop = (id: any) => {
  console.log("id", id);
  initialStateStackCard.value.forEach((singleState: any) => {
    if (singleState.id === id) {
      window.scrollTo(0, singleState.initialOffsetTop);
    }
  });
};
</script>

<template>
  <div id="our-services" class="">
    <div class="px-0 md:px-4 lg:px-24">
      <h2 class="text-center secondary-h-tag">
        <span class="text-[#F1F1F2]">Our</span>
        <span class="instrument-italic"> Services</span>
      </h2>
      <!-- <div class="text-white font-semibold text-center md:pt-8 pt-5">
        <h2 class="md:text-[32px] md:leading-8 text-xl">
          Comprehensive Software Development Services
        </h2>
        <h3 class="md:text-2xl text-base pt-2">
          Streamlining Processes & Enhancing User Experiences
        </h3>
      </div>
      <p class="text-[#F0F0F0] md:text-lg text-base text-center pt-5">
        Software development services include Custom Software Development, Web
        and Mobile Application Design & Development, UI/UX design, DevOps,
        Software Testing, Maintenance, and Continuous Support. These services
        streamline processes, improve productivity, and enhance user experiences
        by creating tailored software solutions.
      </p> -->
    </div>
    <ClientOnly>
      <div
        v-if="isLarge"
        class="hidden lg:grid grid-cols-1 gap-4 md:gap-6 justify-items-center justify-around flex-wrap stack-cards js-stack-cards !mt-10"
      >
        <div
          v-for="service in Services"
          :key="service.id"
          :id="`${service.id}`"
          :class="`stack-cards__item js-stack-cards__item w-full`"
        >
          <div
            class="company-card max-w-[955px] bg-[#F3F1EE] mx-auto p-[60px] xl:h-[756px] lg:h-[680px] h-[557px] w-full flex items-center justify-between rounded-[20px]"
            @click="getOffsetTop(service.id)"
          >
            <div
              class="flex flex-col justify-start h-full w-full rounded-[20px]"
            >
              <!-- <h2 class="text-lg font-bold leading-8 cursor-pointer">
                {{ service?.serviceTitle.toUpperCase() }}
              </h2> -->
              <div class="flex flex-col space-y-5 justify-center">
                <h3
                  class="text-[#1D1A20] text-[38px] md:text-[44px] xl:text-[54px] leading-10 md:leading-[56px] xl:leading-[66px] font-bold"
                  v-html="service.serviceTitle"
                ></h3>
                <div
                  class="third-p-tag !text-[#1D1A20] leading-[19px] space-y-6"
                  v-html="service.serviceDes"
                ></div>
              </div>
              <button class="btn-top">
                <NuxtLink
                  :to="service.path"
                  class="w-[150px] h-[46px] bg-[#FFD700] text-[#1D1A20] flex justify-center items-center rounded-full"
                  v-if="service.btnText"
                  >{{ service.btnText }}
                  <span class="sr-only">{{ service.subServiceTitle }}</span>
                </NuxtLink>
              </button>
            </div>
            <!-- <div
              class="h-full max-w-[400px] flex items-center justify-center rounded-r-[30px] p-8 image-border cursor-pointer"
            >
              <div class="flex flex-col justify-center">
                <img
                  class="w-[18vw] aspect-square object-contain"
                  :src="service.image"
                  :alt="service.name"
                />
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <div
        v-else
        class="lg:hidden grid grid-cols-1 gap-4 md:gap-6 justify-items-center justify-around flex-wrap stack-cards js-stack-cards mt-5"
      >
        <div
          v-for="(service, index) in Services"
          :key="service.id"
          class="stack-cards__item js-stack-cards__item w-full"
        >
          <div
            class="company-card bg-[#F3F1EE] sm:h-[580px] xs:h-[660px] h-[795px] w-full flex flex-col lg:flex-row md:items-center items-start lg:justify-between rounded-[20px]"
          >
            <!-- <div
              class="h-[150px] md:h-[220px] w-full flex items-center justify-center rounded-t-[30px] lg:rounded-r-[30px] p-8 image-border"
            >
              <img
                class="w-[130px] md:w-[180px] lg:w-[18vw] aspect-square object-contain"
                :src="service.image"
                :alt="service.name"
              />
            </div> -->
            <div
              class="flex flex-col md:justify-center md:items-center items-start h-fit md:h-full w-full rounded-[20px] md:text-center lg:text-left p-4 xl:px-8 space-y-0 grow"
            >
              <!-- <h2
                class="text-sm md:text-lg font-bold leading-8 hidden lg:block"
              >
                {{ service?.serviceTitle.toUpperCase() }}
              </h2> -->
              <h2
                class="text-3xl md:text-[38px] xl:text-[40px] leading-8 md:leading-10 xl:leading-[48px] font-bold"
                v-html="service.serviceTitle"
              ></h2>
              <div
                class="md:primary-p-tag home-service-p-tag !text-[#1D1A20] md:space-y-6 space-y-4 text-left"
                v-html="service.serviceDes"
              ></div>
            </div>
            <button class="mt-[20px] pl-4 pb-4">
              <NuxtLink
                :to="service.path"
                class="btn-align w-[150px] h-[46px] bg-[#FFD700] text-[#1D1A20] rounded-full"
                v-if="service.btnText"
                >{{ service.btnText }}
                <span class="sr-only">{{ service.subServiceTitle }}</span>
              </NuxtLink>
            </button>
          </div>
        </div>
      </div>
    </ClientOnly>
  </div>
</template>

<style lang="scss" scoped>
.stack-cards__item {
  position: sticky;
  @apply sm:top-36 -top-36 mt-0;
  // top: -9rem;
  transform-origin: center top;
}
.stack-cards {
  --stack-cards-gap: 36px;
}
.image-border {
  @apply border-[#FDB21D] border-y-[1px] border-r-[1px];
}

@media (max-width: 1023px) {
  .stack-cards {
    --stack-cards-gap: 0px;
  }
  .image-border {
    @apply border-x-[1px] border-t-[1px];
  }
}
@media (max-height: 800px) {
  .stack-cards__item {
    @apply sm:top-[5rem] -top-36 mt-0;
  }
  .stack-cards__item > div {
    // padding-top: 60px;
  }
}
@media (max-height: 670px) {
  .stack-cards__item {
    @apply sm:top-[0rem] -top-36 mt-0;
  }
  .stack-cards__item > div {
    // padding: 40px;
  }
}
@media (max-width: 767px) {
  @media (max-height: 670px) {
    .stack-cards__item {
      @apply sm:top-[0rem] -top-52 mt-0;
    }
    .stack-cards__item > div {
      padding: 16px;
    }
  }
}
.company-card {
  box-shadow: rgba(0, 0, 0, 0.15) 0px -1px 3px 0px;
}

.btn-align {
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn-top {
  margin-top: 50px;
}
@media (min-width: 1024px) and (max-width: 1439px) {
  .btn-top {
    margin-top: 20px;
  }
}
</style>

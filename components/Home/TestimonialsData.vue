<template>
  <!-- max-w-[620px] -->
  <div class="w-full">
    <div class="flex items-center justify-center relative testimonialCard p-10">
      <div class="flex flex-col justify-between">
        <BaseIconInvertedComma></BaseIconInvertedComma>
        <div class="text-[#1D1A20] mt-[24px] text-left">
          <p class="" v-html="description"></p>
        </div>
        <div class="mt-[60px] flex space-x-5 items-center">
          <img
            v-if="images"
            class="rounded-full"
            width="80"
            height="80"
            :src="images"
            :alt="ownerName || companyName"
          />
          <div class="text-[#1D1A20] font-bold text-left">
            <p v-if="ownerName" class="text-xl">{{ ownerName }}</p>
            <h3 class="text-sm">
              {{ companyName }}
            </h3>
            <p class="text-sm font-normal">
              {{ position }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: [
    "companyName",
    "images",
    "ownerName",
    "position",
    "title",
    "description",
  ],
};
</script>

<style lang="scss" scoped>
.testimonialCard {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.05);
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-width: thin;
  scrollbar-color: #fdb21d #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 3px;
    height: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #fdb21d;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #fdb21d;
  }
}
</style>

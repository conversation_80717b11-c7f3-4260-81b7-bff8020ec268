<script setup>
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { Carousel, Slide } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

// breakpoints
const breakpoints = useBreakpoints(breakpointsTailwind);
const isLarge = breakpoints.greaterOrEqual("lg");
const isTab = breakpoints.greaterOrEqual("md");
const route = useRoute();
const settings = computed(() => {
  const setting = {
    itemsToShow: isLarge.value ? 5 : isTab.value ? 3 : 1,
    napAlign: "center",
    wrapAround: false,
    autoplay: 5000,
    wrapAround: true,
  };

  return setting;
});

const show = ref(false);
const companies = ref([
  {
    id: 1,
    selected: false,
    image: "/industryExperties/fintech.svg",
    whiteimage: "/industryExperties/fintech-white.svg",
    text: "Fintech",
    backgroundColor: "#000000",
    margin: 1.8,
  },
  {
    id: 2,
    selected: false,
    image: "/industryExperties/healthCare.svg",
    whiteimage: "/industryExperties/healthCare-white.svg",
    text: "Health Care",
    backgroundColor: "#662D90",
    margin: 1.8,
  },
  {
    id: 3,
    selected: false,
    image: "/industryExperties/telco.svg",
    whiteimage: "/industryExperties/telco-white.svg",
    text: "Telco",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 4,
    selected: false,
    image: "/industryExperties/e-com.svg",
    whiteimage: "/industryExperties/e-com-white.svg",
    text: "E-com",
    backgroundColor: "#079CC0",
    margin: 1.8,
  },
  {
    id: 5,
    selected: false,
    image: "/industryExperties/realEstate.svg",
    whiteimage: "/industryExperties/realEstate-white.svg",
    text: "Real Estate",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 6,
    selected: false,
    image: "/industryExperties/autoMotive.svg",
    whiteimage: "/industryExperties/autoMotive-white.svg",
    text: "AutoMotive",
    backgroundColor: "#079CC0",
    margin: 1.8,
  },
  {
    id: 7,
    selected: false,
    image: "/industryExperties/software.svg",
    whiteimage: "/industryExperties/software-white.svg",
    text: "Software",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 8,
    selected: false,
    image: "/industryExperties/startup.svg",
    whiteimage: "/industryExperties/startup-white.svg",
    text: "Startup",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 9,
    selected: false,
    image: "/industryExperties/education.svg",
    whiteimage: "/industryExperties/education-white.svg",
    text: "Edu Tech",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 10,
    selected: false,
    image: "/industryExperties/retails.svg",
    whiteimage: "/industryExperties/retails-white.svg",
    text: "Retail",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 11,
    selected: false,
    image: "/industryExperties/nonProfit.svg",
    whiteimage: "/industryExperties/nonProfit-white.svg",
    text: "Non Profit",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 12,
    selected: false,
    image: "/industryExperties/miscellaneous.svg",
    whiteimage: "/industryExperties/miscellaneous-white.svg",
    text: "Miscellaneous",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 13,
    selected: false,
    image: "/industryExperties/agriculture.svg",
    whiteimage: "/industryExperties/agriculture-white.svg",
    text: "Agriculture",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 14,
    selected: false,
    image: "/industryExperties/construction.svg",
    whiteimage: "/industryExperties/construction-white.svg",
    text: "Construction",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 15,
    selected: false,
    image: "/industryExperties/energy.svg",
    whiteimage: "/industryExperties/energy-white.svg",
    text: "Energy",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 16,
    selected: false,
    image: "/industryExperties/erpSolution.svg",
    whiteimage: "/industryExperties/erpSolution-white.svg",
    text: "ERP-Solution",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 17,
    selected: false,
    image: "/industryExperties/financeBanking.svg",
    whiteimage: "/industryExperties/financeBanking-white.svg",
    text: "Finance Banking",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 18,
    selected: false,
    image: "/industryExperties/govtPublicSector.svg",
    whiteimage: "/industryExperties/govtPublicSector-white.svg",
    text: "Government Public Sector",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 19,
    selected: false,
    image: "/industryExperties/hospitality.svg",
    whiteimage: "/industryExperties/hospitality-white.svg",
    text: "Hospitality",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 20,
    selected: false,
    image: "/industryExperties/informationTechnology.svg",
    whiteimage: "/industryExperties/informationTechnology-white.svg",
    text: "Information Technology",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 21,
    selected: false,
    image: "/industryExperties/manufacturing.svg",
    whiteimage: "/industryExperties/manufacturing-white.svg",
    text: "Manufacturing",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
  {
    id: 22,
    selected: false,
    image: "/industryExperties/mediaEntertainment.svg",
    whiteimage: "/industryExperties/mediaEntertainment-white.svg",
    text: "Media Entertainment",
    backgroundColor: "#6D4410",
    margin: 1.8,
  },
]);
const myCarousel = ref(null);
onMounted(() => {
  if (route.name === "index") {
    setTimeout(() => {
      show.value = true;
    }, 600);
  }
});
const showRightArrow = ref(true);
const showLeftArrow = ref(false);
const getLastSlideIndex = (cI) => {
  if (cI.currentSlideIndex + 6 === cI.slidesCount && isLarge.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 4 === cI.slidesCount && isTab.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 2 === cI.slidesCount) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex === 0) {
    showRightArrow.value = true;
    showLeftArrow.value = false;
  }
};
</script>

<template>
  <div>
    <h2 class="sixth-h-tag">
      <p><span class="text-[#999]">Our Expertise in</span> Software</p>
      <p>Development <span class="text-[#999]">Across </span>Industries</p>
    </h2>
    <p class="primary-p-tag mt-5 max-w-[653px]">
      Combining advanced technology and decades of industry insight, we design
      and develop bespoke full-cycle solutions tailored to deliver your unique
      software vision
    </p>
    <!-- <NextLink
      class="md:mt-[52px] mt-[32px] bg-[#FFD700] text-[#1D1A20] flex justify-center items-center w-[160px] h-[52px] rounded-full"
      >Learn More</NextLink
    > -->
    <div class="relative mt-[80px]">
      <carousel
        ref="myCarousel"
        :autoplay="0"
        :wrap-around="false"
        :items-to-show="isLarge ? 6 : isTab ? 4 : 2"
        :pauseAutoplayOnHover="true"
        snapAlign="start"
        @slide-end="getLastSlideIndex"
        class="relative"
      >
        <slide
          class="flex flex-col !justify-start !px-[1px] transition-all duration-200 ease-in-out"
          :class="
            company.selected
              ? isLarge
                ? '!w-[20.6667%]'
                : isTab
                ? '!w-[29%]'
                : '!w-[54%]'
              : ''
          "
          v-for="(company, index) in companies"
          :key="index"
          @mouseover="company.selected = true"
          @mouseleave="company.selected = false"
        >
          <div
            class="flex flex-col justify-between items-start lg:p-5 p-3.5 bg-white w-full h-[280px]"
          >
            <div class="flex flex-col space-y-2.5 z-[10]">
              <Transition name="fadeIn" mode="out-in">
                <img
                  v-if="!company.selected"
                  class="w-[30px] h-[30px]"
                  :src="company.image"
                  :alt="company.text"
                />
                <img
                  v-else
                  class="w-[30px] h-[30px]"
                  :src="company.whiteimage"
                  :alt="company.text"
                />
              </Transition>
              <p
                class="text-left text-xl font-bold transition-all duration-1000 ease-in-out"
                :class="company.selected ? 'text-white' : 'text-[#1D1A20]'"
              >
                {{ company.text }}
              </p>
            </div>
            <ClientOnly>
              <fa
                :icon="['fas', 'arrow-up']"
                class="transform rotate-90 text-left text-[28px] transition-all duration-1000 ease-in-out"
                :class="company.selected ? 'opacity-0' : 'opacity-100'"
              />
            </ClientOnly>
          </div>
          <!-- <img
            class="w-full h-full absolute top-0 left-0 transition-all duration-1000 ease-in-out"
            :class="company.selected ? 'opacity-100' : 'opacity-0'"
            src="/public/industryExperties/image.png"
            alt="industry-expert"
          /> -->
          <div
            class="w-full h-full bg-black absolute top-0 left-0 transition-all duration-1000 ease-in-out"
            :class="company.selected ? 'opacity-100' : 'opacity-0'"
          ></div>
        </slide>
      </carousel>
      <div
        class="cursor-pointer flex justify-center items-center w-10 h-10 rounded-full bg-[#FFD700] absolute top-1/2 transform -translate-y-1/2 right-[16.6667%] z-[9]"
        @click="showRightArrow ? myCarousel.next() : myCarousel.prev()"
      >
        <ClientOnly>
          <fa
            class="text-3xl text-[#1D1A20] transform transition-all duration-300 ease-in-out"
            :class="showRightArrow ? 'rotate-0' : 'rotate-180'"
            :icon="['fas', 'arrow-right']"
          />
        </ClientOnly>
      </div>
    </div>
  </div>
</template>

<style scoped>
.heading-one {
  @apply text-[#FDB21D] text-3xl leading-[40px] md:text-[40px] md:leading-[55px] md:font-bold font-semibold;
}

/* for page */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.3s ease-in-out;
}
.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}
</style>

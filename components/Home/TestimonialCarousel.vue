<script setup>
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { Carousel, Slide } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

const breakpoints = useBreakpoints(breakpointsTailwind);

const isDesktop = breakpoints.greaterOrEqual("lg");
// const carousel = ref(null);
const companies = ref([
  {
    companyName: "ERPGAP",
    ownerName: "<PERSON><PERSON>",
    image: "/home/<USER>/diago_Duarte.webp",
    position: "CTO",
    title: "",
    description:
      "We worked with Devxhub to do Nuxt.js and Astro.build development and they are very responsive and professional. I highly recommend working with Devxhub Team",
  },
  {
    companyName: "Maat K12 & NowMeta IO",
    ownerName: "<PERSON> Evans",
    image: "/images/assets/img/blank-profile-picture.png",
    position: "Founder & CEO",
    title: "",
    description:
      "As a business owner in the USA, finding the right software solutions is critical for our growth and development. It was in the midst of this search that we discovered Devxhub, a software company that completely redefined our understanding of innovation, efficiency, and service excellence.<br>We couldn’t be more delighted with our decision to partner with Devxhub. They’ve proven themselves to be an asset to our operations, a trusted advisor, and an indispensable partner in our continued growth and success. To any company searching for software solutions that drive results, we highly recommend Devxhub. They are the embodiment of technological innovation and customer-centric service.",
  },
  {
    companyName: "Iscaneo",
    // ownerName: "Fabien Keller",
    image: "/images/assets/img/blank-profile-picture.png",
    position: "Ex-CEO",
    title: "",
    description:
      "Working with Devxhub was an amazing experience and a very smart move for our company. Great deliveries, fantastic experience with the team, they also have a technical expertise in many fields (web, mobile, APIs and more). An international collaboration is also possible : we’ve been working hand in hand with our teams based in Europe and Brazil. Massive thanks to the whole team.",
  },
  {
    companyName: "SharpArchive",
    // ownerName: "Chad Gordon",
    image: "/images/assets/img/blank-profile-picture.png",
    position: "CEO",
    title: "",
    description:
      "I have worked with Devxhub for three years and will continue to work with them long into the future. We have worked together from idea inception to product launch. I can’t imagine having a better partner in carrying out our vision. They have persisted through countless challenges and have a complete commitment to quality, creating my vision, and most importantly to user experience. I personally know next to nothing about tech development. Because of this, it was essential to work with a company with clear and responsive communication. They will work tirelessly for you and the technology you wish to create. As a result of this, we successfully brought our product to market only one year after we first started working with them. We are now growing our user base and expect to be profitable within one year. This would not have been possible without Devxhub. We highly recommend them to anyone looking to bring their ideas into reality.",
  },
]);
const myCarousel = ref(null);
const show = ref(false);
onMounted(() => {
  setTimeout(() => {
    show.value = true;
  }, 1000);
});
// const breakpoints = {
//   // 300px and up
//   1024: {
//     itemsToShow: 2,
//     snapAlign: "start",
//   },
//   // 400px and up
//   200: {
//     itemsToShow: 1,
//     snapAlign: "start",
//   },
// };
</script>

<template>
  <div class="testimonial_container">
    <div
      class="testimonial_containe_inner container-fluid relative flex justify-center"
    >
      <div class="max-w-[1064px]">
        <div class="flex items-center justify-start space-x-[9px]">
          <h3
            class="text-left text-[#f1f1f2] instrument-italic md:text-4xl md:leading-[48px] text-2xl"
          >
            Testimonial
          </h3>
          <div class="w-13 h-[1px] bg-[#1D1A20]"></div>
        </div>
        <h3
          class="md:mb-[53px] mb-5 text-[#F1F1F2] text-left md:text-4xl md:leading-[48px] text-2xl font-bold"
        >
          <span class="text-[#999]">What</span>
          <span class="instrument-italic"> People Think</span>
          <span class="text-[#999]"> of</span>
          <span class="instrument-italic"> Us</span>
        </h3>

        <!-- 
      :paginationSize="8"
          paginationColor="#BABABA"
          paginationActiveColor="#FAAF04"
          :navigationEnabled="false"
          :loop="true"
          :centerMode="true"
          :autoplay="0"
          :wrapAround="true"
          :perPageCustom="[[320, 2]]" -->
        <!-- :autoplay="off"
          :items-to-show="isDesktop? 1 : 1"
          :snapAlign="isDesktop ? 'center' : 'center'" -->
        <div class="relative">
          <carousel
            ref="myCarousel"
            :autoplay="5000"
            :wrap-around="true"
            :items-to-show="isDesktop ? 2 : 1"
            :pauseAutoplayOnHover="true"
            snapAlign="start"
          >
            <slide
              class="flex flex-col !justify-start"
              v-for="(company, index) in companies"
              :key="index"
            >
              <div class="testimonial-card max-w-[96%]">
                <HomeTestimonialsData
                  :images="company.image"
                  :companyName="company.companyName"
                  :ownerName="company.ownerName"
                  :position="company.position"
                  :title="company.title"
                  :description="company.description"
                />
              </div>
            </slide>
          </carousel>
          <div
            class="flex flex-nowrap justify-end space-x-5 md:mt-16 mt-8 pr-5"
          >
            <div class="arrow-icon cursor-pointer" @click="myCarousel.prev()">
              <ClientOnly>
                <fa class="text-2xl" :icon="['fas', 'arrow-left']" />
              </ClientOnly>
            </div>
            <div class="arrow-icon cursor-pointer" @click="myCarousel.next()">
              <ClientOnly>
                <fa class="text-2xl" :icon="['fas', 'arrow-right']" />
              </ClientOnly>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.VueCarousel-slide {
  @apply rounded-md bg-transparent h-full pt-5;
  position: relative;
  text-align: center;
  justify-content: end;
}
.arrow-icon {
  // background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  // /* Praimary Color */

  // border: 1px solid #fdb21d;
  /* box-shadow: 0px 16px 42px rgba(0, 30, 108, 0.3); */
  @apply rounded-full flex justify-center items-center;
  width: 40px;
  height: 40px;
  background-color: white;
  color: black;
  // padding: 16px;
  box-shadow: 6px 10px 20px #22283133;
}
@media (max-width: 767px) {
  .arrow-icon {
    width: 40px;
    height: 40px;
  }
}

@media screen and (min-width: 1024px) {
  .testimonial-card {
    max-width: 502px !important;
    padding-right: 20px;
  }
}
@media screen and (min-width: 1280px) {
  .testimonial-card {
    max-width: 522px !important;
    padding-right: 20px;
  }
}
.testimonial_containe_inner {
  // border-radius: 30px;
  // background: linear-gradient(145deg, #2a1e56 11.97%, #3d2c79 89.93%);
  // box-shadow: 0px 16px 42px 0px rgba(0, 30, 108, 0.3);
}
</style>

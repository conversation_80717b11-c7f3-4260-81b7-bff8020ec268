<script setup>
defineProps({
  isMobile: {
    default: false
  }
});
</script>

<template>
  <div :class="[
    'flex items-center',
    isMobile ? 'justify-center space-x-3 mb-4' : 'space-x-4 md:pt-[64px]'
  ]">
    <div class="flex justify-center items-center space-x-2 md:w-[72px] w-14 md:h-8 h-6">
      <img
        src="/public/landing/icons/clutch.svg"
        loading="lazy"
        alt="clutch"
        class="rounded-[10px] w-full h-full"
      />
    </div>
    <HomeStarRating 
      :size="isMobile ? 'sm' : 'base'"
      :rating="5.0"
    />
  </div>
</template>
<script setup>
const viewPortController1 = () => {
  const animatedTextOne = document.getElementById("animatedTextOne");
  const animatedTextTwo = document.getElementById("animatedTextTwo");

  // First Observer
  const observer = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        } else {
          entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  // Second Observer
  const observerTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        } else {
          entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  observer.observe(animatedTextOne);
  // Start observing with the first observer
  observerTwo.observe(animatedTextTwo);
};

onMounted(() => {
  viewPortController1();
});
</script>

<template>
  <div>
    <div
      class="flex min-[992px]:flex-row flex-col justify-between space-y-4 min-[992px]:space-y-0"
    >
      <div class="whitespace-nowrap overflow-hidden sixth-h-tag">
        <p id="animatedTextOne" class="font-bold text-[#999]">Devxhub</p>
        <p
          id="animatedTextTwo"
          class="font-bold instrument-italic text-[#f1f1f2]"
        >
          Working Process
        </p>
      </div>
      <p
        class="min-[992px]:mt-1 xl:text-2xl md:text-[22px] md:leading-[32px] text-xl font-medium text-[#999] min-[992px]:max-w-[416px] w-full"
      >
        Our process is simple: we begin by understanding your needs, conducting
        analysis and research, testing our solutions in real-world scenarios,
        and then developing, implementing, and refining them based on your
        feedback.
      </p>
    </div>
    <div
      class="four-grid-item mt-16 min-[992px]:mt-20 min-[992px]:ml-[22px] min-[800px]:ml-[15px] md:ml-[-8px]"
    >
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="work-single-item-image"
          src="/public/landing/howwework/1.svg"
          alt="Validate the Idea"
        />
        <p class="work-single-item-title">Validate the Idea</p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="work-single-item-image"
          src="/public/landing/howwework/2.svg"
          alt="Develop & Implement"
        />
        <p class="work-single-item-title">Develop & Implement</p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="work-single-item-image"
          src="/public/landing/howwework/3.svg"
          alt="Deploy & Go-Live"
        />
        <p class="work-single-item-title">Deploy & Go-Live</p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="work-single-item-image"
          src="/public/landing/howwework/4.svg"
          alt="Continuous Improvement"
        />
        <p class="work-single-item-title">Continuous Improvement</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.four-grid-item {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.work-single-item-image {
  @apply mb-2.5 min-w-[60px] w-[60px] h-[60px] xl:h-20 xl:w-20;
}
.work-single-item-title {
  @apply xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-medium;
}
.work-single-item {
  text-align: center;
  border: 1px solid #d6dce1;
  border-radius: 128px;
  margin-left: -22px;
  padding: 36px 20px 36px;
}

@media screen and (max-width: 991px) {
  .work-single-item {
    margin-left: -15px;
    padding: 20px;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
    grid-template-columns: 1fr 1fr;
    margin-right: 0;
  }
  .work-single-item {
    margin-left: 0;
  }
}

@media screen and (max-width: 479px) {
  .work-single-item {
    padding: 60px 20px 60px;
  }
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
  }
}

#animatedTextOne {
  transition: all 0.5s ease-in-out;
  transform: translateX(-6vw);
}
#animatedTextTwo {
  transition: all 0.5s ease-in-out;
  transform: translateX(6vw);
}

#animatedTextOne.final {
  transform: translateX(0vw);
}
#animatedTextTwo.final {
  transform: translateX(0vw);
}
</style>

<script setup lang="ts"></script>

<template>
  <div class="w-full flex flex-col">
    <div
      class="max-w-[160px] w-full h-[30px] border boeder-[#ffffff4d] rounded-full py-1.5 px-[13px] space-x-2 flex justify-center items-center"
    >
      <img
        src="/public/blogs/clutch-white-2.svg"
        loading="lazy"
        alt=""
        class="clutch-logo h-[11px]"
      />
      <div class="flex space-x-0.5 text-[#FFD700]">
        <BaseIconStar class="size-2.5" />
        <BaseIconStar class="size-2.5" />
        <BaseIconStar class="size-2.5" />
        <BaseIconStar class="size-2.5" />
        <BaseIconStar class="size-2.5" />
      </div>
      <p class="text-base font-bold text-[#ffffffb3]">5.0</p>
    </div>
    <h3 class="third-h-tag !text-white mt-5">
      Unlock your new development team today.
    </h3>
    <p class="text-sm text-white font-bold opacity-70 mt-5">
      If you have a hard time finding the right development team or software
      solutions
    </p>
    <NuxtLink
      to="/contact-us#appointment"
      class="w-full h-[46px] text-[#1D1A20] text-lg font-medium mt-10 bg-[#FFD700] rounded-full leading-[46px] text-center"
      >Book a free call</NuxtLink
    >
  </div>
</template>

<style scoped></style>

<script setup lang="ts">
import { nextTick, onUnmounted } from "vue";
// import { useBlogStore } from "~/stores/pages/blog/blog";
import type { Categories } from "~/types/posts";
// const { setSearchBlogsPost } = useBlogStore();
const route = useRoute();

const { data: categories } = useFetch<Categories>("/api/blogs/categories");

const searchBySlug = (searchText: string) => {
  // setSearchBlogsPost(searchText);
  categories.value?.forEach((category, index) => {
    if (category.slug === searchText) {
      const item = document.getElementById(`${category.id}`);
      item?.classList.add("active");
    } else {
      const item = document.getElementById(`${category.id}`);
      item?.classList.remove("active");
    }
  });
};
const categoryHeight = ref("0px");
const categoryList = ref(null);
const getCategoryHeight = () => {
  setTimeout(() => {
    if (categoryList.value) {
      categoryHeight.value = categoryList.value.offsetHeight;
    }
  }, 1000);
};
onMounted(async () => {
  await nextTick();
  setTimeout(() => {
    const rightArrowButton = document.querySelector(".owl-prev");
    rightArrowButton.style.display = "none";
    getAllItemsWidth();
    window.addEventListener("resize", getAllItemsWidth);
  }, 1000);

  if (route.fullPath.includes("/blog/?category")) {
    searchBySlug(route.fullPath.replace("/blog/?category=", ""));
  }
  window.addEventListener("resize", getCategoryHeight);
  categoryList.value = document.getElementById("categoryList") as HTMLElement;
  getCategoryHeight();
});
onUnmounted(() => {
  window.removeEventListener("resize", getAllItemsWidth);
});
const seeMore = ref<boolean>(false);
const slideValue = ref(0);
const itemsContainerWidth = ref(0);

const getAllItemsWidth = () => {
  itemsContainerWidth.value = 0;
  const allItems = document.querySelectorAll(".owl-item");
  allItems.forEach((item) => {
    const widthWithMargin = (item as HTMLElement).offsetWidth + 10;
    itemsContainerWidth.value = itemsContainerWidth.value + widthWithMargin;
  });
  const windowWidth = document.querySelector(".owl-stage-outer") as HTMLElement;
  itemsContainerWidth.value =
    itemsContainerWidth.value - (windowWidth.offsetWidth - 45);
};

const slideleft = () => {
  const owlStage = document.querySelector(".owl-stage");
  const slideAmount = 67;

  slideValue.value = slideValue.value + slideAmount;
  if (slideValue.value <= itemsContainerWidth.value) {
    if (owlStage) {
      const rightArrowButton = document.querySelector(
        ".owl-prev"
      ) as HTMLElement;
      rightArrowButton.style.display = "block";
      (
        owlStage as HTMLElement
      ).style.transform = `translate3d(-${slideValue.value}px, 0px, 0px)`;
    }
  } else {
    slideValue.value = slideValue.value - slideAmount;
    const rightArrowButton = document.querySelector(".owl-next") as HTMLElement;
    rightArrowButton.style.display = "none";
  }
};

const slideright = () => {
  const owlStage = document.querySelector(".owl-stage");
  const slideAmount = 67;

  slideValue.value = slideValue.value - slideAmount;
  if (slideValue.value != 0) {
    if (owlStage) {
      const rightArrowButton = document.querySelector(
        ".owl-next"
      ) as HTMLElement;
      rightArrowButton.style.display = "block";
      (
        owlStage as HTMLElement
      ).style.transform = `translate3d(-${slideValue.value}px, 0px, 0px)`;
    }
  } else {
    (
      owlStage as HTMLElement
    ).style.transform = `translate3d(-${slideValue.value}px, 0px, 0px)`;
    const rightArrowButton = document.querySelector(".owl-prev") as HTMLElement;
    rightArrowButton.style.display = "none";
  }
};
</script>

<template>
  <div class="blog-archive-filter">
    <div class="owl-carousel owl-theme owl-loaded owl-drag">
      <div class="owl-stage-outer">
        <div class="owl-stage transition-all duration-200 ease-linear">
          <div
            :id="category.id"
            class="owl-item"
            v-for="(category, index) in categories"
            :key="index"
            @click="searchBySlug(category.slug)"
          >
            <div class="item whitespace-nowrap">
              <NuxtLink
                :to="{
                  path: '/blog/category/' + category.slug,
                }"
                :class="[
                  route.params?.slug === category?.slug &&
                  route.name === 'blog-category-slug'
                    ? '!bg-[#ffd700]'
                    : '',
                ]"
                >{{ category?.name }}</NuxtLink
              >
            </div>
          </div>
        </div>
      </div>
      <div class="owl-nav">
        <button class="owl-prev" @click="slideright" aria-label="Previous">
          <div class="owl-prev-item flex justify-center items-center">
            <BaseIconRightArrow extraClass="text-2xl" icon="chevron-left" />
          </div>
        </button>
        <button class="owl-next" @click="slideleft" aria-label="Next">
          <div class="owl-next-item flex justify-center items-center">
            <BaseIconRightArrow extraClass="text-2xl" icon="chevron-right" />
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.owl-carousel {
  height: 48px;
  overflow: hidden;
  border-radius: 30px;
  width: 100%;
  z-index: 1;
}
.owl-carousel,
.owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}
.owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}
.owl-stage {
  touch-action: manipulation;
  display: flex;
}
.owl-item {
  touch-action: pan-y;
  user-select: none;
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  position: relative;
  margin-right: 10px;
  width: auto;
}
.owl-carousel .item a {
  padding: 13px 20px;
  background: #e0e0e0;
  border-radius: 30px;
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.25;
  color: #1d1a20;
  transition: all 0.3s ease-in-out;
}
.owl-carousel .active .item a {
  background-color: #ffd700;
}
.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev,
.owl-carousel button.owl-dot {
  background: 0 0;
  color: inherit;
  border: none;
  padding: 0 !important;
  font: inherit;
}
.owl-carousel .owl-nav .owl-prev.disabled,
.owl-carousel .owl-nav .owl-next.disabled {
  display: none;
}
.owl-carousel .owl-nav .owl-prev {
  left: 0;
}
.owl-carousel .owl-nav .owl-next {
  right: 0;
}

.owl-carousel .owl-nav .owl-prev .owl-prev-item,
.owl-carousel .owl-nav .owl-next .owl-next-item {
  background-color: #999 !important;
  color: #000;
  font-size: 16px !important;
  height: 46px;
  width: 46px;
  line-height: 46px;
  border-radius: 50%;
}
@media (min-width: 310px) and (max-width: 450px) {
  .owl-prev:after,
  .owl-next:after {
    background: none !important;
  }
}
.owl-carousel .owl-nav .owl-prev:after {
  content: "";
  position: absolute;
  top: 0;
  left: 20px;
  width: 80px;
  height: 100%;
  background: linear-gradient(
    90deg,
    #f2f2f3 59.2%,
    rgba(242, 242, 243, 0.32) 111.49%
  );
  filter: blur(9px);
  z-index: -1;
}
.owl-carousel .owl-nav .owl-next:after {
  content: "";
  position: absolute;
  top: 0;
  right: 20px;
  width: 80px;
  height: 100%;
  background: linear-gradient(
    270deg,
    #f2f2f3 59.2%,
    rgba(242, 242, 243, 0.32) 157.92%
  );
  filter: blur(9px);
  z-index: -1;
}
.owl-carousel .owl-nav .owl-prev,
.owl-carousel .owl-nav .owl-next {
  position: absolute;
  top: 0;
  background-color: #cdcdcd !important;
  height: 46px;
  width: 46px;
  margin: 0;
  cursor: pointer;
  color: #000;
  border-radius: 50%;
  z-index: 2;
}
</style>

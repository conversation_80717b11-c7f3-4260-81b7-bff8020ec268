<script setup lang="ts">
import type { Post } from "~/types/posts";

defineProps({
  posts: {
    type: Array as PropType<Post[]>,
    default: () => [],
  },
});
</script>

<template>
  <div class="space-y-16">
    <BlogsCategoriesTab />
    <div class="four-grid-item">
      <NuxtLink
        :to="`/blog/${post.slug}`"
        class="h-full flex flex-col"
        v-for="(post, index) in posts"
        :key="index"
      >
        <BlogsCard :post="post" />
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
.inout-enter-active,
.inout-leave-active {
  transition: all 0.5s ease-in-out;
}
.inout-enter-from,
.inout-leave-to {
  opacity: 0;
  scale: 0.5;
}
.four-grid-item {
  grid-column-gap: 20px;
  grid-row-gap: 64px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 24px;
    grid-row-gap: 60px;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }
}
@media (max-width: 639px) {
  .four-grid-item {
    grid-column-gap: 0px;
    grid-row-gap: 60px;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }
}
</style>

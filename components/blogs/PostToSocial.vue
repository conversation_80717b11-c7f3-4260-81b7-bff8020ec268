<script setup lang="ts">
const route = useRoute();
const postUrl = ref("");

onMounted(() => {
  postUrl.value = `https://devxhub.com${route.fullPath}`;
});
</script>

<template>
  <div>
    <NuxtLink
      :to="`https://www.facebook.com/sharer/sharer.php?u=${postUrl}`"
      target="_blank"
      rel="noopener noreferrer"
      class="flex justify-center items-center text-[#000] bg-[#D9D9D9] w-11 h-11 rounded-full"
      ><img class="size-4" src="/public/blogs/facebook.svg" alt="Facebook"
    /></NuxtLink>
    <NuxtLink
      class="flex justify-center items-center text-[#000] bg-[#D9D9D9] w-11 h-11 rounded-full"
      :to="`https://www.linkedin.com/shareArticle?mini=true&url=${postUrl}`"
      target="_blank"
      rel="noopener noreferrer"
      ><img class="size-4" src="/public/blogs/LinkedIn.svg" alt="LinkedIn"
    /></NuxtLink>
    <NuxtLink
      class="flex justify-center items-center text-[#000] bg-[#D9D9D9] w-11 h-11 rounded-full"
      :to="`https://twitter.com/intent/tweet?url==${postUrl}`"
      target="_blank"
      rel="noopener noreferrer"
      ><img class="size-4" src="/public/blogs/x.svg" alt="Twitter"
    /></NuxtLink>
  </div>
</template>

<style scoped></style>

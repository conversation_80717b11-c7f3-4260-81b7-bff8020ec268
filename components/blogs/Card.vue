<script setup lang="ts">
import defaultImage from "~/assets/img/blog/image-not-found.webp";
import type { Post } from "~/types/posts";

defineProps({
  post: {
    type: Object as PropType<Post>,
    required: true,
  },
  titelClass: {
    type: String,
    default: "text-[#F1F1F2]",
  },
});
const dateFormat = (date: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  return new Date(date).toLocaleDateString(undefined, options);
};
</script>

<template>
  <div class="flex flex-col space-y-5 w-full h-full">
    <div
      class="flex-grow overflow-hidden relative rounded-[10px] max-h-[500px] image-container"
    >
      <img
        class="w-full h-auto aspect-[413/275] blog-image object-cover transform scale-100 rotate-0 transition-all duration-300 ease-in-out"
        :src="post?.image ? post.image : defaultImage"
        :alt="post?.title"
      />
      <div class="dm-post__overlay-read">Read</div>
    </div>
    <div class="flex flex-col space-y-5">
      <div class="dm-post__category-lists w-auto">
        {{ post?.categories[0].name }}
      </div>
      <h2 class="text-2xl font-medium line-clamp-1" :class="titelClass">
        {{ post?.title }}
      </h2>
    </div>
  </div>
</template>

<style scoped lang="scss">
.bg-card {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
}
.dm-post__overlay-read {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  border-radius: 50%;
  border: 1px solid #b8b8b8;
  background: rgba(39, 39, 39, 0.5);
  backdrop-filter: blur(5px);
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  letter-spacing: -0.32px;
}
.image-container:hover .dm-post__overlay-read {
  opacity: 1;
}
.image-container:hover .blog-image {
  transform: scale(1.05) rotate(3deg);
}
.dm-post__category-lists {
  background: #833ab4;
  display: block;
  padding: 8px 20px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 30px;
  color: #f1f1f2;
  line-height: 24px;
  width: fit-content;
}
</style>

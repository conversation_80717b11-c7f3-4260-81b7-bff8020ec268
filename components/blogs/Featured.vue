<script setup lang="ts">
import defaultImage from "~/assets/img/blog/image-not-found.webp";
import type { Post } from "~/types/posts";

const { data: featured } = useFetch<Post[]>("/api/blogs/featured");
const dateFormat = (date: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  return new Date(date).toLocaleDateString(undefined, options);
};
const featuredBlogPost = ref({
  img: "/blogs/featured-image.png",
  label: "",
  title: "",
  description: "",
});
</script>
<template>
  <div class="feature-shadow w-full rounded-xl bg-white max-w-[1280px] mx-auto">
    <div class="w-full">
      <NuxtLink
        :to="`/blog/${featured?.[0]?.slug}`"
        class="featured-blog-item-wrap items-center"
      >
        <div class="blog-post-wrap">
          <div class="blog-left-wrap">
            <div class="blog-image-wrap rounded-lg">
              <img
                class="xl:min-h-[261px] min-h-[200px] object-cover transition-all duration-700 ease-in-out transform translate-x-0 translate-y-0 translate-z-0 scale-100 hover:scale-[1.07] rotate-x-0 rotate-y-0 rotate-z-0 skew-x-0 skew-y-0 transform-style-preserve-3d"
                :src="featured?.[0]?.image ? featured[0].image : defaultImage"
                :alt="featured?.[0]?.title"
              />
            </div>
          </div>
        </div>
        <div class="flex flex-col space-y-5">
          <div
            v-if="featured?.[0]?.categories?.[0]?.name"
            class="w-fit h-10 whitespace-nowrap rounded-full px-5 py-2 bg-[#9999994D] !text-[#1D1A20] text-base font-medium"
          >
            {{ featured[0].categories[0].name }}
          </div>
          <div class="flex flex-col space-y-2.5">
            <h2 class="fifth-h-tag !text-[#1D1A20] !font-medium line-clamp-4">
              {{ featured?.[0]?.title }}
            </h2>
            <p class="third-p-tag !text-[#1D1A20] line-clamp-3">
              {{ featured?.[0]?.description }}
            </p>
          </div>
          <div class="!mt-10 flex items-center space-x-2">
            <div class="text-base font-medium !text-[#1D1A20]">View more</div>
            <BlogsFeaturedRightIcon />
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
.border-bottom {
  @apply absolute top-8 sm:top-8 md:top-8 lg:top-10 w-[6.5rem] sm:w-24 md:w-32 lg:w-32 xl:w-40 h-[2.5px] bg-orange mt-[2px];
}
.bg-feature {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
}
/* .feature-shadow {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
} */
.feature {
  background-image: url("~/assets/img/blog/desktop.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.featured-blog-item-wrap {
  grid-column-gap: 50px;
  grid-row-gap: 50px;
  border-radius: 12px;
  grid-template-rows: auto;
  grid-template-columns: 1.2fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center;
  padding: 24px 24px 24px 24px;
  display: grid;
}
.blog-post-wrap {
  grid-column-gap: 9.375vw;
  color: #000;
  flex-flow: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  text-decoration: none;
  display: flex;
  position: relative;
}
.blog-left-wrap {
  position: relative;
}
.blog-image-wrap {
  border-radius: 6px;
  height: 100%;
  position: relative;
  overflow: hidden;
}
@media (max-width: 991px) {
  .featured-blog-item-wrap {
    grid-column-gap: 50px;
    grid-row-gap: 50px;
    grid-template-columns: 1fr;
  }
}
</style>

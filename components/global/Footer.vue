<script setup>
const route = useRoute();
const copyrightDate = computed(() => new Date().getFullYear());
const showModal = ref(false);

// Function to handle navigation to the same page
const handleSamePageNavigation = (path) => {
  if (route.path === path) {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
};

onMounted(() => {
  // setTimeout(() => {
  //   const allFooter = document.querySelectorAll(".footerSectionDiv");
  //   console.log(allFooter, "here all");
  //   const observer = new window.IntersectionObserver((entries) => {
  //     entries.forEach((singleDiv) => {
  //       if (singleDiv.isIntersecting) {
  //         singleDiv.target.classList.remove("notInViewPort");
  //         singleDiv.target.classList.add("nowInViewPort");
  //       } else if (!singleDiv.isIntersecting) {
  //         singleDiv.target.classList.add("notInViewPort");
  //         singleDiv.target.classList.remove("nowInViewPort");
  //       }
  //     });
  //   });
  //   allFooter.forEach((item) => {
  //     observer.observe(item);
  //   });
  // }, 700);
  setTimeout(() => {
    generateBalls();
  }, 700);

  window.addEventListener("resize", function (e) {
    var balls = document.querySelectorAll(".gooey-animations .ball");
    balls.forEach(function (ball) {
      ball.remove();
    });
    generateBalls();
  });
});
// methods
const generateBalls = () => {
  var gooeyAnimations = document.querySelector(".gooey-animations");
  if (gooeyAnimations) {
    var windowWidth = window.innerWidth;
    var ballCount = Math.floor(windowWidth / 20);
    var colors = ["#28323B", "#FFA036"];

    for (var i = 0; i < ballCount; i++) {
      var ball = document.createElement("div");
      ball.classList.add("ball");
      gooeyAnimations.appendChild(ball);
      ball.style.position = "absolute";
      ball.style.bottom = "0px";
      ball.style.left = Math.random() * (windowWidth - 100) + "px";
      ball.style.animationDelay = Math.random() * 5 + "s";
      ball.style.transform = "translateY(" + Math.random() * 10 + "px)";
      ball.style.backgroundColor = colors[i % 2];
    }
  }
};
const countryAddresses = [
  {
    image: "/contacts/bangladesh.svg",
    name: "Bangladesh",
    address: "/contacts/address.svg",
    street: "158/27, Boalia, Kazla,",
    country: "Rajshahi-6204, Bangladesh",
    phone: "/contacts/call.svg",
    number: "+880 1326 506464",
  },
  {
    image: "/contacts/usa.svg",
    name: "USA",
    address: "/contacts/address.svg",
    street: "903 1st Street North #1061 ",
    country: "Hopkins, MN 55343, United States",
    phone: "/contacts/call.svg",
    number: "****** 300-7711",
  },
  {
    image: "/contacts/finland.svg",
    name: "Finland",
    address: "/contacts/address.svg",
    street: "Viulutie 1 A 1, 00420",
    country: "Helsinki, Uusimaa, Finland",
    phone: "/contacts/call.svg",
    number: "+358 402545717",
  },
];
</script>

<template>
  <section>
    <template
      v-if="
        ['work', 'our-working-process', 'blog', 'career'].includes(route.name)
      "
    >
      <!-- <ServicesOurBenefits class="container-fluid md:pt-40 pt-20" /> -->
      <!-- <HomeFaq /> -->
    </template>
    <HomeOurSolution
      class="footerSectionDiv"
      v-if="$route.name !== 'contact-us' && !$route.name.includes('checklist')"
    />
    <Modal :showModal="showModal" @closeModal="showModal = false" />

    <div class="relative footerSectionDiv flex flex-col justify-end h-auto">
      <!-- <img
        src="/home/<USER>/footerBg.svg"
        class="h-full w-full hidden md:block"
        alt=""
      />
      <img
        src="/home/<USER>/footerBgMobile.svg"
        class="h-full w-full block md:hidden"
        alt=""
      /> -->
      <!-- <footer>
        <div class="gooey-animations"></div>

        <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
          <defs>
            <filter id="goo">
              <feGaussianBlur
                in="SourceGraphic"
                stdDeviation="15"
                result="blur"
              />
              <feColorMatrix
                in="blur"
                mode="matrix"
                values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 15 -7"
                result="goo"
              />
              <feBlend in="SourceGraphic" in2="goo" />
            </filter>
          </defs>
        </svg>

        <svg viewbox="0 0 1440 328" width="100vw">
          <defs>
            <clipPath
              id="wave"
              clipPathUnits="objectBoundingBox"
              transform="scale(0.00069444444, 0.00304878048)"
            >
              <path
                d="M504.452 27.7002C163.193 -42.9551 25.9595 38.071 0 87.4161V328H1440V27.7002C1270.34 57.14 845.711 98.3556 504.452 27.7002Z"
              />
            </clipPath>
          </defs>
        </svg>
      </footer> -->
      <div class="footerSection bg-[#1A1139] relative bottom-0 w-full">
        <div class="mx-auto">
          <div class="relative md:pt-[180px] pt-8">
            <div
              class="flex flex-col space-y-7 bg-[#120736] md:pt-[30px] pt-[64px]"
            >
              <div
                class="container-fluid footer-top flex lg:flex-row lg:items-start items-center flex-col space-y-10 lg:space-y-0 space-x-0 2xl:space-x-26 xl:space-x-17 lg:space-x-10 justify-between bg-[#120736]"
              >
                <div
                  class="flex flex-col justify-between lg:items-start md:items-center lg:text-left md:text-center md:space-y-10 space-y-8 2xl:w-auto lg:w-auto w-full"
                >
                  <img
                    class="w-[155px] h-[30px]"
                    src="~/assets/img/site-logo-with-devxhub.webp"
                    alt="devxhub logo"
                  />
                  <div
                    class="md:max-w-[193px] md:text-base text-sm text-[#999]"
                  >
                    <p>A Leading Software Development Company</p>
                  </div>
                  <NuxtLink
                    to="/contact-us"
                    aria-label="Contact Us Now"
                    class="hero_section_carousal_aeonik font-medium py-3 px-[29px] w-[180px] text-[#1D1A20] text-center bg-[#FFD700] rounded-full"
                    @click="handleSamePageNavigation('/contact-us')"
                  >
                    Contact Us Now
                  </NuxtLink>
                </div>
                <div
                  class="md:flex flex-col lg:items-start lg:text-left text-center space-y-4 lg:w-auto w-full whitespace-nowrap hidden"
                >
                  <p class="text-[#F1F1F2] text-base font-bold uppercase mt-1">
                    Services
                  </p>
                  <ul
                    class="flex flex-col space-y-[12px] text-base text-[#999]"
                  >
                    <li>
                      <NuxtLink
                        to="/services/it-staff-augmentation"
                        @click="
                          handleSamePageNavigation(
                            '/services/it-staff-augmentation'
                          )
                        "
                        >IT Staff Augmentation</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/full-stack-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/full-stack-development'
                          )
                        "
                        >Full Stack Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/mobile-application-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/mobile-application-development'
                          )
                        "
                        >Mobile Application Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/custom-software-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/custom-software-development'
                          )
                        "
                        >Custom & Enterprise Software Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/mvp-saas-end-to-end-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/mvp-saas-end-to-end-development'
                          )
                        "
                      >
                        MVP, SaaS, End-to-End Development
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/ai-ml-development-integration"
                        @click="
                          handleSamePageNavigation(
                            '/services/ai-ml-development-integration'
                          )
                        "
                      >
                        AI/ML Development & Integration
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/devops-cloud-solutions"
                        @click="
                          handleSamePageNavigation(
                            '/services/devops-cloud-solutions'
                          )
                        "
                      >
                        DevOps & Cloud Solutions
                      </NuxtLink>
                    </li>
                    <!-- <li>
                      <NuxtLink to="/ui-ux-design"
                        >UI/UX, 3D Animations
                      </NuxtLink>
                    </li> -->
                  </ul>
                </div>

                <div
                  class="md:flex md:flex-col lg:items-start md:items-center lg:text-left text-center space-y-4 lg:w-auto w-full whitespace-nowrap hidden"
                >
                  <p
                    class="text-[#F1F1F2] text-base text-left font-bold uppercase mt-1"
                  >
                    Company
                  </p>
                  <ul
                    class="flex flex-col space-y-[12px] md:text-base text-[14px] text-[#999]"
                  >
                    <li>
                      <NuxtLink
                        to="/services"
                        @click="handleSamePageNavigation('/services')"
                      >
                        Services
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/about-us"
                        @click="handleSamePageNavigation('/about-us')"
                      >
                        About us
                      </NuxtLink>
                    </li>
                    <!-- <li>
                      <NuxtLink to="/our-clients">Clients </NuxtLink>
                    </li> -->
                    <li>
                      <NuxtLink
                        to="/our-working-process"
                        @click="
                          handleSamePageNavigation('/our-working-process')
                        "
                        >Our process
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/career"
                        @click="handleSamePageNavigation('/career')"
                        >Career
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/blog"
                        @click="handleSamePageNavigation('/blog')"
                        >Blog
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/contact-us#appointment"
                        @click="
                          handleSamePageNavigation('/contact-us#appointment')
                        "
                        >Schedule a Meeting
                      </NuxtLink>
                    </li>
                    <li>
                      <NuxtLink
                        to="/products"
                        @click="handleSamePageNavigation('/products')"
                        >Products
                      </NuxtLink>
                    </li>
                    <!-- <li>
                      <NuxtLink to="/faq">Service Provider FQA's </NuxtLink>
                    </li> -->
                  </ul>
                </div>
                <div
                  class="md:flex flex-col lg:items-start md:items-center lg:text-left md:text-center space-y-4 lg:w-auto w-full whitespace-nowrap hidden"
                >
                  <p
                    class="text-[#F1F1F2] text-base font-bold uppercase mt-1 text-left"
                  >
                    HIRE SPECIALIZED TALENT
                  </p>

                  <ul
                    class="flex flex-col space-y-[12px] md:text-base md:text-center lg:text-left text-left text-[14px] text-[#999]"
                  >
                    <li>
                      <p>Frontend Developer</p>
                    </li>
                    <li>
                      <p>Backend Developer</p>
                    </li>
                    <li>
                      <p>App Developer</p>
                    </li>
                    <li>
                      <p>MERN STACK Developer</p>
                    </li>
                    <li>
                      <p>Java Developer</p>
                    </li>
                    <li>
                      <p>.NET Core Developer</p>
                    </li>
                    <li>
                      <p>Software Architect</p>
                    </li>
                    <li>
                      <p>AI/ML Engineer</p>
                    </li>
                    <li>
                      <p>DevOps/Cloud Engineer</p>
                    </li>
                    <!-- <li>
                      <NuxtLink to="/faq">Service Provider FQA's </NuxtLink>
                    </li> -->
                  </ul>
                </div>
                <div
                  class="md:hidden flex items-start justify-between gap-[59px]"
                >
                  <div
                    class="flex flex-col lg:items-start md:items-center lg:text-left text-left space-y-4 lg:w-auto w-full whitespace-nowrap"
                  >
                    <p
                      class="text-[#F1F1F2] text-base text-left font-bold uppercase mt-1"
                    >
                      Company
                    </p>
                    <ul
                      class="flex flex-col space-y-[12px] md:text-base text-[14px] text-[#999]"
                    >
                      <li>
                        <NuxtLink
                          to="/services"
                          @click="handleSamePageNavigation('/services')"
                        >
                          Services
                        </NuxtLink>
                      </li>
                      <li>
                        <NuxtLink
                          to="/about-us"
                          @click="handleSamePageNavigation('/about-us')"
                        >
                          About us
                        </NuxtLink>
                      </li>
                      <!-- <li>
                      <NuxtLink to="/our-clients">Clients </NuxtLink>
                    </li> -->
                      <li>
                        <NuxtLink
                          to="/our-working-process"
                          @click="
                            handleSamePageNavigation('/our-working-process')
                          "
                          >Our process
                        </NuxtLink>
                      </li>
                      <li>
                        <NuxtLink
                          to="/career"
                          @click="handleSamePageNavigation('/career')"
                          >Career
                        </NuxtLink>
                      </li>
                      <li>
                        <NuxtLink
                          to="/blog"
                          @click="handleSamePageNavigation('/blog')"
                          >Blog
                        </NuxtLink>
                      </li>
                      <li>
                        <NuxtLink
                          to="/contact-us#appointment"
                          @click="
                            handleSamePageNavigation('/contact-us#appointment')
                          "
                          >Schedule a Meeting
                        </NuxtLink>
                      </li>
                      <li>
                        <NuxtLink
                          to="/products"
                          @click="handleSamePageNavigation('/products')"
                          >Products
                        </NuxtLink>
                      </li>
                      <!-- <li>
                      <NuxtLink to="/faq">Service Provider FQA's </NuxtLink>
                    </li> -->
                    </ul>
                  </div>
                  <div
                    class="flex flex-col lg:items-start items-start lg:text-left text-center space-y-4 lg:w-auto w-full md:whitespace-nowrap"
                  >
                    <p
                      class="text-[#F1F1F2] text-base font-bold uppercase mt-1 text-left"
                    >
                      HIRE SPECIALIZED TALENT
                    </p>

                    <ul
                      class="flex flex-col space-y-[12px] md:text-base text-left text-[14px] text-[#999]"
                    >
                      <li>
                        <p>Frontend Developer</p>
                      </li>
                      <li>
                        <p>Backend Developer</p>
                      </li>
                      <li>
                        <p>App Developer</p>
                      </li>
                      <li>
                        <p>MERN STACK Developer</p>
                      </li>
                      <li>
                        <p>Java Developer</p>
                      </li>
                      <li>
                        <p>.NET Core Developer</p>
                      </li>
                      <li>
                        <p>Software Architect</p>
                      </li>
                      <li>
                        <p>AI/ML Engineer</p>
                      </li>
                      <li>
                        <p>DevOps/Cloud Engineer</p>
                      </li>
                      <!-- <li>
                      <NuxtLink to="/faq">Service Provider FQA's </NuxtLink>
                    </li> -->
                    </ul>
                  </div>
                </div>
                <div
                  class="flex flex-col lg:items-start lg:text-left text-left space-y-4 lg:w-auto w-full whitespace-nowrap md:hidden"
                >
                  <p class="text-[#F1F1F2] text-base font-bold uppercase mt-1">
                    Services
                  </p>
                  <ul
                    class="flex flex-col space-y-[12px] text-[14px] text-[#999]"
                  >
                    <li>
                      <NuxtLink
                        to="/services/it-staff-augmentation"
                        @click="
                          handleSamePageNavigation(
                            '/services/it-staff-augmentation'
                          )
                        "
                        >IT Staff Augmentation</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/full-stack-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/full-stack-development'
                          )
                        "
                        >Full Stack Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/mobile-application-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/mobile-application-development'
                          )
                        "
                        >Mobile Application Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/custom-software-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/custom-software-development'
                          )
                        "
                        >Custom & Enterprise Software Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/mvp-saas-end-to-end-development"
                        @click="
                          handleSamePageNavigation(
                            '/services/mvp-saas-end-to-end-development'
                          )
                        "
                        >MVP, SaaS, End-to-End Development</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/ai-ml-development-integration"
                        @click="
                          handleSamePageNavigation(
                            '/services/ai-ml-development-integration'
                          )
                        "
                        >AI/ML Development & Integration</NuxtLink
                      >
                    </li>
                    <li>
                      <NuxtLink
                        to="/services/devops-cloud-solutions"
                        @click="
                          handleSamePageNavigation(
                            '/services/devops-cloud-solutions'
                          )
                        "
                        >DevOps & Cloud Solutions</NuxtLink
                      >
                    </li>
                    <!-- <li>
                      <NuxtLink to="/ui-ux-design"
                        >UI/UX, 3D Animations
                      </NuxtLink>
                    </li> -->
                  </ul>
                </div>
              </div>
              <hr class="h-0.5 !mb-[30px] border-[#ffffff1a] container-fluid" />
              <div
                class="container-fluid !mt-[0px] w-full flex lg:flex-row lg:items-start md:items-center flex-col space-y-10 lg:space-y-0 space-x-0 2xl:space-x-26 xl:space-x-17 lg:space-x-10 justify-between"
              >
                <div
                  class="lg:w-auto w-full flex flex-col lg:items-start md:items-center"
                  v-for="address in countryAddresses"
                  :key="address.id"
                >
                  <div class="min-h-[41px] flex items-center">
                    <img :src="address.image" :alt="address.name" />
                  </div>
                  <p class="text-[#F1F1F2] text-2xl font-bold mt-[4.65px]">
                    {{ address.name }}
                  </p>
                  <ul class="flex flex-col items-start">
                    <li
                      class="flex space-x-[7px] items-start text-[#999] mt-[15px]"
                    >
                      <img
                        class="mt-1"
                        :src="address.address"
                        :alt="address.address"
                      />
                      <div class="text-start">
                        <p>{{ address.street }}</p>
                        <p>{{ address.country }}</p>
                      </div>
                    </li>
                    <li
                      class="flex space-x-[7px] md:items-center text-[#F1F1F2] mt-[12px]"
                    >
                      <img :src="address.phone" :alt="address.phone" />
                      <p>{{ address.number }}</p>
                    </li>
                  </ul>
                </div>
              </div>
              <hr
                class="h-0.5 md:!mb-[30px] border-[#ffffff1a] container-fluid"
              />
              <div class="container-fluid bg-[#120736] !mt-0">
                <div
                  class="footer-bottom flex flex-col min-[1100px]:flex-row min-[1100px]:justify-between md:items-center min-[1100px]:space-y-0 space-y-[30px] md:pb-[30px] text-base text-[#999] text-center font-normal"
                >
                  <div
                    class="md:flex md:flex-row md:items-start items-center md:space-y-0 space-y-4 hidden"
                  >
                    <p class="leading-6">
                      &copy; {{ copyrightDate }},
                      <span
                        ><a
                          href="https://devxhub.com/"
                          rel="noopener noreferrer"
                          aria-label="Devxhub Ltd."
                          >Devxhub Limited,</a
                        ></span
                      >
                      All Rights Reserved.
                    </p>
                    <span class="px-4 md:block hidden text-white">|</span>
                    <p class="md:mt-0 mt-4 leading-6">
                      <NuxtLink
                        to="/privacy-policy"
                        @click="handleSamePageNavigation('/privacy-policy')"
                      >
                        Privacy Policy
                      </NuxtLink>
                      <span class="px-4 text-white">|</span>
                      <NuxtLink
                        to="/terms-of-use"
                        @click="handleSamePageNavigation('/terms-of-use')"
                      >
                        Terms of Use
                      </NuxtLink>
                    </p>
                  </div>
                  <div
                    class="md:flex md:flex-row flex-col !md:items-center gap-8 md:gap-auto space-x-4 hidden"
                  >
                    <div
                      style="
                        transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1)
                          rotateX(0deg) rotateY(0deg) rotateZ(0deg)
                          skew(0deg, 0deg);
                        opacity: 1;
                        transform-style: preserve-3d;
                      "
                      class="clutch-badge-new flex px-5 h-10 items-center justify-start border border-[#999] rounded-full gap-2 bg-transparent max-w-[159px] mx-auto"
                    >
                      <img
                        src="/public/landing/clutch.svg"
                        loading="lazy"
                        alt="clutch-logo"
                        class="clutch-logo w-[43px] h-[12px]"
                      />
                      <img
                        src="/public/landing/Star_1.svg"
                        loading="lazy"
                        alt=""
                        class="clutch_stars w-[13px] h-[12px]"
                      />
                      <div class="text-[#f1f1f2] text-sm font-medium">5.0</div>
                    </div>
                    <div class="flex items-center space-x-4 pt-8 md:pt-0 !pl-0">
                      <a
                        href="https://www.linkedin.com/company/devxhubcom/"
                        rel="noopener noreferrer"
                        aria-label="Devxhub LinkedIn Account"
                        target="_blank"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/linkedin.svg"
                          alt="Devxhub LinkedIn Icon"
                      /></a>
                      <a
                        href="https://github.com/devxhub"
                        rel="noopener noreferrer"
                        aria-label="Devxhub github Account"
                        target="_blank"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/github.svg"
                          alt="Devxhub github Icon"
                      /></a>
                      <a
                        href="https://www.facebook.com/devxhubcom/"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Devxhub Facebook Page"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/facebook.svg"
                          alt="Devxhub Facebook Icon"
                      /></a>
                      <a
                        href="http://youtube.com/@devxhub"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Devxhub YouTube Channel"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/youtube.svg"
                          alt="Devxhub YouTube Icon"
                      /></a>
                      <a
                        href="https://twitter.com/devxhub"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Devxhub Twitter/X Account"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/twitter.svg"
                          alt="Devxhub Twitter/X Logo"
                      /></a>
                      <!-- <a
                          href="https://www.pinterest.com/devxhub_com"
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Devxhub Pinterest Account"
                          ><img
                            class="social-icons"
                            src="/home/<USER>/pinterest.png"
                            alt="Devxhub Pinterest Icon"
                        /></a> -->
                    </div>
                  </div>
                  <div class="flex flex-col justify-items-start md:hidden">
                    <div
                      style="
                        transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1)
                          rotateX(0deg) rotateY(0deg) rotateZ(0deg)
                          skew(0deg, 0deg);
                        opacity: 1;
                        transform-style: preserve-3d;
                      "
                      class="clutch-badge-new flex px-5 h-10 items-center justify-start border border-[#999] rounded-full gap-2 bg-transparent max-w-[130px]"
                    >
                      <img
                        src="/public/landing/clutch.svg"
                        loading="lazy"
                        alt="clutch-logo"
                        class="clutch-logo w-[43px] h-[12px]"
                      />
                      <img
                        src="/public/landing/Star_1.svg"
                        loading="lazy"
                        alt=""
                        class="clutch_stars w-[13px] h-[12px]"
                      />
                      <div class="text-[#f1f1f2] text-sm font-medium">5.0</div>
                    </div>
                    <div class="flex items-center space-x-4 pt-8 md:pt-0 !pl-0">
                      <a
                        href="https://www.linkedin.com/company/devxhubcom/"
                        rel="noopener noreferrer"
                        aria-label="Devxhub LinkedIn Account"
                        target="_blank"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/linkedin.svg"
                          alt="Devxhub LinkedIn Icon"
                      /></a>
                      <a
                        href="https://github.com/devxhub"
                        rel="noopener noreferrer"
                        aria-label="Devxhub github Account"
                        target="_blank"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/github.svg"
                          alt="Devxhub github Icon"
                      /></a>
                      <a
                        href="https://www.facebook.com/devxhubcom/"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Devxhub Facebook Page"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/facebook.svg"
                          alt="Devxhub Facebook Icon"
                      /></a>
                      <a
                        href="http://youtube.com/@devxhub"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Devxhub YouTube Channel"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/youtube.svg"
                          alt="Devxhub YouTube Icon"
                      /></a>
                      <a
                        href="https://twitter.com/devxhub"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Devxhub Twitter/X Account"
                        ><img
                          class="social-icons"
                          src="/contacts/social-icon/twitter.svg"
                          alt="Devxhub Twitter/X Logo"
                      /></a>
                      <!-- <a
                          href="https://www.pinterest.com/devxhub_com"
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Devxhub Pinterest Account"
                          ><img
                            class="social-icons"
                            src="/home/<USER>/pinterest.png"
                            alt="Devxhub Pinterest Icon"
                        /></a> -->
                    </div>
                  </div>
                  <div
                    class="flex flex-col items-start space-y-[10px] text-[14px] md:hidden"
                  >
                    <p class="leading-6">
                      &copy; {{ copyrightDate }},
                      <span
                        ><a
                          href="https://devxhub.com/"
                          rel="noopener noreferrer"
                          aria-label="Devxhub Ltd."
                          >Devxhub Limited,</a
                        ></span
                      >
                      All Rights Reserved.
                    </p>
                    <span class="px-4 md:block hidden text-white">|</span>
                    <p class="!mt-0">
                      <NuxtLink
                        to="/privacy-policy"
                        @click="handleSamePageNavigation('/privacy-policy')"
                      >
                        Privacy Policy
                      </NuxtLink>
                      <span class="px-2.5 text-white">|</span>
                      <NuxtLink
                        to="/terms-of-use"
                        @click="handleSamePageNavigation('/terms-of-use')"
                        >Terms of Use
                      </NuxtLink>
                    </p>
                  </div>
                </div>
              </div>
              <img
                class="container-fluid"
                src="/public/landing/Footer_Devxhub.svg"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}

@keyframes twitter {
  0% {
    background-image: url("~/assets/img/icons/TwitterIcon/twitter.svg");
  }

  50% {
    background-image: url("~/assets/img/icons/TwitterIcon/X_logo.svg");
  }

  100% {
    background-image: url("~/assets/img/icons/TwitterIcon/twitter.svg");
  }
}

.nowInViewPort {
  @apply transition-all ease-in duration-1000 opacity-100;
}

.notInViewPort {
  @apply transition-all ease-in duration-300 opacity-0;
  transform: translate3d(0, 100px, 0) scale(0.6);
}

.link-style {
  @apply text-heading text-xs lg:text-lg lg:pb-[10px] font-normal leading-[22px] lg:leading-[27px] cursor-pointer hover:text-orange;
}

.address {
  @apply text-heading text-xs lg:text-lg lg:pb-[10px] font-normal leading-[22px] lg:leading-[27px];
}

.heading-style {
  @apply text-black-1 text-sm lg:text-[25px] font-bold pb-[16px] lg:pb-[30px];
}

.footerInner {
  @apply text-white text-base 2dx:text-lg lg:pb-[10px] leading-[22px] lg:leading-[27px] cursor-pointer hover:text-orange dark:text-gray-400;
}

@import url("https://fonts.googleapis.com/css2?family=Quicksand:wght@300&family=Source+Serif+4:opsz,wght@8..60,200;8..60,300;8..60,400;8..60,500;8..60,600;8..60,700;8..60,800;8..60,900&family=Tinos:wght@700&display=swap");

.footer-top h1,
h2,
h3,
h4,
p,
span,
a {
  // font-family: "Source Serif 4", Georgia, "Times New Roman", Times, serif;
}

@import url("https://fonts.cdnfonts.com/css/helvetica-neue-5");

/* Define @font-face for Sohne */
@font-face {
  font-family: "Sohne";
  src: url("/test-soehne-leicht.woff2") format("woff2"),
    url("/test-soehne-leicht.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

.footer-bottom p,
.footer-bottom p a,
.footer-bottom p span a {
  // font-family: "Sohne", "Helvetica Neue";
  // font-family: "Aeonik", sans-serif;
  font-family: "Roboto", sans-serif;
}

svg {
  width: 100%;
}

footer {
  left: 0;
  overflow: hidden;
  position: absolute;
  width: 100%;
  height: 400px;
  top: 0;
  filter: url(#goo);

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 225px;
    clip-path: url("#wave");
    background-color: #0d0736;

    @media only screen and (max-width: 700px) {
      height: 150px;
    }
  }
}

.social-icons {
  @apply w-[40px] h-[40px] aspect-square object-contain;
}

@media (min-width: 1024px) and (max-width: 1200px) {
  .social-icons {
    @apply w-[40px] h-[40px];
  }
}

.hero_section_carousal_aeonik {
  // font-family: "Aeonik", sans-serif;
  font-family: "Roboto", sans-serif;
}
</style>

<script setup>
import { useRuntimeConfig } from "nuxt/app";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import { useHeaderStore } from "~/stores/header";
import DevXHubTopIcon from "../base/icon/DevXHubTopIcon.vue";
const config = useRuntimeConfig();
// pinia
const { isMobileMenu, isMobileMenuItem } = storeToRefs(useHeaderStore());
const { setMobileMenu } = useHeaderStore();
const company = [
  {
    id: 1,
    name: "About us",
    path: "/about-us",
    img: "/header/company/aboutUs.svg",
  },
  {
    id: 2,
    name: "Why Choose us",
    path: "/why-choose-us",
    img: "/header/company/whyChooseUs.svg",
  },
  {
    id: 3,
    name: "Products",
    path: "/products",
    img: "/header/company/product.webp",
  },
  {
    id: 4,
    name: "Blog",
    path: "/blog",
    img: "/header/company/blog.svg",
  },
  // {
  //   id: 5,
  //   name: "Service Provider FAQs",
  //   path: "/faq",
  //   img: "/header/company/faq.svg",
  // },
  {
    id: 6,
    name: "Careers",
    path: "/career",
    img: "/header/company/career.svg",
  },
];
const services = [
  {
    id: 1,
    name: "IT Staff Augmentation",
    path: "/services/it-staff-augmentation",
    img: "/header/services/teamEnhancement.png",
  },
  {
    id: 2,
    name: "Full Stack Development",
    path: "/services/full-stack-development",
    img: "/header/services/offshoreOffice.png",
  },
  {
    id: 3,
    name: "Mobile Application Development",
    path: "/services/mobile-application-development",
    img: "/header/services/offshoreOffice.png",
  },
  {
    id: 4,
    name: "Custom & Enterprise Software Development",
    path: "/services/custom-software-development",
    img: "/header/services/mvpService.png",
  },
  {
    id: 5,
    name: "MVP, SaaS, End-to-End Development",
    path: "/services/mvp-saas-end-to-end-development",
    img: "/header/services/saas.png",
  },
  {
    id: 6,
    name: "AI/ML Development & Integration",
    path: "/services/ai-ml-development-integration",
    img: "/header/services/ai.png",
  },
  {
    id: 7,
    name: "DevOps & Cloud Solutions",
    path: "/services/devops-cloud-solutions",
    img: "/header/services/ai.png",
  },
  // {
  //   id: 6,
  //   name: "End to End Development",
  //   path: "/coming-soon",
  //   img: "/header/services/endToEndDevelopment.png",
  //   nestedService: [
  //     {
  //       id: 1,
  //       name: "Custom Software Development",
  //       path: "/custom-software-development",
  //       img: "/header/services/customSoftwareDevelopment.png",
  //     },
  //     {
  //       id: 2,
  //       name: "Web Application Development",
  //       path: "/web-application-development",
  //       img: "/header/services/webApplicationDevelopment.png",
  //     },
  //     {
  //       id: 3,
  //       name: "Mobile App Development",
  //       path: "/mobile-app-development",
  //       img: "/header/services/mobileAppDevelopment.png",
  //     },
  //     // {
  //     //   id: 4,
  //     //   name: "UI/UX Design",
  //     //   path: "/ui-ux-design",
  //     //   img: "/header/services/UiUxDesign.png",
  //     // },
  //     {
  //       id: 5,
  //       name: "SQA and Testing Services",
  //       path: "/sqa-and-testing",
  //       img: "/header/services/SQATestingServices.png",
  //     },
  //     {
  //       id: 6,
  //       name: "DevOps",
  //       path: "/devops",
  //       img: "/header/services/DevOps.png",
  //     },
  //     // {
  //     //   id: 7,
  //     //   name: "Digital Marketing",
  //     //   path: "/digital-marketing",
  //     //   img: "/header/services/DigitalMarketing.png",
  //     // },
  //     {
  //       id: 8,
  //       name: "IT Consultancy",
  //       path: "/it-consultancy",
  //       img: "/header/services/consultancy.png",
  //     },
  //     {
  //       id: 9,
  //       name: "Software Maintenance and Support",
  //       path: "/software-maintenance-and-support",
  //       img: "/header/services/softwareMaintenanceSupport.png",
  //     },
  //   ],
  // },
];

const currentSubMenu = ref(null);
const toggleSubMenu = (submenu) => {
  if (currentSubMenu.value === submenu) {
    currentSubMenu.value = null;
  } else {
    currentSubMenu.value = submenu;
  }
};

const currentNestedMenu = ref(null);
const setCurrentNestedMenu = (activeNested) => {
  if (currentNestedMenu.value !== activeNested) {
    currentNestedMenu.value = activeNested;
  } else {
    currentNestedMenu.value = null;
  }
};
const selectedLang = ref("En");
const langListes = ref(["En", "Fr", "ع.ر"]);
const openLangMenu = ref(false);
const changeLang = (lang) => {
  selectedLang.value = lang;
  openLangMenu.value = false;
};
const route = useRoute();
const showHeaderOptions = ref(false);

// Function to handle navigation to the same page
const handleSamePageNavigation = (path) => {
  if (route.path === path) {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }
};

onMounted(() => {
  if (route?.query?.option_menu === "true") {
    showHeaderOptions.value = true;
  } else {
    showHeaderOptions.value = false;
  }
  document.addEventListener("click", () => {
    openLangMenu.value = false;
  });
  // Only run this on client
  // if (process.client) {
  //   // Add the Google Translate script dynamically
  //   const script = document.createElement("script");
  //   script.src =
  //     "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
  //   script.async = true;
  //   document.head.appendChild(script);

  //   // Create a global callback for the widget
  //   window.googleTranslateElementInit = function () {
  //     new window.google.translate.TranslateElement(
  //       {
  //         pageLanguage: "en",
  //         includedLanguages: "en,ar", // Add or remove languages here
  //         layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
  //       },
  //       "google_translate_element"
  //     );
  //   };
  // }
});
onUnmounted(() => {
  document.removeEventListener("click", () => {
    openLangMenu.value = false;
  });
});
</script>

<template>
  <!-- shadow-[0px_4px_4px_rgba(0,0,0,0.25)] -->
  <div class="bg-[#130C2A] header" @click="setMobileMenu(false)">
    <div class="container-fluid bg-[#130C2A]">
      <div
        class="flex justify-between items-center md:py-7 py-3.5 relative h-20"
      >
        <NuxtLink to="/" @click="handleSamePageNavigation('/')">
          <!-- <BaseIconDEVxHUBLogo
            class="md:w-[170px] md:h-[32px] w-[170px] h-[32px]"
          /> -->
          <DevXHubTopIcon class="md:w-[170px] md:h-[32px] w-[170px] h-[32px]" />
        </NuxtLink>
        <ul
          class="hidden lg:flex ml-4 space-x-4 lgx:space-x-6 2xl:space-x-10 text-lg font-semibold text-white"
        >
          <li>
            <NuxtLink
              to="/about-us"
              aria-label="About Us"
              class="whitespace-nowrap"
              @click="handleSamePageNavigation('/about-us')"
              >About us</NuxtLink
            >
          </li>
          <!-- <li class="dropdown">
            <p
              class="flex items-center space-x-4 cursor-pointer"
              @click="toggleSubMenu('company')"
            >
              <span>Company</span>
              <ClientOnly>
                <fa
                  :icon="['fas', 'angle-down']"
                  class="transition-all duration-300 ease-in-out"
                />
              </ClientOnly>
            </p>
            <ul
              v-if="currentSubMenu === 'company'"
              class="dropdown-content flex flex-col py-2"
            >
              <span class="box"></span>
              <span class="triangle"></span>
              <li class="mx-6" v-for="item in company" :key="item.id">
                <NuxtLink
                  class="py-3 pr-10 flex space-x-2 border-b-[0.5px] border-[#FDB21D] items-center"
                  :to="item.path"
                  :aria-label="item.name"
                  @click.native="toggleSubMenu(null)"
                  ><img :src="item.img" :alt="item.name" class="w-6 h-6" />
                  <span>{{ item.name }}</span>
                </NuxtLink>
              </li>
            </ul>
          </li> -->
          <li class="dropdown">
            <div
              class="flex items-center space-x-2 cursor-pointer"
              @click="toggleSubMenu('services')"
            >
              <NuxtLink
                to="/services"
                @click="handleSamePageNavigation('/services')"
                >Services</NuxtLink
              >
              <div
                class="bg-[#f1f1f2] text-black w-[16px] h-[16px] flex justify-center items-center rounded-full"
              >
                <ClientOnly>
                  <fa
                    :icon="['fas', 'arrow-up']"
                    class="transition-all duration-300 ease-in-out text-sm transform arrow-up-icon"
                  />
                </ClientOnly>
              </div>
            </div>
            <!-- v-if="currentSubMenu === 'services'" -->
            <div
              class="dropdown-content service-dropdown flex flex-col overflow-hidden"
            >
              <!-- <span class="box"></span>
              <span class="triangle service-triangle"></span> -->
              <div class="flex space-x-3 w-full px-[25px] py-[30px]">
                <div class="services w-full">
                  <ul class="w-full">
                    <li
                      v-for="(item, index) in services"
                      :key="item.id"
                      class="border-b-[0.5px] border-[#ffffff1a] last:border-b-0"
                    >
                      <NuxtLink
                        :to="item.path"
                        class="py-[18px] flex items-center"
                        @click="handleSamePageNavigation(item.path)"
                      >
                        <span class="text-[#F1F1F2] font-medium">
                          {{ item.name }}
                        </span>
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <!-- <template v-for="item in services" :key="item.id">
                  <div v-if="item.nestedService" class="mx-6 nestedServices">
                    <div
                      class="py-3 pr-10 flex space-x-2 border-b-[0.5px] border-[#FDB21D] items-center mr-6"
                    >
                      <img :src="item.img" :alt="item.name" class="w-6 h-6" />
                      <span>{{ item.name }}</span>
                      <ClientOnly>
                        <fa :icon="['fas', 'hand-point-down']" class="ml-2" />
                      </ClientOnly>
                    </div>
                    <ul>
                      <template
                        v-for="nestedItem in item.nestedService"
                        :key="nestedItem.id"
                      >
                        <li class="mx-6">
                          <NuxtLink
                            class="py-3 pr-10 flex space-x-2 border-b-[0.5px] border-[#FDB21D] items-center"
                            :to="nestedItem.path"
                            @click.native="toggleSubMenu(null)"
                            ><img
                              :src="nestedItem.img"
                              :alt="nestedItem.name"
                              class="w-6 h-6"
                            />
                            <span>{{ nestedItem.name }}</span>
                          </NuxtLink>
                        </li>
                      </template>
</ul>
</div>
</template> -->
              </div>
            </div>
          </li>
          <li>
            <NuxtLink
              to="/work"
              aria-label="Case study"
              class="whitespace-nowrap"
              @click="handleSamePageNavigation('/work')"
              >Work</NuxtLink
            >
          </li>
          <li>
            <NuxtLink
              to="/our-working-process"
              aria-label="Our process"
              class="whitespace-nowrap"
              @click="handleSamePageNavigation('/our-working-process')"
              >Our process
            </NuxtLink>
          </li>
          <!-- <li><NuxtLink to="/our-clients">Clients</NuxtLink></li> -->
          <!-- <li><NuxtLink to="/products">Products</NuxtLink></li> -->
          <!-- <li>
            <NuxtLink to="/how-we-work" class="whitespace-nowrap"
              >How We Work</NuxtLink
            >
          </li> -->
          <li>
            <NuxtLink
              to="/blog"
              aria-label="Blog"
              class="whitespace-nowrap"
              @click="handleSamePageNavigation('/blog')"
              >Blog</NuxtLink
            >
          </li>
          <!-- <li>
            <NuxtLink
              to="/appointment"
              aria-label="Schedule a meeting"
              class="whitespace-nowrap"
              >Schedule a meeting</NuxtLink
            >
          </li> -->
          <li>
            <NuxtLink
              to="/career"
              aria-label="Career"
              class="whitespace-nowrap"
              @click="handleSamePageNavigation('/career')"
              >Career</NuxtLink
            >
          </li>
          <!-- <li>
            <NuxtLink
              to="/career"
              aria-label="Careers"
              class="whitespace-nowrap flex items-center relative"
              ><img
                class="w-[16px] h-[16px] absolute -top-[1px] -right-[10px]"
                src="/header/newrocket.webp"
                alt="rocket_space_icon"
              /><span>Career</span></NuxtLink
            >
          </li> -->
        </ul>

        <div class="flex space-x-6 items-center">
          <NuxtLink
            to="/contact-us"
            aria-label="Contact Us"
            class="hero_section_carousal_aeonik whitespace-nowrap w-[150px] h-10 hidden lg:flex justify-center items-center bg-[#FFD700] text-[#1D1A20] rounded-full"
            @click="handleSamePageNavigation('/contact-us')"
          >
            Contact us</NuxtLink
          >
          <!-- Translation dropdown -->
          <!-- <div v-if="config.public.workflow === 'dev'" id="google_translate_element" class="p-2" /> -->
          <!-- <div
            v-if="config.public.workflow === 'dev'"
            class="hidden relative w-[60px] h-10 px bg-[#fff] text-[#1D1A20] lg:flex justify-center items-center"
            :class="
              openLangMenu ? 'rounded-b-none rounded-t-lg' : 'rounded-full'
            "
          >
            <div
              class="flex justify-between items-center space-x-2 p-2 w-full cursor-pointer"
              @click.stop="openLangMenu = !openLangMenu"
            >
              <p>{{ selectedLang }}</p>
              <ClientOnly>
                <fa
                  :icon="['fas', 'angle-down']"
                  class="transition-all duration-300 ease-in-out"
                  :class="openLangMenu ? 'rotate-180' : 'rotate-0'"
                />
              </ClientOnly>
            </div>
            <ul
              v-if="openLangMenu"
              class="rounded-b-lg w-full flex flex-col bg-[#fff] text-[#1D1A20] absolute top-10 left-0"
            >
              <li
                class="border-t border-[#f1f1f2] p-2 cursor-pointer flex items-center justify-between"
                v-for="(langList, index) in langListes"
                :key="index"
                @click.stop="changeLang(langList)"
              >
                {{ langList }}
                <fa
                  v-if="selectedLang === langList"
                  class="text-[#1a1139]"
                  :icon="['fa-solid', 'fa-check']"
                />
              </li>
            </ul>
          </div> -->
        </div>
        <div class="lg:hidden relative">
          <div
            class="flex justify-center items-center rounded-[100px] py-[7px] px-[21px] space-x-4 bg-[#FFD700] text-[#333333]"
            @click.stop="
              isMobileMenu ? setMobileMenu(false) : setMobileMenu(true)
            "
          >
            <!-- <img
              @click.stop="
                isMobileMenu ? setMobileMenu(false) : setMobileMenu(true)
              "
              class="h-8 w-8 cursor-pointer"
              :src="isMobileMenu ? closeIcon : menuIcon"
              alt="Devxhub Menu Icon"
            /> -->
            <div class="flex flex-col">
              <div
                class="w-6 h-[1px] border border-solid border-[#0e0f18]"
              ></div>
              <div
                class="w-6 h-[2px] mt-1.5 border border-solid border-[#0e0f18]"
              ></div>
            </div>
            <p class="text-lg">Menu</p>
          </div>
          <!-- v-if="isMobileMenu" -->
          <!-- v-if="isMobileMenu" -->
          <transition name="menu">
            <div
              v-if="isMobileMenu"
              class="mobile w-[91%] h-full rounded-[15px] py-3 fixed top-[13px] right-[19px] overflow-y-auto"
              :class="showHeaderOptions ? 'max-h-[92vh]' : 'max-h-[550px]'"
              @click.stop="openLangMenu = false"
            >
              <transition name="fadeIn" mode="out-in">
                <ul
                  v-if="isMobileMenuItem"
                  class="overflow-hidden opacity-100 flex flex-col mx-5 text-white whitespace-nowrap text-sm md:text-base font-medium text-center"
                >
                  <div class="flex justify-between w-full items-center mt-4">
                    <BaseIconDEVxHUBLogo class="w-[170px] h-[32px]" />
                    <button
                      class="text-lg font-medium flex justify-center items-center rounded-[100px] py-[7px] px-[21px] space-x-4 bg-white text-black"
                      @click.stop="setMobileMenu(false)"
                    >
                      Close
                    </button>
                  </div>
                  <li
                    class="py-4 border-b-[0.5px] border-[#FDB21D] mt-[40px] text-left"
                  >
                    <NuxtLink
                      to="/about-us"
                      aria-label="About us"
                      class="text-4xl font-medium"
                      @click="
                        handleSamePageNavigation('/about-us');
                        setMobileMenu(false);
                      "
                      >About us</NuxtLink
                    >
                  </li>
                  <!-- <li
                    class="mt-10 py-4 flex flex-col border-b-[0.5px] border-[#FDB21D]"
                  >
                    <p
                      class="flex items-center justify-center space-x-2"
                      :class="
                        currentSubMenu === 'company'
                          ? 'text-[#FDB21D]'
                          : 'text-white'
                      "
                      @click="toggleSubMenu('company')"
                    >
                      <span class="text-xl font-medium">Company</span>
                      <ClientOnly>
                        <fa
                          :icon="['fas', 'angle-down']"
                          class="transition-all duration-300 ease-in-out"
                          :class="
                            currentSubMenu === 'company'
                              ? 'rotate-180'
                              : 'rotate-0'
                          "
                        />
                      </ClientOnly>
                    </p>
                    <ul
                      v-if="currentSubMenu === 'company'"
                      class="flex flex-col pb-2 pt-4"
                    >
                      <li class="py-3" v-for="item in company" :key="item.id">
                        <NuxtLink
                          :to="item.path"
                          @click.native="setMobileMenu(false)"
                          ><span>{{ item.name }}</span>
                        </NuxtLink>
                      </li>
                    </ul>
                  </li> -->
                  <li
                    class="py-4 flex flex-col border-b-[0.5px] border-[#FDB21D] text-left"
                  >
                    <div
                      class="flex items-center justify-start space-x-2"
                      :class="
                        currentSubMenu === 'services'
                          ? 'text-[#FDB21D]'
                          : 'text-white'
                      "
                      @click="toggleSubMenu('services')"
                    >
                      <span class="text-4xl font-medium">Services</span>
                      <div
                        class="bg-[#f1f1f2] text-black w-[20px] h-[20px] flex justify-center items-center rounded-full"
                      >
                        <ClientOnly>
                          <fa
                            :icon="['fas', 'arrow-up']"
                            class="transition-all duration-300 ease-in-out text-sm transform arrow-up-icon"
                            :class="
                              currentSubMenu === 'services'
                                ? 'rotate-180'
                                : 'rotate-0'
                            "
                          />
                        </ClientOnly>
                      </div>
                    </div>
                    <ul
                      v-if="currentSubMenu === 'services'"
                      class="flex flex-col pb-2 pt-4 md:text-lg text-[16px]"
                    >
                      <template v-for="item in services">
                        <li
                          v-if="!item.nestedService"
                          class="py-3"
                          :key="item.id"
                        >
                          <NuxtLink
                            :to="item.path"
                            @click="
                              handleSamePageNavigation(item.path);
                              setMobileMenu(false);
                            "
                            ><span>{{ item.name }}</span>
                          </NuxtLink>
                        </li>
                        <!-- <li
                          v-if="item.nestedService"
                          class="py-3"
                          :key="item.id"
                        >
                          <div
                            :class="
                              currentNestedMenu === item.id
                                ? 'text-[#FDB21D]'
                                : 'text-white'
                            "
                            @click.native="setCurrentNestedMenu(item.id)"
                          >
                            <span>{{ item.name }}</span>
                            <ClientOnly>
                              <fa
                                :icon="['fas', 'angle-down']"
                                class="ml-2 transition-all duration-300 ease-in-out"
                                :class="
                                  currentNestedMenu === item.id
                                    ? 'rotate-180'
                                    : 'rotate-0'
                                "
                              />
                            </ClientOnly>
                          </div>
                          <ul
                            v-if="currentNestedMenu === item.id"
                            class="flex flex-col pb-2 pt-4 transition-all duration-300 ease-in-out"
                          >
                            <template
                              v-for="nestedItem in item.nestedService"
                              :key="nestedItem.id"
                            >
                              <li class="py-3">
                                <NuxtLink
                                  :to="nestedItem.path"
                                  @click.native="setMobileMenu(false)"
                                  ><span>{{ nestedItem.name }}</span>
                                </NuxtLink>
                              </li>
                            </template>
            </ul>
          </li> -->
                      </template>
                    </ul>
                  </li>
                  <li class="py-4 border-b-[0.5px] border-[#FDB21D] text-left">
                    <NuxtLink
                      to="/work"
                      aria-label="Case study"
                      class="text-4xl font-medium"
                      @click="
                        handleSamePageNavigation('/work');
                        setMobileMenu(false);
                      "
                      >Work</NuxtLink
                    >
                  </li>
                  <li class="py-4 border-b-[0.5px] border-[#FDB21D] text-left">
                    <NuxtLink
                      to="/our-working-process"
                      aria-label="Our process"
                      class="text-4xl font-medium"
                      @click="
                        handleSamePageNavigation('/our-working-process');
                        setMobileMenu(false);
                      "
                      >Our process</NuxtLink
                    >
                  </li>
                  <li class="py-4 border-b-[0.5px] border-[#FDB21D] text-left">
                    <NuxtLink
                      to="/blog"
                      class="text-4xl font-medium"
                      @click="
                        handleSamePageNavigation('/blog');
                        setMobileMenu(false);
                      "
                      aria-label="Blog"
                      >Blog
                    </NuxtLink>
                  </li>
                  <!-- <li class="py-4 border-b-[0.5px] border-[#FDB21D]">
                    <NuxtLink
                      to="/our-clients"
                      class="text-xl font-medium"
                      @click.native="setMobileMenu(false)"
                      >Clients</NuxtLink
                    >
                  </li> -->
                  <!-- <li class="py-4 border-b-[0.5px] border-[#FDB21D]">
                <NuxtLink
                  to="/products"
                  class="text-xl font-medium"
                  @click.native="setMobileMenu(false)"
                  >Products</NuxtLink
                >
              </li> -->
                  <!-- <li class="py-4 border-b-[0.5px] border-[#FDB21D]">
                    <NuxtLink
                      to="/how-we-work"
                      class="text-xl font-medium"
                      @click.native="setMobileMenu(false)"
                      >How We Work</NuxtLink
                    >
                  </li> -->
                  <!-- <li class="py-4 border-b-[0.5px] border-[#FDB21D]">
                    <NuxtLink
                      to="/appointment"
                      class="text-xl font-medium"
                      @click.native="setMobileMenu(false)"
                      aria-label="Schedule a meeting"
                      >Schedule a meeting</NuxtLink
                    >
                  </li> -->
                  <li class="py-4 border-b-[0.5px] border-[#FDB21D] text-left">
                    <NuxtLink
                      to="/career"
                      class="text-4xl font-medium"
                      @click="
                        handleSamePageNavigation('/career');
                        setMobileMenu(false);
                      "
                      aria-label="Careers"
                    >
                      Career</NuxtLink
                    >
                  </li>
                  <li
                    class="py-4 border-b-[0.5px] border-[#FDB21D] text-left"
                    v-if="config.public.workflow === 'dev' && showHeaderOptions"
                  >
                    <NuxtLink
                      to="#rate_this_app"
                      class="text-4xl font-medium"
                      @click.native="setMobileMenu(false)"
                      aria-label="Careers"
                      >Rate this app</NuxtLink
                    >
                  </li>
                  <li
                    class="py-4 border-b-[0.5px] border-[#FDB21D] text-left"
                    v-if="config.public.workflow === 'dev' && showHeaderOptions"
                  >
                    <NuxtLink
                      to="#share_this_app"
                      class="text-4xl font-medium"
                      @click.native="setMobileMenu(false)"
                      aria-label="Careers"
                      >Share this app</NuxtLink
                    >
                  </li>
                  <!-- <li
                    v-if="config.public.workflow === 'dev'"
                    class="py-4 flex justify-start"
                    :class="
                      config.public.workflow === 'dev'
                        ? 'border-b-[0.5px] border-[#FDB21D]'
                        : ''
                    "
                  >
                    <div
                      class="relative w-[60px] px bg-[#fff] text-[#1D1A20] flex flex-col justify-start items-center"
                      :class="
                        openLangMenu
                          ? 'rounded-t-lg rounded-b-lg'
                          : 'rounded-full'
                      "
                    >
                      <div
                        class="h-10 flex justify-between items-center space-x-2 p-2 w-full cursor-pointer"
                        @click.stop="openLangMenu = !openLangMenu"
                      >
                        <p>{{ selectedLang }}</p>
                        <ClientOnly>
                          <fa
                            :icon="['fas', 'angle-down']"
                            class="transition-all duration-300 ease-in-out"
                            :class="openLangMenu ? 'rotate-180' : 'rotate-0'"
                          />
                        </ClientOnly>
                      </div>
                      <ul
                        v-if="openLangMenu"
                        class="rounded-b-lg w-full flex flex-col bg-[#fff] text-[#1D1A20]"
                      >
                        <li
                          class="text-left border-t border-[#f1f1f2] p-2 cursor-pointer"
                          v-for="(langList, index) in langListes"
                          :key="index"
                          @click.stop="changeLang(langList)"
                        >
                          {{ langList }}
                        </li>
                      </ul>
                    </div>
                  </li> -->
                  <li class="py-4 flex justify-center">
                    <NuxtLink
                      to="/contact-us"
                      aria-label="Contact Us"
                      class="hero_section_carousal_aeonik whitespace-nowrap w-full h-10 flex justify-center items-center bg-[#FFD700] text-[#1D1A20] rounded-full"
                      @click.native="setMobileMenu(false)"
                      >Contact us</NuxtLink
                    >
                    <!-- <NuxtLink
                  to="/contact-us"
                  class="text-xl font-medium"
                  aria-label="Contact Us"
                  @click.native="setMobileMenu(false)"
                  >Contact Us</NuxtLink
                > -->
                  </li>
                </ul>
              </transition>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.header {
  /* font-family: "SF UI Display", sans-serif !important; */
}

.header .container-fluid {
  /* box-shadow: rgba(0, 0, 0, 0.25) 0px 14px 28px,
    rgba(0, 0, 0, 0.22) 0px 10px 10px;
  @apply border-b-[0.5px] border-[#d9d9d9] border-opacity-30; */
}

.dropdown {
  position: relative;
}

.dropdown-content {
  /* display: none; */
  position: absolute;
  top: 56px;
  z-index: 1;
  min-width: 550px;
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  box-shadow: 6px 6px 8px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  font-size: 18px;
  font-weight: normal;
  height: 0;
  transition: height 0.5s ease-in-out;
}

.dropdown:hover .dropdown-content {
  height: 514px;
}

.arrow-up-icon {
  rotate: 180deg;
}

.dropdown:hover .arrow-up-icon {
  rotate: 0deg;
}

.dropdown-content li {
  color: white;
  white-space: nowrap;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content li:last-child a {
  border-bottom: none;
}

.service-dropdown {
  left: 130%;
  transform: translateX(-50%);
}

.triangle {
  clip-path: polygon(50% 40%, 0% 100%, 100% 100%);
  background-color: #2a1e56;
  position: absolute;
  top: -20px;
  left: 40px;
  display: block;
  width: 20px;
  height: 20px;
}

.service-triangle {
  left: 30%;
}

.box {
  width: 100%;
  height: 30px;
  position: absolute;
  top: -30px;
  left: 0;
  opacity: 0;
}

.mobile {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* for page */
.menu-enter-active {
  transition: height 0.6s ease-in-out, width 0.6s ease-in-out;
  transition-delay: 0.2s;
}

.menu-leave-active {
  transition: height 0.6s ease-in-out, width 0.6s ease-in-out;
  /* transition-delay: 0.6s; */
}

.menu-enter-from,
.menu-leave-to {
  width: 129px;
  height: 44px;
  /* opacity: 0; */
}

.fadeIn-enter-active {
  transition: opacity 0.6s ease-in-out;
  /* transition-delay: 0.6s; */
}

.fadeIn-leave-active {
  transition: opacity 0.6s ease-in-out;
}

.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}
</style>

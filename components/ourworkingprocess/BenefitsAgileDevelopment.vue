<script setup>
const headerTitle = `<p class="primary-p-tag text-up-animation font-bold leading-[30px] max-w-[197px]"><span>Benefit of</span> <br/> <span class="instrument-italic">Agile Development</span></p>`;

const agileDevelopment = ref([
  {
    title: "Flexibility and Adaptability",
    description:
      "Agile accommodates changes in requirements, even late in development, ensuring the product stays relevant.",
  },
  {
    title: "Faster Time-to-Market",
    description:
      "Regular delivery of functional increments allows businesses to launch usable features early.",
  },
  {
    title: "Improved Collaboration",
    description:
      "Continuous communication among teams and stakeholders ensures alignment and reduces misunderstandings.",
  },
  {
    title: "Higher Quality",
    description:
      "Continuous testing and feedback during development result in fewer defects and a more reliable product.",
  },
]);
</script>

<template>
  <OurworkingprocessComBenefit
    :header-title="headerTitle"
    :core-values="agileDevelopment"
  />
</template>

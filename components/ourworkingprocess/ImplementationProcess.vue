<script setup>
const implementationProcesses = ref([
  {
    point: "01",
    title: "Business Strategy",
    items: [
      "Business Goals",
      "Vision and Mission",
      "Target Market",
      "Value Proposition",
      "SWOT Analysis",
      "Business Model Canvas",
    ],
  },
  {
    point: "02",
    title: "Discovery and Ideation",
    items: [
      "Problem Identification",
      "User Persona Development",
      "Competitor Research",
      "Design Thinking Workshops",
      "Prioritization of Features",
    ],
  },
  {
    point: "03",
    title: "Product Backlog Creation",
    items: [
      "Epics and User Stories Definition",
      "Prioritization (MoSCoW Method)",
      "Creation of Acceptance Criteria",
      "Backlog Grooming",
      "Cross-functional Team Alignment",
    ],
  },
  {
    point: "04",
    title: "Sprint Planning",
    items: [
      "Sprint Goal Definition",
      "Task Breakdown",
      "Estimation (Story Points)",
      "Capacity Planning",
      "Team Commitment",
    ],
  },
  {
    point: "05",
    title: "Incremental Development",
    items: [
      "Iterative Feature Development",
      "Daily Stand-ups (Scrum Meetings)",
      "Cross-team Collaboration",
      "Continuous Integration (CI)",
      "Regular Code Reviews",
    ],
  },
  {
    point: "06",
    title: "Prototyping and Validation",
    items: [
      "Low-fidelity Wireframes",
      "High-fidelity Prototypes",
      "Usability Testing",
      "Customer Validation (Feedback Loop)",
      "Refinement of User Stories",
    ],
  },
  {
    point: "07",
    title: "Sprint Reviews and Demos",
    items: [
      "Demonstrate Working Increments",
      "Gather Stakeholder Feedback",
      "Capture Lessons Learned",
      "Update Product Backlog",
    ],
  },
  {
    point: "08",
    title: "Release Planning and Deployment",
    items: [
      "Minimum Viable Product (MVP) Release",
      "Continuous Deployment (CD) Setup",
      "Feature Toggles for Safe Release",
      "Release Notes Preparation",
      "Pilot Testing",
    ],
  },
  {
    point: "09",
    title: "Market Testing",
    items: [
      "Beta Testing",
      "A/B Testing",
      "A/B Testing",
      "Performance Metrics Collection",
      "Performance Metrics Collection",
      "Feedback Implementation",
    ],
  },
  {
    point: "10",
    title: "Continuous Improvement",
    items: [
      "Sprint Retrospectives",
      "Identification of Bottlenecks",
      "Process Optimization",
      "Automation Enhancements",
      "Backlog Refinement",
    ],
  },
  {
    point: "11",
    title: "Product Scaling and Maintenance",
    items: [
      "Continuous Delivery of Features",
      "Performance Monitoring",
      "Customer Support Integration",
      "Regular Updates and Improvements",
      "Scaling Architecture for Growth",
    ],
  },
]);

const title = `<p class="third-p-tag leading-[30px] font-bold">Product <span class="instrument-italic text-[#F1F1F2]"> Development & <br/>
Implementation </span> Process</p>`;
const implementationProcessHeight = () => {
  // Loop through expertiseAreas to calculate heights
  implementationProcesses.value.forEach((implementationProcess) => {
    implementationProcess.items.forEach((area) => {
      const element = document.getElementById(implementationProcess.point);
      if (element) {
        implementationProcess.height = element.getBoundingClientRect().height;
      }
    });
  });
};
const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(async () => {
  await nextTick(); // Wait for the DOM to render
  viewPortEnter();
  implementationProcessHeight();
  window.addEventListener("resize", implementationProcessHeight);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", implementationProcessHeight);
});
</script>

<template>
  <div class="">
    <!-- <h2 class="secondary-h-tag title-up-animation">
      <p><span class="text-[#999]">Product</span> Development &</p>
      <p>Implementation <span class="text-[#999]">Process</span></p>
    </h2> -->
    <div class="case-grid-item md:mt-[180px] mt-[40px]">
      <div class="title-up-animation" v-html="title"></div>
      <div class="grid-item gap-70 w-full">
        <div
          v-for="(implementationProcess, index) in implementationProcesses"
          :key="index"
          class="md:pb-[60px] pb-[30px] last:pb-0 border-b last:border-b-0 border-[#ffffff1a] implementationProcess text-up-animation"
        >
          <div class="grid-subitem">
            <div class="flex space-x-5">
              <p class="primary-p-tag text-gradient !text-[24px] h-[24px]">
                {{ implementationProcess.point }}
              </p>
              <h2 class="text-[24px] text-[#999] font-bold">
                {{ implementationProcess.title }}
              </h2>
            </div>
            <div
              class="xl:ml-0 lg:ml-[68px] md:ml-[64px] ml-[60px] flex justify-between items-center transition-all duration-500 ease-in-out overflow-hidden implementationProcess-items"
              :style="{
                '--elementHeight': `${implementationProcess.height}px`,
              }"
            >
              <ul
                :id="implementationProcess.point"
                class="flex flex-col space-y-5"
              >
                <li
                  class="flex items-center space-x-[9px]"
                  v-for="(item, index) in implementationProcess.items"
                  :key="index"
                >
                  <img
                    class="w-[18px] h-4"
                    src="/public/ourworkingprocess/rotate-arrow.svg"
                    alt="rotate-arrow"
                  />
                  <p class="forth-p-tag">{{ item }}</p>
                </li>
              </ul>
              <!-- <img
                src="/public/ourworkingprocess/up-arrow-with-bg.svg"
                alt="up-arrow-with-bg"
              /> -->
            </div>
            <div class="h-full hidden md:inline-block">
              <img
                src="/public/ourworkingprocess/up-arrow-with-bg.svg"
                alt="up-arrow-with-bg"
                class="down_image h-[50px] w-[50px]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 60px;
}

.grid-subitem {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 0.2fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
  grid-column-gap: 0px;
  grid-row-gap: 16px;
}
.implementationProcess-items {
  height: 0;
}
.implementationProcess:hover h2 {
  color: #f1f2f1;
}
.down_image {
  position: absolute;
  top: 0%;
  right: 0;
  rotate: 180deg;
  transition: all 0.5s ease-in-out;
}
.implementationProcess:hover .down_image {
  top: 50%;
  rotate: 0deg;
  transition: all 0.5s ease-in-out;
}
.implementationProcess:hover .implementationProcess-items {
  height: var(--elementHeight);
}
@media screen and (max-width: 1279px) {
  .grid-subitem {
    grid-column-gap: 30px;
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 1279px) and (min-width: 1080px) {
  .down_image {
    top: 20px;
    right: 20px;
    transform: translateY(0%);
  }
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

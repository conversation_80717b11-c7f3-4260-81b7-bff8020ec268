<script setup>
const props = defineProps({
  heroTitle: {
    type: String,
    default: ``,
  },
  heroDes: {
    type: String,
    default: ``,
  },
  showImage: {
    type: Boolean,
    default: false,
  },
  titleWrapper: {
    type: String,
    default: "",
  },
  titleClass: {
    type: String,
    default: "w-full",
  },
  descWrapper: {
    type: String,
    default: "",
  },
  descClass: {
    type: String,
    default: "text-left",
  },
});

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");
  const imgUpAnimation = document.querySelectorAll(".img-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  const imgUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  imgUpAnimation.forEach((item) => {
    imgUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="overflow-hidden max-w-[1280px] w-full" :class="titleWrapper">
      <h1
        class="primary-h-tag text-left title-animation"
        :class="titleClass"
        v-html="heroTitle"
      ></h1>
    </div>
    <div class="max-w-[1280px] w-full" :class="descWrapper">
      <div
        class="primary-p-tag max-w-[1054px] mt-5 text-up-animation"
        :class="descClass"
        v-html="heroDes"
      ></div>
    </div>
    <div
      v-if="showImage"
      class="w-full flex justify-center lg::mt-[64px] md:mt-14 mt-10 img-up-animation"
    >
      <img
        class="max-w-[1280px] max-h-[853px] w-[89vw] h-[60vw]"
        src="/public/ourworkingprocess/DeWatermark.svg"
        alt="DeWatermark"
      />
    </div>
    <slot name="button"></slot>
  </div>
</template>

<style scoped>
.title-animation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-animation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
.text-up-animation,
.img-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.img-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

<script setup>
const extensionProcesses = ref([
  {
    point: "01",
    title: "Talent Requirements",
    description:
      "Share your talent requirements and team size for the ultimate augmentation.",
  },
  {
    point: "02",
    title: "Contract Signing",
    description: "Contract signing for your IT Staff Augmentation.",
  },
  {
    point: "03",
    title: "Talent Allocation",
    description: "Provide ready-to-go teams as per your requirements.",
  },
  {
    point: "04",
    title: "Project Continues",
    description:
      "Augmented team will seamlessly align with your in-house team to ensure on-time project delivery.",
  },
]);
const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-animation");
  const textLeftAnimation = document.querySelectorAll(".text-left-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textLeftAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textLeftAnimation.forEach((item) => {
    textLeftAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div class="w-fill flex flex-col items-center md:mt-[180px] mt-[100px]">
    <div class="overflow-hidden">
      <h2
        class="our-process-secondary-h-tag text-center text-[#999] title-animation"
      >
        <span class="instrument-italic text-[#F1F1F2] text-[48px]"
          >Team Extension
        </span>
        <span class="text-[#999] text-[48px]">Process</span>
      </h2>
    </div>
    <div class="four-grid-item mt-[40px]">
      <div
        class="flex space-x-[12px] text-left-animation"
        v-for="(extensionProcess, index) in extensionProcesses"
        :key="index"
      >
        <p class="primary-p-tag">{{ extensionProcess.point }}</p>
        <div class="flex flex-col space-y-2.5">
          <h2 class="third-h-tag">{{ extensionProcess.title }}</h2>
          <p class="secondary-p-tag">
            {{ extensionProcess.description }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.four-grid-item {
  grid-column-gap: 60px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid-item {
    grid-column-gap: 60px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}

.title-animation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-animation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}

.text-left-animation {
  transition: all 0.8s ease-in-out;
  transform: translateX(-45px);
  opacity: 0;
}
.text-left-animation.final {
  transform: translateX(0px);
  opacity: 1;
}
</style>

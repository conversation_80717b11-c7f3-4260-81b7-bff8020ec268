<script setup>
const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");
  const imgUpAnimation = document.querySelectorAll(".img-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  const imgUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  imgUpAnimation.forEach((item) => {
    imgUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div class="flex flex-col items-center md:mt-[200px] mt-[100px]">
    <h2
      class="text-[48px] font-bold max-w-[611px] text-center title-up-animation"
    >
      <span class="text-[#999] md:whitespace-nowrap"
        >Our Standard Development
      </span>
      <span class="instrument-italic"> Agile Process</span>
    </h2>
    <img
      class="img-up-animation max-w-[821px] max-h-[472px] lg:mt-[80px] md:mt-[60px] mt-[40px] md:w-[57vw] md:h-[33vw] w-[77vw] h-[53vw]"
      src="/public/ourworkingprocess/Agile.svg"
      alt="Agile"
    />
    <div
      class="text-up-animation four-grid-item md:ml-[16px] md:mt-[120px] mt-[100px]"
    >
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[35px] mb-[20px] min-[992px]:w-[90px] min-[992px]:h-[91px] w-[70px] h-[70.77px]"
          src="/public/ourworkingprocess/agile-process/project-kick-off.svg"
          alt="Project Kick-Off"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-medium">
          Project Kick-Off
        </p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[35px] mb-[20px] min-[992px]:w-[90px] min-[992px]:h-[91px] w-[70px] h-[70.77px]"
          src="/public/ourworkingprocess/agile-process/stay-on-agile.svg"
          alt="Stay on Agile"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-medium">
          Stay on Agile
        </p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[35px] mb-[20px] min-[992px]:w-[90px] min-[992px]:h-[91px] w-[70px] h-[70.77px]"
          src="/public/ourworkingprocess/agile-process/successful-delivery.svg"
          alt="Successful Delivery"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-medium">
          Successful Delivery
        </p>
      </div>
      <!-- <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[35px] mb-[20px] min-[992px]:w-[90px] min-[992px]:h-[91px] w-[70px] h-[70.77px]"
          src="/public/landing/howwework/4.svg"
          alt="Continuous Improvement"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-medium">
          Continuous Improvement
        </p>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.four-grid-item {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.work-single-item {
  text-align: center;
  border: 1px solid #d6dce1;
  border-radius: 128px;
  margin-left: -22px;
  padding: 30px 40px 20px;
}
.work-single-item:first-child {
  /* margin-left: 0px; */
}
@media screen and (min-width: 1440px) {
  .work-single-item {
    padding: 49px 70px 33px;
  }
}

@media screen and (max-width: 991px) {
  .work-single-item {
    margin-left: -15px;
    padding: 15px 30px;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
    grid-template-columns: 1fr 1fr;
    margin-right: 0;
  }
  .work-single-item {
    margin-left: 0;
  }
}

@media screen and (max-width: 479px) {
  .work-single-item {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
  }
}

.text-up-animation,
.img-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.img-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

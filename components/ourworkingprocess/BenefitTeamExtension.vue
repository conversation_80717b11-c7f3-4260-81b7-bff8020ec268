<script setup>
const teamExtentions = ref([
  {
    image: "/ourworkingprocess/benefit-team-extension/feel-like.svg",
    title: "Feel Like an In-House Team",
    description:
      "Our extended team integrates seamlessly with your existing processes and culture, ensuring a collaborative and familiar working environment.",
  },
  {
    image:
      "/ourworkingprocess/benefit-team-extension/access-top-experienced.svg",
    title: "Access to the Top Experienced Developers",
    description:
      "Work with highly skilled and vetted professionals who bring deep expertise and proven experience to your projects.",
  },
  {
    image: "/ourworkingprocess/benefit-team-extension/quick-hiring.svg",
    title: "Quick Hiring Process",
    description:
      "Avoid lengthy recruitment cycles with our streamlined process to onboard top talent in a matter of days.",
  },
  {
    image: "/ourworkingprocess/benefit-team-extension/smooth-communication.svg",
    title: "Smooth Communication",
    description:
      "Ensure clear and consistent communication with dedicated professionals who align with your time zones and preferred tools.",
  },
  {
    image: "/ourworkingprocess/benefit-team-extension/time-cost-saving.svg",
    title: "Time & Cost Saving",
    description:
      "Save valuable time and reduce costs by leveraging pre-screened talent without the overhead of full-time employment.",
  },
  {
    image: "/ourworkingprocess/benefit-team-extension/scalable-workforce.svg",
    title: "Scalable Workforce",
    description:
      "Quickly scale your team up or down based on project demands without long-term commitments, providing flexibility for both short-term projects and long-term growth.",
  },
  {
    image:
      "/ourworkingprocess/benefit-team-extension/geographic-flexibility.svg",
    title: "Geographic Flexibility",
    description:
      "Build a distributed or hybrid team by accessing talent across different regions, enabling 24/7 productivity and catering to global markets.",
  },
]);
const headerTitle = `<p class="primary-p-tag text-up-animation font-bold leading-[30px] max-w-[197px]"><span>Benefit of</span> <br/> <span class="instrument-italic">Team Extension</span></p>`;
</script>
<template>
  <OurworkingprocessComBenefit
    :header-title="headerTitle"
    :core-values="teamExtentions"
  />
</template>

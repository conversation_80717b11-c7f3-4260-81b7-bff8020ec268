<script setup>
const props = defineProps({
  ourServices: {
    type: Array,
    default: () => [],
  },
  ourServicesHeight: {
    type: Function,
    default: () => {},
  },
  specificValuesSelect: {
    type: Function,
    default: () => {},
  },
  arrowRightToBottom: {
    type: Boolean,
    default: false,
  },
});

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(async () => {
  await nextTick(); // Wait for the DOM to render
  viewPortEnter();
  props.ourServicesHeight();
  window.addEventListener("resize", props.ourServicesHeight);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", props.ourServicesHeight);
});
</script>

<template>
  <div class="grid-item gap-70 w-full">
    <div
      v-for="(ourService, index) in ourServices"
      :key="index"
      class="pb-10 border-b border-[#ffffff1a] text-up-animation last:border-b-0"
    >
      <div
        class="flex items-start justify-between md:space-x-[58px] space-x-[18px]"
      >
        <div
          class="flex flex-col"
          :class="ourService.slectedItem ? 'space-y-5' : 'space-y-0'"
        >
          <h3
            class="third-h-tag cursor-pointer"
            @click.stop="specificValuesSelect(ourService.id)"
          >
            {{ ourService.title }}
          </h3>
          <div
            class="overflow-hidden transition-all duration-500 ease-in-out"
            :class="
              ourService.slectedItem
                ? 'description-section-expand'
                : 'description-section'
            "
            :style="{
              '--elementHeight': `${ourService.height}px`,
            }"
          >
            <ClientOnly>
              <p
                :id="ourService.id"
                class="third-p-tag"
                v-html="ourService.description"
              ></p>
            </ClientOnly>
          </div>
        </div>
        <img
          class="w-13 h-13 cursor-pointer transform transition-all duration-500 ease-in-out"
          :class="
            arrowRightToBottom
              ? ourService.slectedItem
                ? 'rotate-0'
                : 'rotate-180'
              : ourService.slectedItem
              ? 'rotate-0'
              : 'rotate-180'
          "
          src="/public/ourworkingprocess/up-arrow-with-bg.svg"
          alt="up-arrow-with-bg"
          @click.stop="specificValuesSelect(ourService.id)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 49px;
}
.description-section {
  height: 0;
}
.description-section-expand {
  height: var(--elementHeight);
}
</style>

<script setup>
const experts = ref([
  {
    title: "Timely Delivery",
    image: "/ourworkingprocess/what-to-expect/timely-delivery.svg",
  },
  {
    title: "Budget Friendly",
    image: "/ourworkingprocess/what-to-expect/budget-friendly.svg",
  },
  {
    title: "Quality & Data Security",
    image: "/ourworkingprocess/what-to-expect/quality-data-security.svg",
  },
  {
    title: "Scalable Architecture",
    image: "/ourworkingprocess/what-to-expect/scalable-architecture.svg",
  },
  {
    title: "Long Term-viability",
    image: "/ourworkingprocess/what-to-expect/long-term-viability.svg",
  },
  {
    title: "24/7 Support",
    image: "/ourworkingprocess/what-to-expect/24-7-support.svg",
  },
]);

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-animation");
  const textLeftAnimation = document.querySelectorAll(".text-left-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textLeftAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textLeftAnimation.forEach((item) => {
    textLeftAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div class="w-fill flex flex-col items-center md:mt-[180px] mt-[100px]">
    <div class="w-full overflow-hidden text-left">
      <h2 class="our-process-secondary-h-tag text-left title-animation">
        <span class="text-[#999]">What to</span>
        <span class="instrument-italic"> Expect</span>
      </h2>
    </div>
    <div class="four-grid-item md:mt-[80px] mt-[40px] w-full">
      <div
        v-for="(expert, index) in experts"
        :key="index"
        class="text-left-animation"
      >
        <div
          class="md:pb-[40px] pb-[30px] flex flex-col items-center space-y-5"
        >
          <img
            class="w-[58px] h-[58px]"
            :src="expert.image"
            alt="Simplification"
          />
          <h2 class="md:text-2xl text-xl font-bold text-[#F1F1F2] text-center">
            {{ expert.title }}
          </h2>
        </div>
        <div class="h-[1px] w-full px-20">
          <div class="w-full h-full bg-[#ffffff1a]"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.four-grid-item {
  grid-column-gap: 40px;
  grid-row-gap: 50px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid-item {
    grid-column-gap: 40px;
    grid-row-gap: 50px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}

.title-animation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-animation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}

.text-left-animation {
  transition: all 0.8s ease-in-out;
  transform: translateX(-45px);
  opacity: 0;
}
.text-left-animation.final {
  transform: translateX(0px);
  opacity: 1;
}
</style>

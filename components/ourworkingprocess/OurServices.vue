<script setup>
import ServiceAnimation from "./ServiceAnimation.vue";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  ourServices: {
    type: Array,
    default: () => [],
  },
  ourServicesHeight: {
    type: Function,
    default: () => {},
  },
  specificValuesSelect: {
    type: Function,
    default: () => {},
  },
});
</script>

<template>
  <div class="md:mt-[200px] mt-[100px]">
    <div class="case-grid-item md:mt-[80px] mt-[40px]">
      <div
        class="title-up-animation our-process-secondary-h-tag !text-[#999]"
        v-html="title"
      ></div>
      <ServiceAnimation
        :ourServices="ourServices"
        :ourServicesHeight="ourServicesHeight"
        :specificValuesSelect="specificValuesSelect"
      />
    </div>
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 40px;
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
}
.description-section {
  height: 0;
}
.description-section-expand {
  height: var(--elementHeight);
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

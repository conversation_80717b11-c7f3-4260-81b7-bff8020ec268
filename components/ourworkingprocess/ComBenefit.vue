<script setup>
const props = defineProps({
  coreValues: {
    type: Object,
    default: () => { },
  },
  headerTitle: {
    type: String,
    default: "",
  },
});
const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div class="">
    <!-- <h2 class="secondary-h-tag title-up-animation">
      <p class="text-[#999]">Benefit of</p>
      <p>{{ headerTitle }}</p>
    </h2> -->
    <div class="case-grid-item md:mt-[180px] mt-[40px]">
      <div v-html="headerTitle"></div>
      <div class="grid-item gap-70 w-full">
        <div v-for="(coreValue, index) in coreValues" :key="index" :class="[
          'pb-10 text-up-animation border-b border-[#ffffff1a]',
          // Remove border in md+ screen for the last 2 visual items
          coreValues.length >= 4 && (
            (coreValues.length % 2 === 0 && index >= coreValues.length - 2) ||
            (coreValues.length % 2 !== 0 && index === coreValues.length - 1)
          ) ? 'md:border-b-0' : '',
          // Always remove border on last item in mobile (1 column)
          index === coreValues.length - 1 ? 'border-b-0' : ''
        ]">
          <div class="flex items-start">
            <img v-if="coreValue.image" class="md:w-[32px] md:h-[32px] w-[24px] h-[24px] mr-5" :src="coreValue.image"
              :alt="coreValue.title" />
            <div class="flex flex-col space-y-2.5 max-w-[390px]">
              <h2 class="third-h-tag">
                {{ coreValue.title }}
              </h2>
              <p :class="{ 'max-w-[338px]': coreValue.image }" class="forth-p-tag">
                {{ coreValue.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}

.grid-item.gap-70 {
  grid-column-gap: 67px;
  grid-row-gap: 40px;
}

@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }

  .grid-item {
    grid-template-columns: 1fr;
  }
}

.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}

.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

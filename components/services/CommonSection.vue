<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  title1: {
    type: String,
    default: "",
  },
  title2: {
    type: String,
    default: "",
  },
  fullTimes: {
    type: Array,
    default: () => [],
  },
});

const sectionViewPort = () => {
  // what makes us difference section animation
  const titleAnimation = document.querySelectorAll(".title-aimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  titleAnimation.forEach((item) => {
    observerThree.observe(item);
  });

  const descripAnimation = document.querySelectorAll(".descrip-aimation");
  const observerFour = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  descripAnimation.forEach((item) => {
    observerFour.observe(item);
  });
};

onMounted(() => {
  sectionViewPort();
});
</script>

<template>
  <div class="relative">
    <!-- <div class="absolute top-0 right-0 left-0 flex justify-center">
          <img
            src="/public/landing//644a1c92b49983ca116cd5f2_Rectangle 17714.webp"
            alt="Why use Full-time Developers & Tech Solutions from Devxhub?"
          />
        </div> -->
    <div class="flex flex-col z-10">
      <!-- <div class="md:mb-[140px] mb-[40px]">
        <div class="overflow-hidden">
          <p
            class="title-aimation sixth-h-tag font-bold text-[#F1F1F2]"
            v-html="title1"
          ></p>
        </div>
        <div class="overflow-hidden">
          <p
            class="title-aimation sixth-h-tag font-bold text-[#F1F1F2]"
            v-html="title2"
          ></p>
        </div>
      </div> -->
      <div class="three-grid-item">
        <div>
          <div class="overflow-hidden">
            <p class="title-aimation text-[20px] leading-[30px] font-bold text-[#999]" v-html="title"></p>
          </div>
          <div class="overflow-hidden">
            <p class="title-aimation text-xl font-bold text-[#F1F1F2]" v-html="title1"></p>
          </div>
          <div class="overflow-hidden">
            <p class="title-aimation text-xl instrument-italic text-[#F1F1F2]" v-html="title2"></p>
          </div>
        </div>
        <div class="flex flex-col w-full ">
          <div v-for="fullTime in fullTimes" :key="fullTime.id"
            class="flex space-x-6 text-[#F1F1F2] min-[992px]:items-center w-full border-b last:border-b-0 border-[#ffffff1a] pb-[40px] last:pb-0 mt-[34px]">
            <img class="descrip-aimation w-[40px] h-[40px] min-[992px]:mt-0 mt-0.5" :src="fullTime.img"
              :alt="fullTime.title" />
            <div class="text-grid-item flex-grow justify-start items-center">
              <p class="descrip-aimation text-xl font-aeonik leading-7 font-bold">
                {{ fullTime.title }}
              </p>
              <div class="bg-[#1A1139] z-[1] descrip-aimation w-full flex justify-start">
                <p class="text-base text-[#999] leading-6  font-aeonik min-[992px]:max-w-[413px]">
                  {{ fullTime.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.three-grid-item {
  /* z-index: 0; */
  grid-column-gap: 283px;
  /* grid-row-gap: 106px; */
  grid-template-rows: auto auto;
  grid-template-columns: 0.2fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}

.text-grid-item {
  /* z-index: 0; */
  grid-column-gap: 55px;
  /* grid-row-gap: 106px; */
  grid-template-rows: auto auto;
  grid-template-columns: 0.7fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}

@media screen and (max-width: 1369px) {
  .three-grid-item {
    grid-column-gap: 183px;
  }
}

@media screen and (max-width: 1279px) {
  .three-grid-item {
    /* z-index: 0; */
    grid-column-gap: 43px;
    /* grid-row-gap: 106px; */
    grid-template-rows: auto auto;
    grid-template-columns: 0.3fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
    position: relative;
  }
}

@media screen and (max-width: 991px) {
  .three-grid-item {
    grid-column-gap: 0px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr;
  }

  .text-grid-item {
    /* z-index: 0; */
    grid-column-gap: 0px;
    grid-row-gap: 20px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    display: grid;
    position: relative;
  }
}

@media screen and (max-width: 767px) {
  .three-grid-item {
    /* grid-row-gap: 88px; */
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 479px) {
  .three-grid-item {
    grid-template-columns: 1fr;
  }
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}

.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}

.descrip-aimation {
  transform: translateY(70px);
  opacity: 0;
  transition: all 0.8s ease-in-out;
}

.descrip-aimation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

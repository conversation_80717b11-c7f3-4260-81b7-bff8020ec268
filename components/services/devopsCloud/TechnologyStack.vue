<script setup lang="ts">
import { ref } from "vue";

const versionControls = ref([
  {
    id: 1,
    title: "Git",
    image: "/services/full-stack/tech-stack/git.svg",
  },
  {
    id: 2,
    title: "GitHub",
    image: "/services/full-stack/tech-stack/github.svg",
  },
  {
    id: 3,
    title: "GitLab",
    image: "/services/full-stack/tech-stack/gitlab.svg",
  },
  {
    id: 4,
    title: "Bitbucket",
    image: "/services/full-stack/tech-stack/bitbucket.svg",
  },

  // {
  //   id: 8,
  //   title: "Ruby",
  //   image: "/services/full-stack/tech-stack/ruby.png",
  // },
  // {
  //   id: 10,
  //   title: "",
  //   image: "/services/full-stack/tech-stack/go.webp",
  // },
  // {
  //   id: 11,
  //   title: "Kotlin",
  //   image: "/services/full-stack/tech-stack/kotlin.png",
  // },
  // {
  //   id: 12,
  //   title: "Rust",
  //   image: "/services/full-stack/tech-stack/rust.webp",
  // },
  // {
  //   id: 13,
  //   title: "Scala",
  //   image: "/services/full-stack/tech-stack/scala.png",
  // },
  // {
  //   id: 15,
  //   title: "R",
  //   image: "/services/full-stack/tech-stack/r.png",
  // },
  // {
  //   id: 16,
  //   title: "Perl",
  //   image: "/services/full-stack/tech-stack/perl.png",
  // },
  // {
  //   id: 17,
  //   title: "Lua",
  //   image: "/services/full-stack/tech-stack/lua.png",
  // },
  // {
  //   id: 18,
  //   title: "Haskell",
  //   image: "/services/full-stack/tech-stack/haskell.png",
  // },
  // {
  //   id: 19,
  //   title: "MATLAB",
  //   image: "/services/full-stack/tech-stack/matlab.png",
  // },
  // {
  //   id: 20,
  //   title: "Julia",
  //   image: "/services/full-stack/tech-stack/julia.png",
  // },
  // {
  //   id: 21,
  //   title: "Elixir",
  //   image: "/services/full-stack/tech-stack/elixir.png",
  // },
  // {
  //   id: 22,
  //   title: "Groovy",
  //   image: "/services/full-stack/tech-stack/groovy.png",
  // },
  // {
  //   id: 23,
  //   title: "Clojure",
  //   image: "/services/full-stack/tech-stack/clojure.png",
  // },
  // {
  //   id: 24,
  //   title: "F#",
  //   image: "/services/full-stack/tech-stack/fsharp.svg",
  // },
]);

const Configurations = ref([
  {
    id: 1,
    title: "",
    image: "/services/full-stack/tech-stack/ansible.png",
  },
  {
    id: 2,
    title: "",
    image: "/services/full-stack/tech-stack/chef.svg",
  },
  {
    id: 3,
    title: "",
    image: "/services/full-stack/tech-stack/puppet.svg",
  },
]);

const infrastructures = ref([
  {
    id: 1,
    title: "Terraform",
    image: "/services/full-stack/tech-stack/teraform.svg",
  },
  {
    id: 2,
    title: "AWS CloudFormation",
    image: "/services/full-stack/tech-stack/aws-cloudformation.svg",
  },
]);

const containerization = ref([
  {
    id: 1,
    title: "",
    image: "/services/full-stack/tech-stack/docker.png",
  },
  {
    id: 2,
    title: "Kubernetes",
    image: "/services/full-stack/tech-stack/kubernates.png",
  },
]);

const techStackMobileTechnologies = ref([
  {
    id: 1,
    title: "Prometheus",
    image: "/services/full-stack/tech-stack/prometheus.svg",
  },
  {
    id: 2,
    title: "",
    image: "/services/full-stack/tech-stack/grafana.svg",
  },
  {
    id: 3,
    title: "ELK Stack",
    image: "/services/full-stack/tech-stack/elk-stack.svg",
  },
]);

const securities = ref([
  {
    id: 1,
    title: "Vault",
    image: "/services/full-stack/tech-stack/vault.svg",
  },
  {
    id: 2,
    title: "SonarQube",
    image: "/services/full-stack/tech-stack/sonar-qube.svg",
  },
]);

const clouds = ref([
  {
    id: 1,
    title: "AWS",
    image: "/services/full-stack/tech-stack/aws.png",
  },
  {
    id: 2,
    title: "Azure",
    image: "/services/full-stack/tech-stack/aurora.png",
  },
  {
    id: 3,
    title: "Google Cloud Platform",
    image: "/services/full-stack/tech-stack/google-cloud.svg",
  },
  {
    id: 4,
    title: "Linux server",
    image: "/services/full-stack/tech-stack/linux.png",
  },
  {
    id: 5,
    title: "Windows server",
    image: "/services/full-stack/tech-stack/windows.svg",
  },
  {
    id: 6,
    title: "Digital Ocean",
    image: "/services/full-stack/tech-stack/ocean.png",
  },
]);

const cicdTools = ref([
  {
    id: 1,
    title: "Jenkins",
    image: "/services/full-stack/tech-stack/jenkins.svg",
  },
  {
    id: 2,
    title: "GitLab CI/CD",
    image: "/services/full-stack/tech-stack/gitlab.svg",
  },
  {
    id: 3,
    title: "",
    image: "/services/full-stack/tech-stack/circle-ci.png",
  },
]);
</script>
<template>
  <div class="md:mt-[180px] mt-[100px]">
    <h2 class="sixth-h-tag">Technology stack</h2>

    <div class="flex gap-2.5 md:gap-10 lg:gap-[55px] mt-20">
      <div class="h-auto bg-[#999999] w-[5px]"></div>

      <div class="space-y-20">
        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Version Control & Collaboration</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in versionControls"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">CI/CD Tools</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="aiMl in cicdTools"
              :key="aiMl.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold"
            >
              <img
                :class="aiMl.title === '' ? 'max-h-[86px]' : 'max-h-[58px]'"
                :src="aiMl.image"
                :alt="aiMl.title"
              />
              <p class="whitespace-nowrap">
                {{ aiMl.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Infrastructure as Code (IaC)</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in infrastructures"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[125px] flex flex-col py-4 justify-center items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Configuration Management</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in Configurations"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-center items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Containerization & Orchestration</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in containerization"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Monitoring & Logging</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackMobileTechnologies"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Security & Compliance</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in securities"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Cloud & DevOps</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in clouds"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[125px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>

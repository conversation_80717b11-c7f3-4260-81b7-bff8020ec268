<script setup>
const viewPortEnter = () => {
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 2% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>
<template>
  <div class="w-full md:mt-[180px] mt-[80px] flex flex-col items-start">
    <h2
      class="text-up-animation w-full text-center sixth-h-tag font-[700] text-[#F1F1F2]"
    >
      <span class="instrument-italic">Cloud Confidence </span>
      <span class="text-[#999]">Starts Here</span>
    </h2>
    <div class="mt-[32px] text-[#999] text-xl max-w-[1112px] mx-auto">
      <p class="text-up-animation text-center">
        Modern infrastructure is within reach. Let's build scalable, secure
        systems together.​ Reach <NAME_EMAIL>​
      </p>
      <p class="text-up-animation text-center">
        Talk to our DevOps team and bring your cloud transformation to
        life—starting today.
      </p>
    </div>
  </div>
</template>

<style scoped>
.text-up-animation {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.text-up-animation.final {
  transform: translateY(0px);
}
</style>

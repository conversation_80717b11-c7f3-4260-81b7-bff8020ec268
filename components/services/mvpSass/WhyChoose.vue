<script setup>
const headerTitle = ref(`<div class="overflow-hidden">
          <p class="text-[20px] leading-[30px] text-[#999999] !title-animation">Why Choose</p>
          <p class="text-[20px] leading-[30px]  text-[#999999] !title-animation"><span class="instrument-italic">Devxhub</span> for <span class='instrument-italic'>MVP, SaaS & End-to-End</span></p>
          <p class="text-[20px] leading-[30px]  text-[#999999] !title-animation">Developement?</p>
            </div>
            `);

const whyChooseDevxhub = ref([
  {
    image: "/services/mobile-app/why-choose/leadership.svg",
    title: "Product-Minded Execution",
    description:
      "We think like product owners. Every build decision is guided by your users' needs, time to market, and future scalability.",
  },
  {
    image: "/services/mobile-app/why-choose/full-stack-mobile-expertise.svg",
    title: "Startup-Ready Speed",
    description:
      "Our agile approach ensures rapid prototyping, fast sprints, and lean MVP launches—ideal for startups and evolving businesses.",
  },
  {
    image: "/services/mobile-app/why-choose/cost-effective.svg",
    title: "Global Talent, Local Insight",
    description:
      "We combine Bangladesh's best engineering minds with international product strategy, offering world-class quality at optimized costs.",
  },
  {
    image: "/services/mobile-app/why-choose/customize-high-tech-solutions.svg",
    title: "Scalable SaaS Solutions",
    description:
      "Whether there are 100 or 100,000 users, we build with scalability from day one, ensuring your platform grows smoothly with your user base",
  },
  {
    image: "/services/mobile-app/why-choose/ongoing-commmunication.svg",
    title: "Transparent Communication",
    description:
      "From roadmap to release, we keep you informed with real-time updates, sprint demos, and clear milestones.",
  },
  {
    image: "/services/mobile-app/why-choose/end-to-end-mobile-support.svg",
    title: "Full Product Lifecycle Support",
    description:
      "From ideation to post-launch maintenance, we stay with you throughout the journey—refining, iterating, and optimizing your platform.",
  },
]);

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  // what makes us difference section animation
  const headerTitleAnimation = document.querySelectorAll(".title-aimation");
  console.log(headerTitleAnimation, "headerTitleAnimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  headerTitleAnimation.forEach((item) => {
    observerThree.observe(item);
  });
};

onMounted(() => {
  setTimeout(() => {
    viewPortEnter();
  }, 1000);
});
</script>

<template>
  <div>
    <ServicesWhyChooseDevxhub
      class="relative md:mt-[180px] mt-[120px]"
      :header-title="headerTitle"
      :core-values="whyChooseDevxhub"
    />
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 65px;
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
</style>

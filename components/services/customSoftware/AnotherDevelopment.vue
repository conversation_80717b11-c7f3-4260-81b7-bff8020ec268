<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { nextTick, onBeforeUnmount, onMounted, ref } from "vue";

const breakpoints = useBreakpoints(breakpointsTailwind);
const isDesktop = breakpoints.greaterOrEqual("lg");

// Add interface for expertise area
interface ExpertiseArea {
  id: string;
  image: string;
  text: string;
  description: string;
  backgroundColor: string;
  margin: number;
  slectedItem: boolean;
  height?: number; // Add optional height property
}

const heading = `<span class="md:text-[36px] text-[24px]  font-bold leading-[48px] text-[#999]"><span class="instrument-italic">Enterprise Software Development 
</span> Services</span>`;

// Update ref type
const expertiseAreas = ref<ExpertiseArea[]>([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/end-to-end-development.png",
    text: "Enterprise Resource Planning (ERP) Systems",
    description:
      "Integrated platforms for managing core business processes like finance, inventory, and HR.",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/frontend-development.png",
    text: "Customer Relationship Management (CRM) Software",
    description:
      "Tools to track leads, sales, and customer interactions for better relationship management.",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/backend-development.png",
    text: "Enterprise Content Management (ECM)",
    description:
      "Centralized systems for storing, organizing, and retrieving digital content securely.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/mobile-app-development.png",
    text: "Business Intelligence (BI) & Analytics Software",
    description:
      "Data-driven tools that turn raw data into actionable business insights.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea5",
    image: "/services/full-stack/mvp-saas.png",
    text: "Workflow Automation Solutions",
    description:
      "Automated systems that eliminate manual tasks and improve process efficiency.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea6",
    image: "/services/full-stack/ai-ml-devops.png",
    text: "Human Resource Management Systems (HRMS)",
    description:
      "Comprehensive tools for managing employee data, payroll, recruitment, and performance.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea7",
    image: "/services/full-stack/maintenance-support.png",
    text: "Enterprise Mobile App Development",
    description:
      "Mobile applications designed to support enterprise operations and improve workforce productivity.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea8",
    image: "/services/full-stack/maintenance-support.png",
    text: "Supply Chain Management (SCM) Software",
    description:
      "Solutions to manage logistics, inventory, suppliers, and order fulfillment across the supply chain.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea9",
    image: "/services/full-stack/maintenance-support.png",
    text: "Enterprise Data Management Solutions",
    description:
      "Systems to organize, govern, and protect large volumes of enterprise data.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea10",
    image: "/services/full-stack/maintenance-support.png",
    text: "Custom Intranet/Portal Development",
    description:
      "Private networks and portals that improve internal communication and collaboration.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const calculateHeights = () => {
  // Loop through expertiseAreas to calculate heights
  expertiseAreas.value.forEach((area) => {
    const element = document.getElementById(area.id);
    if (element) {
      area.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id: string) => {
  console.log(id, "here the id");
  expertiseAreas.value.forEach((expertiseArea) => {
    if (expertiseArea.id === id) {
      expertiseArea.slectedItem = !expertiseArea.slectedItem;
    } else {
      expertiseArea.slectedItem = false;
    }
  });
};
const viewPortController = () => {
  const allPointSection = document.querySelectorAll(".point-text");
  const title = document.getElementById("title");

  // First Observer
  const titleobserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  // Add null check before observing
  if (title) {
    titleobserver.observe(title);
  }

  // Second Observer
  const observerTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        } else {
          // entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  allPointSection.forEach((item) => {
    observerTwo.observe(item);
  });
};
onMounted(async () => {
  viewPortController();
  await nextTick(); // Wait for the DOM to render
  calculateHeights();
  window.addEventListener("resize", calculateHeights);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateHeights);
});
</script>
<template>
  <div class="md:mt-[180px] mt-[100px]">
    <div>
      <h2 class="text-up-animation" v-html="heading"></h2>
      <div class="flex">
        <p
          class="pt-5 text-[#999] text-2xl font-normal text-up-animation max-w-[787px]"
        >
          Advanced software solutions that support large-scale operations and
          complex business needs.
        </p>
      </div>
    </div>
    <div class="flex flex-col mt-[52px]">
      <div
        v-for="(expertiseArea, index) in expertiseAreas"
        :key="expertiseArea.id"
        class="flex items-center lg:space-x-[86px] md:space-x-[26px] space-x-10 border-b-[0.5px] border-[#ffffff1a] single-item py-7 hover:py-9 last:border-b-0"
      >
        <p
          class="hidden lg:flex flex-col self-center point-text point-text-gradient !ml-0 transition-all duration-500 ease-in-out"
        >
          {{ index + 1 < 10 ? "0" + (index + 1) : index + 1 }}
        </p>
        <p
          class="block lg:hidden point-text point-text-gradient mt-[13px] !ml-0 transition-all duration-500 ease-in-out"
          :class="
            isDesktop
              ? ''
              : expertiseArea.slectedItem
              ? 'mobile-point-text-gradient final'
              : 'mobile-initial-point-text-gradient final'
          "
          @click.stop="isDesktop ? '' : specificValuesSelect(expertiseArea.id)"
        >
          {{ index + 1 < 10 ? "0" + (index + 1) : index + 1 }}
        </p>
        <div
          class="h-[80px] lg:h-[112px] overflow-hidden element-des transition-all duration-500 ease-in-out grow"
          :class="
            isDesktop
              ? ''
              : expertiseArea.slectedItem
              ? 'mobile-element-des'
              : 'mobile-initial-element-des'
          "
          :style="{ '--elementHeight': `${expertiseArea.height}px` }"
        >
          <div :id="expertiseArea.id" class="flex justify-between items-center">
            <div class="flex flex-col space-y-5">
              <div
                class="flex items-center justify-between h-[100px] lg:h-[112px]"
                @click.stop="
                  isDesktop ? '' : specificValuesSelect(expertiseArea.id)
                "
              >
                <p
                  class="text-[#7D7D82] xl:text-5xl xl:leading-[56px] lg:text-3xl text-2xl expertiseArea-title font-bold transition-all duration-500 ease-in-out md:white-space-nowrap max-w-[856px]"
                  :class="
                    isDesktop
                      ? ''
                      : expertiseArea.slectedItem
                      ? 'mobile-expertiseArea-title'
                      : 'mobile-initial-expertiseArea-title'
                  "
                >
                  {{ expertiseArea.text }}
                </p>
              </div>
              <p
                class="text-[#999999] text-[24px] leading-[32px] max-w-[655px]"
              >
                {{ expertiseArea.description }}
              </p>
            </div>
          </div>
        </div>

        <img
          class="cursor-pointer transform transition-all duration-500 ease-in-out right-sight-rotate-image hidden md:block rounded-[10px]"
          :src="expertiseArea.image"
          alt="up-arrow-with-bg"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.point-text {
  font-size: 24px;
  height: 26px;
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.point-text-gradient {
  background: linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  background: -webkit-linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.point-text.final {
  transform: translateY(0px) !important;
}
.right-sight-rotate-image {
  @apply w-[137px] h-[100px];
  transition: all 0.5s ease-in-out;
}
.single-item:hover .right-sight-rotate-image {
  @apply w-[200px] lg:w-[251px] h-[130px] lg:h-[182px];
  transform: rotate(-16.9deg);
}
.single-item:hover .point-text-gradient {
  background: linear-gradient(158deg, #999 10.67%, #999 84.22%);
  background: -webkit-linear-gradient(158deg, #999 10.67%, #999 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.single-item:hover .element-des {
  height: var(--elementHeight);
}
.single-item:hover .expertiseArea-title {
  color: white;
}
#title {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
#title.final {
  transform: translateY(0px);
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
@media (max-width: 767px) {
  .mobile-initial-element-des {
    height: 100px !important;
  }
  .mobile-element-des {
    height: var(--elementHeight) !important;
  }
  .mobile-initial-point-text-gradient {
    background: linear-gradient(
      158deg,
      #833ab4 10.67%,
      #ffd700 84.22%
    ) !important;
    background: -webkit-linear-gradient(
      158deg,
      #833ab4 10.67%,
      #ffd700 84.22% !important
    );
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-fill-color: transparent !important;
  }
  .mobile-point-text-gradient {
    background: linear-gradient(158deg, #999 10.67%, #999 84.22%) !important;
    background: -webkit-linear-gradient(
      158deg,
      #999 10.67%,
      #999 84.22%
    ) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-fill-color: transparent !important;
  }

  .mobile-expertiseArea-title {
    color: white !important;
  }
  .mobile-initial-expertiseArea-title {
    color: #7d7d82 !important;
  }
}
</style>

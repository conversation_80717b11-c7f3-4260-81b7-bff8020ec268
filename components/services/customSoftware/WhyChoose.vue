<script setup>
const headerTitle = ref(`<div class="overflow-hidden">
              <p class="text-[20px] text-[#999999] !title-animation max-w-[230px]">
            Why Choose Devxhub for<span class="text-[#F1F1F2] instrument-italic"> Custom & Enterprise Software </span> for Development?
          </p>
            </div>
            `);
const whyChooseDevxhub = ref([
  {
    image: "/services/custom-software-development/why-choose/leadership.svg",
    title: "Leadership",
    description:
      "We lead every project with a clear focus on solving complex business problems through tailored digital solutions that create long-term value.",
  },
  {
    image: "/services/custom-software-development/why-choose/enterprise.svg",
    title: "Enterprise-Grade Expertise",
    description:
      "Our team specializes in developing secure, scalable, and high-performance software for startups, SMEs, and large enterprises across various industries.",
  },
  {
    image:
      "/services/custom-software-development/why-choose/cost-effective.svg",
    title: "Cost‑Effective Delivery",
    description:
      "We offer premium solutions at competitive rates by leveraging top-tier global talent, particularly from Bangladesh.",
  },
  {
    image: "/services/custom-software-development/why-choose/scalable.svg",
    title: "Scalable Solutions",
    description:
      "With our custom software, you do not have to worry about off-the-shelf limitations. Every solution is customized to your workflows, users, and future growth goals.",
  },
  {
    image: "/services/custom-software-development/why-choose/collaborative.svg",
    title: "Collaborative Communication",
    description:
      "We keep you in the loop with transparent, consistent updates and clear milestones, ensuring you stay informed and in control from day one.",
  },
  {
    image:
      "/services/custom-software-development/why-choose/lifecycle-support.svg",
    title: "Complete Lifecycle Support",
    description:
      "From discovery and design to development, deployment, and beyond, Devxhub provides full-cycle support for seamless execution and long-term success.",
  },
]);

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  // what makes us difference section animation
  const headerTitleAnimation = document.querySelectorAll(".title-aimation");
  console.log(headerTitleAnimation, "headerTitleAnimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  headerTitleAnimation.forEach((item) => {
    observerThree.observe(item);
  });
};

onMounted(() => {
  setTimeout(() => {
    viewPortEnter();
  }, 1000);
});
</script>

<template>
  <div class="">
    <!-- <div class="fifth-h-tag">
      <span class="text-[#999999]">Why Choose</span> <br />
      Devxhub <br />
      <span class="text-[#999999]">for</span> Full Stack <br />
      <span class="text-[#999999]">Development?</span>
    </div> -->
    <!-- <div class="grid-item gap-70 w-full">
      <div
        v-for="(coreValue, index) in whyChooseDevxhub"
        :key="index"
        class="pb-10 border-b border-[#ffffff1a] text-up-animation last:border-b-0"
        :class="{ 'border-b-0': index === 4 }"
      >
        <div class="flex flex-col items-start">
          <img
            v-if="coreValue.image"
            class="md:size-[60px] size-[24px]"
            :src="coreValue.image"
            :alt="coreValue.title"
          />
          <h2 class="third-h-tag mt-5 mb-2.5">
            {{ coreValue.title }}
          </h2>
          <p class="third-p-tag">
            {{ coreValue.description }}
          </p>
        </div>
      </div>
    </div> -->
    <ServicesWhyChooseDevxhub
      class="relative md:mt-[180px] mt-[120px]"
      :header-title="headerTitle"
      :core-values="whyChooseDevxhub"
    />
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 65px;
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
</style>

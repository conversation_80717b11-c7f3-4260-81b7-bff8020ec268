<script setup lang="ts">
import ServiceAnimation from "~/components/ourworkingprocess/ServiceAnimation.vue";

interface Faq {
  id: number;
  slectedItem: boolean;
  title: string;
  description: string;
  height?: number;
}

const ourServices = ref<Faq[]>([
  {
    id: 101,
    slectedItem: true,
    title: "How does enterprise software differ from standard business apps?",
    description: `<p>Enterprise software is designed for large-scale operations. Its enhanced scalability, security, and integration support complex organizational needs across departments.</p>`,
  },
  {
    id: 102,
    slectedItem: false,
    title: "For whom is Devxhub's custom and enterprise software suitable?",
    description: `<p>Our reliable, scalable, and future-ready digital systems can benefit start-ups, SMEs, and enterprises across industries.</p>`,
  },
  {
    id: 103,
    slectedItem: false,
    title: "How long does it take to build a custom software solution?",
    description: `<p>Timelines vary depending on complexity, but most custom enterprise projects take 3 to 9 months to complete, including planning, development, testing, and deployment.</p>`,
  },
  {
    id: 104,
    slectedItem: false,
    title: "Can Devxhub modernize our legacy software?",
    description: `<p>Yes. Using new technologies, we upgrade outdated systems for improved performance, security, and usability.</p>`,
  },
  {
    id: 105,
    slectedItem: false,
    title: "Do you offer post-launch support and maintenance?",
    description: `<p>Absolutely. We provide end-to-end lifecycle support, including regular updates, security monitoring, bug fixes, and feature enhancements after deployment.</p>`,
  },
  {
    id: 106,
    slectedItem: false,
    title: "Can we integrate the new software with our existing systems?",
    description: `<p>Yes. Integration is one of our core strengths. We ensure your new software effortlessly connects with your current tools, APIs, databases, and workflows.</p>`,
  },
  {
    id: 107,
    slectedItem: false,
    title: "How does Devxhub ensure project transparency?",
    description: `<p>We maintain open communication with regular updates, clear timelines, progress reports, and collaborative tools so you're always informed and involved.</p>`,
  },
]);

const ourServicesHeight = () => {
  // Loop through expertiseAreas to calculate heights
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id.toString());
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id: number) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>
<template>
  <div class="md:mt-[200px] mt-[100px] container-fluid">
    <div class="case-grid-item md:mt-[80px] mt-[40px]">
      <div class="title-up-animation !text-[#999] max-w-[413px]">
        <h3 class="text-[#F1F1F2] our-process-secondary-h-tag">
          Any Queries? <br />
          Let’s be clear.
        </h3>
        <div
          class="w-full h-auto title-bg mt-10 px-8 pt-12 pb-7 flex flex-col justify-between aspect-[413/275]"
        >
          <p
            class="text-[#F1F1F2] text-[32px] font-bold leading-9 max-w-[304px]"
          >
            Find the right solution for you now
          </p>
          <a
            href="/contact-us#appointment"
            class="w-[197px] h-[46px] text-base text-[#1D1A20] bg-[#FFD700] rounded-full font-medium text-center leading-[46px] inline-block"
            >Book a Quick call</a
          >
        </div>
      </div>
      <ServiceAnimation
        :ourServices="ourServices"
        :ourServicesHeight="ourServicesHeight"
        :specificValuesSelect="specificValuesSelect"
      />
    </div>
  </div>
</template>

<style scoped>
.title-bg {
  background-image: url("~/assets/img/landing/faqBg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.case-grid-item {
  grid-column-gap: 129px;
  grid-row-gap: 40px;
  grid-template-rows: auto;
  grid-template-columns: 413px 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .case-grid-item {
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 40px;
  }
}
.description-section {
  height: 0;
}
.description-section-expand {
  height: var(--elementHeight);
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

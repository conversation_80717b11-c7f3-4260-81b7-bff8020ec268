<script setup>
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { computed } from "vue";
import { Carousel, Slide } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

// breakpoints
const breakpoints = useBreakpoints(breakpointsTailwind);
const isExtraLarge = breakpoints.greaterOrEqual("2xl");
const isSemiLarge = breakpoints.greaterOrEqual("xl");

const isLarge = breakpoints.greaterOrEqual("lg");
const isTab = breakpoints.greaterOrEqual("md");
const isSemiTab = breakpoints.greaterOrEqual("sm");
const route = useRoute();

// Helper function to normalize paths (remove trailing slashes)
const normalizePath = (path) => {
  return path.endsWith("/") ? path.slice(0, -1) : path;
};

const customBreakpoints = {
  "2300px": 2300,
  "1885px": 1885,
  "1440px": 1440,
};
const breakpointsCustom = useBreakpoints(customBreakpoints);
const isUltraWide = breakpointsCustom.greaterOrEqual("2300px");
const isLargeDesktop = breakpointsCustom.greaterOrEqual("1885px");
const isDesktop = breakpointsCustom.greaterOrEqual("1440px");

const show = ref(false);
const companies = ref([
  {
    id: 1,
    selected: false,
    title: "Enterprise",
    image: "/services/full-stack/expertise/enterprise.png",
    text: "enterprise",
    backgroundColor: "#FF5134",
    margin: 1.8,
  },
  {
    id: 2,
    selected: false,
    title: "Fintech",
    image: "/services/full-stack/expertise/fintech.png",
    text: "fintech",
    backgroundColor: "#3F2039",
    margin: 1.8,
  },
  {
    id: 3,
    selected: false,
    title: "Healthcare",
    image: "/services/full-stack/expertise/healthcare.png",
    text: "healthcare",
    backgroundColor: "#A8B431",
    margin: 1.8,
  },
  {
    id: 4,
    selected: false,
    title: "E-commerce",
    image: "/services/full-stack/expertise/e-commerce.png",
    text: "ecommerce",
    backgroundColor: "#8647F1",
    margin: 1.8,
  },

  {
    id: 5,
    selected: false,
    title: "Edtech",
    image: "/services/full-stack/expertise/edtech.png",
    text: "edtech",
    backgroundColor: "#EB8EE2",
    margin: 1.8,
  },
  {
    id: 6,
    selected: false,
    title: "Corporate",
    image: "/services/full-stack/expertise/corporate.png",
    text: "corporate",
    backgroundColor: "#00E1E6",
    margin: 1.8,
  },
  {
    id: 7,
    selected: false,
    title: "Agriculture",
    image: "/services/full-stack/expertise/agriculture.png",
    text: "agriculture",
  },
  {
    id: 8,
    selected: false,
    title: "Automotive",
    image: "/services/full-stack/expertise/auto-motive.png",
  },
  {
    id: 9,
    selected: false,
    title: "Energy",
    image: "/services/full-stack/expertise/energy.png",
    title: "energy",
  },
  {
    id: 10,
    selected: false,
    title: "ERP Solution",
    image: "/services/full-stack/expertise/erp-solution.png",
    text: "erp_solution",
  },
  {
    id: 11,
    selected: false,
    title: "Finance Banking",
    image: "/services/full-stack/expertise/finance-banking.png",
    text: "finance_banking",
  },
  {
    id: 12,
    selected: false,
    title: "Government Sector",
    image: "/services/full-stack/expertise/government-public-sector.png",
    text: "government_sector",
  },
  {
    id: 13,
    selected: false,
    title: "Hospitality",
    image: "/services/full-stack/expertise/hospitality.png",
    text: "hospitality",
  },
  {
    id: 14,
    selected: false,
    title: "IT",
    image: "/services/full-stack/expertise/information-technology.png",
    text: "it",
  },
  {
    id: 15,
    selected: false,
    title: "Manufacturing",
    image: "/services/full-stack/expertise/manufacturing.png",
    text: "manufacturing",
  },
  {
    id: 16,
    selected: false,
    title: "Miscellaneous",
    image: "/services/full-stack/expertise/miscellaneous.png",
    text: "miscellaneous",
  },
  {
    id: 17,
    selected: false,
    title: "Non Profit",
    image: "/services/full-stack/expertise/non-profit.png",
    text: "non_profit",
  },
  {
    id: 18,
    selected: false,
    title: "Real Estate",
    image: "/services/full-stack/expertise/real-estate.png",
    text: "real_estate",
  },
  {
    id: 19,
    selected: false,
    title: "Retail",
    image: "/services/full-stack/expertise/retail.png",
    text: "retail",
  },
  {
    id: 20,
    selected: false,
    title: "Software",
    image: "/services/full-stack/expertise/software.png",
    text: "software",
  },
  {
    id: 21,
    selected: false,
    title: "Startup",
    image: "/services/full-stack/expertise/startup.png",
    text: "startup",
  },
  {
    id: 22,
    selected: false,
    title: "Telecom",
    image: "/services/full-stack/expertise/telecom.png",
    text: "telecom",
  },
]);
const myCarousel = ref(null);
onMounted(() => {
  if (route.name === "index") {
    setTimeout(() => {
      show.value = true;
    }, 600);
  }
});
const showAll = ref(false);
const showRightArrow = ref(true);
const showLeftArrow = ref(false);
const displayedCompanies = computed(() => {
  // console.log(route.path, "route.path");
  const normalizedPath = normalizePath(route.path);

  if (normalizedPath === "/services/full-stack-development") {
    return companies.value.slice(0, 6);
  } else if (
    normalizedPath === "" ||
    normalizedPath === "/services" ||
    normalizedPath === "/services/ai-ml-development-integration" ||
    normalizedPath === "/services/custom-software-development"
  ) {
    return companies.value;
  }
  return [];
});

const forMobileCompanies = computed(() => {
  return showAll.value ? companies.value : companies.value.slice(0, 6);
});

const toggleShowMore = () => {
  showAll.value = !showAll.value;
};

const getLastSlideIndex = (cI) => {
  if (cI.currentSlideIndex + 6 === cI.slidesCount && isDesktop.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 5 === cI.slidesCount && isSemiLarge.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 4 === cI.slidesCount && isLarge.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 3 === cI.slidesCount && isTab.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 2 === cI.slidesCount && isSemiTab.value) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex + 2 === cI.slidesCount) {
    showRightArrow.value = false;
    showLeftArrow.value = true;
  } else if (cI.currentSlideIndex === 0) {
    showRightArrow.value = true;
    showLeftArrow.value = false;
  }
};

const toggleSelection = (id) => {
  const company = companies.value.find((c) => c.id === id);
  if (company) {
    company.selected = !company.selected;
  }
};
</script>

<template>
  <div class="lg:pt-[180px] md:pt-[84px] pt-[64px] overflow">
    <h3 class="font-bold sixth-h-tag text-[#F1F1F2]">
      <p>
        <span class="text-[#999]">Our Expertise in</span>
        <span class="instrument-italic"> Software</span>
      </p>
      <p>
        <span class="instrument-italic"> Development</span>
        <span class="text-[#999]"> Across </span>
        <span class="instrument-italic"> Industries</span>
      </p>
    </h3>
    <p class="fourth-p-tag text-[#999999] md:mt-5 mt-[10px] max-w-[738px]">
      Combining advanced technology and decades of industry insight, we design
      and develop bespoke full-cycle solutions tailored to deliver your unique
      software vision
    </p>
    <div class="md:hidden block mt-10">
      <div class="expertise-container">
        <div class="expertise-grid">
          <div
            v-for="company in forMobileCompanies"
            :key="company.id"
            @click="toggleSelection(company.id)"
          >
            <div class="relative">
              <div class="card-title">{{ company.title }}</div>
              <div class="h-[202px]">
                <img
                  :src="company.image"
                  :alt="company.title"
                  class="w-full h-full"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-if="route.path === '/'" class="see-more-container">
          <button @click="toggleShowMore" class="see-more-button">
            {{ showAll ? "See Less" : "See More" }}
          </button>
        </div>
      </div>
    </div>
    <div class="mt-10 hidden md:relative md:block overflow-hidden">
      <carousel
        ref="myCarousel"
        :autoplay="0"
        :wrap-around="false"
        :items-to-show="
          isDesktop
            ? 6
            : isSemiLarge
            ? 5
            : isLarge
            ? 4
            : isTab
            ? 3
            : isSemiTab
            ? 2
            : 2
        "
        :pauseAutoplayOnHover="true"
        snapAlign="start"
        @slide-end="getLastSlideIndex"
        class="relative industry-carousel"
      >
        <slide
          v-for="(company, index) in displayedCompanies"
          class="flex flex-col !justify-start transition-all duration-200 ease-in-out sm:!min-w-[214px] sm:last:!min-w-[214px]"
          :class="
            index === companies.length ? '' : 'sm:!pr-5 pl-0.5 last:!pr-0'
          "
          :key="index"
          @mouseover="company.selected = true"
          @mouseleave="company.selected = false"
        >
          <div
            class="flex flex-col justify-between items-start w-full sm:h-[260px] h-[202px]"
          >
            <p
              class="text-[#1D1A20] lg:text-xl md:text-lg text-base absolute top-5 left-5 font-bold white-space-wrap max-w-[172px] text-left"
            >
              {{ company.title }}
            </p>
            <Transition name="fadeIn" mode="out-in">
              <img
                class="size-full object-cover"
                :src="company.image"
                :alt="`company_${company.text}`"
              />
            </Transition>
          </div>
        </slide>
      </carousel>
      <!-- v-if="!isExtraLarge" -->
      <div
        v-if="
          normalizePath(route.path) === '' ||
          (normalizePath(route.path) === '/services/full-stack-development' &&
            !isDesktop) ||
          normalizePath(route.path) ===
            '/services/ai-ml-development-integration'
        "
        class="cursor-pointer flex justify-center items-center w-10 h-10 rounded-full bg-[#FFD700] absolute top-1/2 transform -translate-y-1/2 z-[9] select-none"
        :class="
          isDesktop
            ? 'right-[18%]'
            : isSemiLarge
            ? 'right-[21.5%]'
            : isLarge
            ? 'right-[2%]'
            : isTab
            ? 'right-[3%]'
            : isSemiTab
            ? 'right-[7%]'
            : 'right-[18%]'
        "
        @click="showRightArrow ? myCarousel.next() : myCarousel.prev()"
      >
        <ClientOnly>
          <fa
            class="text-3xl text-[#1D1A20] transform transition-all duration-300 ease-in-out"
            :class="showRightArrow ? 'rotate-0' : 'rotate-180'"
            :icon="['fas', 'arrow-right']"
          />
        </ClientOnly>
      </div>
    </div>
  </div>
</template>

<style scoped>
.heading-one {
  @apply text-[#FDB21D] text-3xl leading-[40px] md:text-[40px] md:leading-[55px] md:font-bold font-semibold;
}

/* for page */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.3s ease-in-out;
}
.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}

@media (max-width: 1439px) {
  .overflow {
    overflow: hidden;
  }
}
.expertise-container {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 11px;
}

.expertise-card {
  /* aspect-ratio: 1 / 1; */
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.card-title {
  color: #1d1a20;
  font-weight: bold;
  position: absolute;
  margin-left: 10px;
  margin-top: 10px;
  font-size: 16px;
  line-height: 24px;
}

.see-more-container {
  display: flex;
  justify-content: center;
}

.see-more-button {
  background-color: #ffd700;
  color: #1d1a20;
  border: none;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: normal;
  cursor: pointer;
  margin-top: 32px;
  width: 140px;
  height: 48px;
}

.see-more-button:hover {
  background-color: #ffc700;
}
</style>

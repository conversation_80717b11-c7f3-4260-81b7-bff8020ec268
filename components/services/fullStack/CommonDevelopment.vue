<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";

const props = defineProps<{
  expertiseAreas: ExpertiseArea[];
}>();

const localExpertiseAreas = ref<ExpertiseArea[]>([]);

watch(
  () => props.expertiseAreas,
  (newVal) => {
    // Copy props data to local ref with selection state
    localExpertiseAreas.value = newVal.map((area) => ({
      ...area,
      slectedItem: false,
    }));
  },
  { immediate: true, deep: true }
);

const breakpoints = useBreakpoints(breakpointsTailwind);
const isDesktop = breakpoints.greaterOrEqual("lg");

interface ExpertiseArea {
  id: string;
  image: string;
  text: string;
  description: string;
  backgroundColor: string;
  margin: number;
  slectedItem?: boolean;
  height?: number;
}

const calculateHeights = () => {
  localExpertiseAreas.value.forEach((area) => {
    const element = document.getElementById(area.id);
    if (element) {
      area.height = element.getBoundingClientRect().height;
    }
  });
};

const specificValuesSelect = (id: string) => {
  localExpertiseAreas.value.forEach((area) => {
    if (area.id === id) {
      area.slectedItem = !area.slectedItem;
    } else {
      area.slectedItem = false;
    }
  });
};

const viewPortController = () => {
  const allPointSection = document.querySelectorAll(".point-text");
  const title = document.getElementById("title");

  const titleObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null,
      rootMargin: "0px 0px 0% 0px",
      threshold: 0.5,
    }
  );

  if (title) {
    titleObserver.observe(title);
  }

  const observerTwo = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null,
      rootMargin: "0px 0px 5% 0px",
      threshold: 0,
    }
  );

  allPointSection.forEach((item) => {
    observerTwo.observe(item);
  });
};

onMounted(async () => {
  viewPortController();
  await nextTick();
  calculateHeights();
  window.addEventListener("resize", calculateHeights);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateHeights);
});
</script>

<template>
  <div>
    <!-- <h2
      id="title"
      class="text-left text-[#f1f1f2] text-[20px] leading-[30px] font-bold"
    >
      <span class="instrument-italic">Full Stack</span>
      <span class="text-[#999]"> Development</span>
      <span class="instrument-italic"> Services</span>
    </h2> -->
    <div class="flex flex-col mt-[12px]">
      <div
        v-for="(expertiseArea, index) in localExpertiseAreas"
        :key="expertiseArea.id"
        class="flex items-center lg:space-x-[86px] md:space-x-[26px] space-x-10 border-b-[0.5px] border-[#ffffff1a] single-item py-7 hover:py-9 last:border-b-0"
      >
        <p
          class="hidden lg:flex flex-col self-center items-center point-text point-text-gradient !ml-0 transition-all duration-500 ease-in-out"
        >
          {{ (index + 1).toString().padStart(2, "0") }}
        </p>

        <p
          class="block lg:hidden point-text point-text-gradient mt-[13px] !ml-0 transition-all duration-500 ease-in-out"
          :class="
            isDesktop
              ? ''
              : expertiseArea.slectedItem
              ? 'mobile-point-text-gradient final'
              : 'mobile-initial-point-text-gradient final'
          "
          @click.stop="isDesktop ? '' : specificValuesSelect(expertiseArea.id)"
        >
          {{ (index + 1).toString().padStart(2, "0") }}
        </p>
        <div
          class="h-[80px] lg:h-[112px] overflow-hidden element-des transition-all duration-500 ease-in-out grow"
          :class="
            isDesktop
              ? ''
              : expertiseArea.slectedItem
              ? 'mobile-element-des'
              : 'mobile-initial-element-des'
          "
          :style="{ '--elementHeight': `${expertiseArea.height}px` }"
        >
          <div :id="expertiseArea.id" class="flex justify-between items-center">
            <div class="flex flex-col space-y-5">
              <div
                class="flex items-center justify-between h-[100px] lg:h-[112px]"
                @click.stop="
                  isDesktop ? '' : specificValuesSelect(expertiseArea.id)
                "
              >
                <p
                  class="text-[#7D7D82] xl:text-5xl xl:leading-[56px] lg:text-3xl md:text-2xl text-xl expertiseArea-title font-bold transition-all duration-500 ease-in-out"
                  :class="
                    isDesktop
                      ? ''
                      : expertiseArea.slectedItem
                      ? 'mobile-expertiseArea-title'
                      : 'mobile-initial-expertiseArea-title'
                  "
                >
                  {{ expertiseArea.text }}
                </p>
              </div>
              <ClientOnly>
                <p
                  class="text-[#999999] text-[24px] leading-[32px] max-w-[655px]"
                  v-html="expertiseArea.description"
                />
              </ClientOnly>
            </div>
          </div>
        </div>

        <img
          class="cursor-pointer transform transition-all duration-500 ease-in-out right-sight-rotate-image hidden md:block rounded-[10px]"
          :src="expertiseArea.image"
          alt="up-arrow-with-bg"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.point-text {
  font-size: 24px;
  height: 26px;
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.point-text-gradient {
  background: linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  background: -webkit-linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.point-text.final {
  transform: translateY(0px) !important;
}
.right-sight-rotate-image {
  @apply w-[137px] h-[100px];
  transition: all 0.5s ease-in-out;
}
.single-item:hover .right-sight-rotate-image {
  @apply w-[200px] lg:w-[251px] h-[130px] lg:h-[182px];
  transform: rotate(-16.9deg);
}
.single-item:hover .point-text-gradient {
  background: linear-gradient(158deg, #999 10.67%, #999 84.22%);
  background: -webkit-linear-gradient(158deg, #999 10.67%, #999 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.single-item:hover .element-des {
  height: var(--elementHeight);
}
.single-item:hover .expertiseArea-title {
  color: white;
}
#title {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
#title.final {
  transform: translateY(0px);
}
@media (max-width: 767px) {
  .mobile-initial-element-des {
    height: 100px !important;
  }
  .mobile-element-des {
    height: var(--elementHeight) !important;
  }
  .mobile-initial-point-text-gradient {
    background: linear-gradient(
      158deg,
      #833ab4 10.67%,
      #ffd700 84.22%
    ) !important;
    background: -webkit-linear-gradient(
      158deg,
      #833ab4 10.67%,
      #ffd700 84.22% !important
    );
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-fill-color: transparent !important;
  }
  .mobile-point-text-gradient {
    background: linear-gradient(158deg, #999 10.67%, #999 84.22%) !important;
    background: -webkit-linear-gradient(
      158deg,
      #999 10.67%,
      #999 84.22%
    ) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-fill-color: transparent !important;
  }

  .mobile-expertiseArea-title {
    color: white !important;
  }
  .mobile-initial-expertiseArea-title {
    color: #7d7d82 !important;
  }
}
</style>

<script setup>
const headerTitle = ref(`<div class="overflow-hidden">
              <p class="text-[20px] text-[#999999] !title-animation max-w-[230px]">
            Why Choose <span class="text-[#F1F1F2] instrument-italic">Devxhub</span> for
            <span class="text-[#F1F1F2] instrument-italic">Full Stack</span> Development?
          </p>
            </div>
            `);
const whyChooseDevxhub = ref([
  {
    image: "/services/full-stack/why-choose/leadership.svg",
    title: "Leadership",
    description:
      "We take the helm of projects at every stage, leading with confidence to deliver innovative solutions and exceptional results.",
  },
  {
    image: "/services/full-stack/why-choose/expertise-stack.svg",
    title: "Expertise Across the Stack",
    description:
      "Our skilled professionals handle both front-end and back-end development with equal precision.",
  },
  {
    image: "/services/full-stack/why-choose/cost-effectiveness.svg",
    title: "Cost‑Effectiveness",
    description:
      "By tapping into top‑tier talent from Bangladesh and other regions, businesses can reduce development costs compared to hiring locally in the USA or EU.",
  },
  {
    image: "/services/full-stack/why-choose/customized-innovative.svg",
    title: "Customized & Innovative Solutions",
    description:
      "Every project is customized to unique business needs, ensuring top functionality and user experience with the latest tools and best practices.",
  },
  {
    image: "/services/full-stack/why-choose/transparent-communication.svg",
    title: "Transparent Communication",
    description:
      "With regular updates and clear reporting, clients stay in control at every stage.",
  },
  {
    image: "/services/full-stack/why-choose/end-to-end.svg",
    title: "End‑to‑End Support",
    description:
      "From planning through post‑deployment support, Devxhub offers a comprehensive development lifecycle.",
  },
]);

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  // what makes us difference section animation
  const headerTitleAnimation = document.querySelectorAll(".title-aimation");
  console.log(headerTitleAnimation, "headerTitleAnimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  headerTitleAnimation.forEach((item) => {
    observerThree.observe(item);
  });
};

onMounted(() => {
  setTimeout(() => {
    viewPortEnter();
  }, 1000);
});
</script>

<template>
  <div class="">
    <!-- <div class="fifth-h-tag">
      <span class="text-[#999999]">Why Choose</span> <br />
      Devxhub <br />
      <span class="text-[#999999]">for</span> Full Stack <br />
      <span class="text-[#999999]">Development?</span>
    </div> -->
    <!-- <div class="grid-item gap-70 w-full">
      <div
        v-for="(coreValue, index) in whyChooseDevxhub"
        :key="index"
        class="pb-10 border-b border-[#ffffff1a] text-up-animation last:border-b-0"
        :class="{ 'border-b-0': index === 4 }"
      >
        <div class="flex flex-col items-start">
          <img
            v-if="coreValue.image"
            class="md:size-[60px] size-[24px]"
            :src="coreValue.image"
            :alt="coreValue.title"
          />
          <h2 class="third-h-tag mt-5 mb-2.5">
            {{ coreValue.title }}
          </h2>
          <p class="third-p-tag">
            {{ coreValue.description }}
          </p>
        </div>
      </div>
    </div> -->
    <ServicesWhyChooseDevxhub
      class="relative md:mt-[180px] mt-[120px]"
      :header-title="headerTitle"
      :core-values="whyChooseDevxhub"
    />
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 65px;
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
</style>

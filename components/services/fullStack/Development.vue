<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { nextTick, onBeforeUnmount, onMounted, ref } from "vue";

const breakpoints = useBreakpoints(breakpointsTailwind);
const isDesktop = breakpoints.greaterOrEqual("lg");

// Add interface for expertise area
interface ExpertiseArea {
  id: string;
  image: string;
  text: string;
  description: string;
  backgroundColor: string;
  margin: number;
  slectedItem: boolean;
  link?: string;
  height?: number; // Add optional height property
}

// Update ref type
const expertiseAreas = ref<ExpertiseArea[]>([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/end-to-end-development.png",
    text: "End‑to‑End Application Development",
    description:
      "Crafting custom web and mobile applications tailored to your business needs.",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/frontend-development.png",
    text: "Frontend Development",
    description:
      "We deliver visually stunning and responsive user interfaces designed to provide seamless interactions and exceptional user experiences.",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/backend-development.png",
    text: "Backend Development",
    description:
      "We create secure and scalable backend solutions to ensure your applications run smoothly, efficiently, and reliably.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/mobile-app-development.png",
    text: "Mobile App Development",
    description:
      "We develop innovative, feature-rich mobile apps tailored for Android and iOS platforms to engage your audience and meet your business goals.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
    link: "/services/mobile-application-development",
  },
  {
    id: "expertiseArea5",
    image: "/services/full-stack/mvp-saas.png",
    text: "MVP, SaaS & Enterprise Solutions",
    description:
      "We deliver visually stunning and responsive user interfaces designed to provide seamless interactions and exceptional user experiences.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
    link: "/services/mvp-saas-end-to-end-development",
  },
  {
    id: "expertiseArea6",
    image: "/services/full-stack/ai-ml-devops.png",
    text: "AI / ML & DevOps Solutions",
    description:
      "We provide AI-driven solutions that empower businesses with intelligent automation, predictive analytics, and smarter decision-making capabilities.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
    link: "/services/ai-ml-development-integration",
  },
  {
    id: "expertiseArea7",
    image: "/services/full-stack/maintenance-support.png",
    text: "Maintenance & Support",
    description:
      "We deliver visually stunning and responsive user interfaces designed to provide seamless interactions and exceptional user experiences.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const calculateHeights = () => {
  // Loop through expertiseAreas to calculate heights
  expertiseAreas.value.forEach((area) => {
    const element = document.getElementById(area.id);
    if (element) {
      area.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  expertiseAreas.value.forEach((expertiseArea) => {
    if (expertiseArea.id === id) {
      expertiseArea.slectedItem = !expertiseArea.slectedItem;
    } else {
      expertiseArea.slectedItem = false;
    }
  });
};
const viewPortController = () => {
  const allPointSection = document.querySelectorAll(".point-text");
  const title = document.getElementById("title");

  // First Observer
  const titleobserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  // Add null check before observing
  if (title) {
    titleobserver.observe(title);
  }

  // Second Observer
  const observerTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        } else {
          // entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  allPointSection.forEach((item) => {
    observerTwo.observe(item);
  });
};
onMounted(async () => {
  viewPortController();
  await nextTick(); // Wait for the DOM to render
  calculateHeights();
  window.addEventListener("resize", calculateHeights);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateHeights);
});
</script>
<template>
  <div class="md:mt-[180px] mt-[100px]">
    <h2
      id="title"
      class="text-left text-[#f1f1f2] text-[20px] leading-[30px] font-bold"
    >
      <span class="instrument-italic">Full Stack</span>
      <span class="text-[#999]"> Development</span>
      <span class="instrument-italic"> Services</span>
    </h2>
    <div class="flex flex-col mt-[52px]">
      <div
        v-for="(expertiseArea, index) in expertiseAreas"
        :key="expertiseArea.id"
        class="flex items-center lg:space-x-[86px] md:space-x-[26px] space-x-10 border-b-[0.5px] border-[#ffffff1a] single-item py-7 hover:py-9 last:border-b-0"
      >
        <p
          class="hidden lg:flex flex-col self-center point-text point-text-gradient !ml-0 transition-all duration-500 ease-in-out"
        >
          0{{ index + 1 }}
        </p>
        <p
          class="block lg:hidden point-text point-text-gradient mt-[13px] !ml-0 transition-all duration-500 ease-in-out"
          :class="
            isDesktop
              ? ''
              : expertiseArea.slectedItem
              ? 'mobile-point-text-gradient final'
              : 'mobile-initial-point-text-gradient final'
          "
          @click.stop="isDesktop ? '' : specificValuesSelect(expertiseArea.id)"
        >
          0{{ index + 1 }}
        </p>
        <div
          class="h-[60px] lg:h-[70px] overflow-hidden element-des transition-all duration-500 ease-in-out grow"
          :class="
            isDesktop
              ? ''
              : expertiseArea.slectedItem
              ? 'mobile-element-des'
              : 'mobile-initial-element-des'
          "
          :style="{ '--elementHeight': `${expertiseArea.height}px` }"
        >
          <div :id="expertiseArea.id" class="flex justify-between items-center">
            <div class="flex flex-col space-y-5">
              <div
                class="flex items-center justify-between h-[73px]"
                @click.stop="
                  isDesktop ? '' : specificValuesSelect(expertiseArea.id)
                "
              >
                <p
                  class="text-[#7D7D82] xl:text-5xl xl:leading-[56px] lg:text-3xl text-2xl expertiseArea-title font-bold transition-all duration-500 ease-in-out md:whitespace-nowrap"
                  :class="
                    isDesktop
                      ? ''
                      : expertiseArea.slectedItem
                      ? 'mobile-expertiseArea-title'
                      : 'mobile-initial-expertiseArea-title'
                  "
                >
                  {{ expertiseArea.text }}
                </p>
              </div>
              <p
                class="text-[#999999] text-[24px] leading-[32px] max-w-[655px]"
              >
                {{ expertiseArea.description }}
              </p>
              <NuxtLink
                :to="expertiseArea.link"
                v-if="expertiseArea.link"
                class="btn-top w-[150px] h-[46px] bg-[#FFD700] text-[#1D1A20] flex justify-center items-center rounded-full"
              >
                See More
              </NuxtLink>
            </div>
          </div>
        </div>

        <img
          class="cursor-pointer transform transition-all duration-500 ease-in-out right-sight-rotate-image hidden md:block rounded-[10px]"
          :src="expertiseArea.image"
          alt="up-arrow-with-bg"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.point-text {
  font-size: 24px;
  height: 26px;
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
.point-text-gradient {
  background: linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  background: -webkit-linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.point-text.final {
  transform: translateY(0px) !important;
}
.right-sight-rotate-image {
  @apply w-[137px] h-[100px];
  transition: all 0.5s ease-in-out;
}
.single-item:hover .right-sight-rotate-image {
  @apply w-[200px] lg:w-[251px] h-[130px] lg:h-[182px];
  transform: rotate(-16.9deg);
}
.single-item:hover .point-text-gradient {
  background: linear-gradient(158deg, #999 10.67%, #999 84.22%);
  background: -webkit-linear-gradient(158deg, #999 10.67%, #999 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.single-item:hover .element-des {
  height: var(--elementHeight);
}
.single-item:hover .expertiseArea-title {
  color: white;
}
#title {
  transition: all 0.5s ease-in-out;
  transform: translateY(45px);
}
#title.final {
  transform: translateY(0px);
}
@media (max-width: 767px) {
  .mobile-initial-element-des {
    height: 66px !important;
  }
  .mobile-element-des {
    height: var(--elementHeight) !important;
  }
  .mobile-initial-point-text-gradient {
    background: linear-gradient(
      158deg,
      #833ab4 10.67%,
      #ffd700 84.22%
    ) !important;
    background: -webkit-linear-gradient(
      158deg,
      #833ab4 10.67%,
      #ffd700 84.22% !important
    );
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-fill-color: transparent !important;
  }
  .mobile-point-text-gradient {
    background: linear-gradient(158deg, #999 10.67%, #999 84.22%) !important;
    background: -webkit-linear-gradient(
      158deg,
      #999 10.67%,
      #999 84.22%
    ) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-fill-color: transparent !important;
  }

  .mobile-expertiseArea-title {
    color: white !important;
  }
  .mobile-initial-expertiseArea-title {
    color: #7d7d82 !important;
  }
}
.btn-top {
  margin-top: 50px;
}
@media (min-width: 1024px) and (max-width: 1439px) {
  .btn-top {
    margin-top: 20px;
  }
}
</style>

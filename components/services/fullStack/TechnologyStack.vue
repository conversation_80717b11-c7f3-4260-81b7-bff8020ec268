<script setup lang="ts">
import { ref } from "vue";

const techStackProgrammingLanguages = ref([
  {
    id: 1,
    title: "Java",
    image: "/services/full-stack/tech-stack/java.svg",
  },
  {
    id: 2,
    title: "C#",
    image: "/services/full-stack/tech-stack/c-sharp.svg",
  },
  {
    id: 3,
    title: "Python",
    image: "/services/full-stack/tech-stack/python.svg",
  },
  {
    id: 4,
    title: "Swift",
    image: "/services/full-stack/tech-stack/swift.png",
  },
  {
    id: 5,
    title: "Kotlin",
    image: "/services/full-stack/tech-stack/kotlin.png",
  },
  {
    id: 6,
    title: "",
    image: "/services/full-stack/tech-stack/go.png",
  },
  {
    id: 7,
    title: "Rust",
    image: "/services/full-stack/tech-stack/rust.svg",
  },

  {
    id: 8,
    title: "JavaScript",
    image: "/services/full-stack/tech-stack/javascript.svg",
  },
  {
    id: 9,
    title: "",
    image: "/services/full-stack/tech-stack/php.svg",
  },

  // {
  //   id: 8,
  //   title: "Ruby",
  //   image: "/services/full-stack/tech-stack/ruby.png",
  // },
  // {
  //   id: 10,
  //   title: "",
  //   image: "/services/full-stack/tech-stack/go.webp",
  // },
  // {
  //   id: 11,
  //   title: "Kotlin",
  //   image: "/services/full-stack/tech-stack/kotlin.png",
  // },
  // {
  //   id: 12,
  //   title: "Rust",
  //   image: "/services/full-stack/tech-stack/rust.webp",
  // },
  // {
  //   id: 13,
  //   title: "Scala",
  //   image: "/services/full-stack/tech-stack/scala.png",
  // },
  // {
  //   id: 15,
  //   title: "R",
  //   image: "/services/full-stack/tech-stack/r.png",
  // },
  // {
  //   id: 16,
  //   title: "Perl",
  //   image: "/services/full-stack/tech-stack/perl.png",
  // },
  // {
  //   id: 17,
  //   title: "Lua",
  //   image: "/services/full-stack/tech-stack/lua.png",
  // },
  // {
  //   id: 18,
  //   title: "Haskell",
  //   image: "/services/full-stack/tech-stack/haskell.png",
  // },
  // {
  //   id: 19,
  //   title: "MATLAB",
  //   image: "/services/full-stack/tech-stack/matlab.png",
  // },
  // {
  //   id: 20,
  //   title: "Julia",
  //   image: "/services/full-stack/tech-stack/julia.png",
  // },
  // {
  //   id: 21,
  //   title: "Elixir",
  //   image: "/services/full-stack/tech-stack/elixir.png",
  // },
  // {
  //   id: 22,
  //   title: "Groovy",
  //   image: "/services/full-stack/tech-stack/groovy.png",
  // },
  // {
  //   id: 23,
  //   title: "Clojure",
  //   image: "/services/full-stack/tech-stack/clojure.png",
  // },
  // {
  //   id: 24,
  //   title: "F#",
  //   image: "/services/full-stack/tech-stack/fsharp.svg",
  // },
]);

const techStackFrontendFrameworks = ref([
  {
    id: 1,
    title: "Angular",
    image: "/services/full-stack/tech-stack/angular.png",
  },
  {
    id: 2,
    title: "ReactJS",
    image: "/services/full-stack/tech-stack/react.svg",
  },
  {
    id: 3,
    title: "Next.js",
    image: "/services/full-stack/tech-stack/next-js.png",
  },
  {
    id: 4,
    title: "Vue.js",
    image: "/services/full-stack/tech-stack/vue-js.png",
  },
  {
    id: 5,
    title: "Nuxt.js",
    image: "/services/full-stack/tech-stack/nuxt-js.png",
  },
  {
    id: 6,
    title: "Svelte",
    image: "/services/full-stack/tech-stack/svelte.png",
  },
  {
    id: 7,
    title: "Astro.js",
    image: "/services/full-stack/tech-stack/astro.svg",
  },
  {
    id: 8,
    title: "TypeScript",
    image: "/services/full-stack/tech-stack/typescript.svg",
  },
  {
    id: 9,
    title: "Tailwind CSS",
    image: "/services/full-stack/tech-stack/tailwind.png",
  },
  {
    id: 10,
    title: "D3.js",
    image: "/services/full-stack/tech-stack/d3-js.png",
  },
  {
    id: 11,
    title: "Chart.js",
    image: "/services/full-stack/tech-stack/chart-js.png",
  },
  {
    id: 12,
    title: "Three.js",
    image: "/services/full-stack/tech-stack/three-js.png",
  },
  {
    id: 13,
    title: "Bootstrap",
    image: "/services/full-stack/tech-stack/bootstrap.png",
  },
  {
    id: 14,
    title: "Bootstrap Vue",
    image: "/services/full-stack/tech-stack/bootstrap-vue.svg",
  },

  // {
  //   id: 6,
  //   title: "Svelte",
  //   image: "/services/full-stack/tech-stack/svelte.png",
  // },
  // {
  //   id: 9,
  //   title: "Material UI",
  //   image: "/services/full-stack/tech-stack/material-ui.png",
  // },
  // {
  //   id: 10,
  //   title: "Chakra UI",
  //   image: "/services/full-stack/tech-stack/chakra.png",
  // },
  // {
  //   id: 11,
  //   title: "Redux",
  //   image: "/services/full-stack/tech-stack/redux.png",
  // },
  // {
  //   id: 12,
  //   title: "Sass/SCSS",
  //   image: "/services/full-stack/tech-stack/sass.png",
  // },
  // {
  //   id: 13,
  //   title: "Gatsby",
  //   image: "/services/full-stack/tech-stack/gatsby.png",
  // },
  // {
  //   id: 14,
  //   title: "Ember.js",
  //   image: "/services/full-stack/tech-stack/ember-js.svg",
  // },
  // {
  //   id: 15,
  //   title: "Alpine.js",
  //   image: "/services/full-stack/tech-stack/alpine-js.png",
  // },
  // {
  //   id: 16,
  //   title: "Preact",
  //   image: "/services/full-stack/tech-stack/preact.png",
  // },
  // {
  //   id: 17,
  //   title: "Solid.js",
  //   image: "/services/full-stack/tech-stack/solid-js.png",
  // },
  // {
  //   id: 18,
  //   title: "Astro",
  //   image: "/services/full-stack/tech-stack/astro.png",
  // },
  // {
  //   id: 20,
  //   title: "Qwik",
  //   image: "/services/full-stack/tech-stack/qwik.png",
  // },
  // {
  //   id: 22,
  //   title: "Three.js",
  //   image: "/services/full-stack/tech-stack/three-js.png",
  // },
  // {
  //   id: 23,
  //   title: "D3.js",
  //   image: "/services/full-stack/tech-stack/d3-js.png",
  // },
  // {
  //   id: 24,
  //   title: "WebGL",
  //   image: "/services/full-stack/tech-stack/webgl.png",
  // },
  // {
  //   id: 25,
  //   title: "jQuery",
  //   image: "/services/full-stack/tech-stack/jquery.png",
  // },
]);

const techStackBackendFrameworks = ref([
  {
    id: 1,
    title: "Spring Boot",
    image: "/services/full-stack/tech-stack/spring-boot.png",
  },
  {
    id: 2,
    title: ".NET Core",
    image: "/services/full-stack/tech-stack/asp-net-core.png",
  },
  {
    id: 3,
    title: "Django",
    image: "/services/full-stack/tech-stack/django.svg",
  },
  {
    id: 4,
    title: "",
    image: "/services/full-stack/tech-stack/flask.png",
  },
  {
    id: 5,
    title: "NodeJS",
    image: "/services/full-stack/tech-stack/node-js.svg",
  },
  {
    id: 6,
    title: "Laravel",
    image: "/services/full-stack/tech-stack/laravel.svg",
  },

  // {
  //   id: 4,
  //   title: "Express.js",
  //   image: "/services/full-stack/tech-stack/express.png",
  // },
  // {
  //   id: 6,
  //   title: "Flask",
  //   image: "/services/full-stack/tech-stack/flask.png",
  // },
  // {
  //   id: 7,
  //   title: "FastAPI",
  //   image: "/services/full-stack/tech-stack/fast-api.png",
  // },
  // {
  //   id: 8,
  //   title: "Ruby on Rails",
  //   image: "/services/full-stack/tech-stack/rails.png",
  // },
  // {
  //   id: 10,
  //   title: "NestJS",
  //   image: "/services/full-stack/tech-stack/nest-js.png",
  // },
  // {
  //   id: 11,
  //   title: "Symfony",
  //   image: "/services/full-stack/tech-stack/symfony.png",
  // },
  // {
  //   id: 12,
  //   title: "CodeIgniter",
  //   image: "/services/full-stack/tech-stack/codeigniter.webp",
  // },
  // {
  //   id: 13,
  //   title: "Phoenix",
  //   image: "/services/full-stack/tech-stack/phoenix.png",
  // },
  // {
  //   id: 15,
  //   title: "GraphQL",
  //   image: "/services/full-stack/tech-stack/graphql.png",
  // },
]);

const techStackFullStackFrameworks = ref([
  {
    id: 1,
    title: "MERN Stack",
    image: "/services/full-stack/tech-stack/mern-stack.png",
  },
  {
    id: 2,
    title: "MEAN Stack",
    image: "/services/full-stack/tech-stack/mean-stack.png",
  },
]);

const techStackMobileTechnologies = ref([
  {
    id: 1,
    title: "Flutter",
    image: "/services/full-stack/tech-stack/flutter.png",
  },
  {
    id: 2,
    title: "React Native",
    image: "/services/full-stack/tech-stack/react-native.webp",
  },

  {
    id: 3,
    title: "PWAs",
    image: "/services/full-stack/tech-stack/pwa.png",
  },
  {
    id: 4,
    title: "Java",
    image: "/services/full-stack/tech-stack/java.svg",
  },
  {
    id: 5,
    title: "Dart",
    image: "/services/full-stack/tech-stack/dart.svg",
  },
  {
    id: 6,
    title: "FlutterFlow",
    image: "/services/full-stack/tech-stack/flutter-flow.png",
  },
  {
    id: 7,
    title: "Swift UI",
    image: "/services/full-stack/tech-stack/swift.png",
  },
  {
    id: 8,
    title: "Jetpack Compose UI",
    image: "/services/full-stack/tech-stack/jetpack.svg",
  },
]);

const techStackDesktopTechnologies = ref([
  {
    id: 1,
    title: "Electron.js",
    image: "/services/full-stack/tech-stack/electron-js.png",
  },
  {
    id: 2,
    title: "",
    image: "/services/full-stack/tech-stack/java-fx.png",
  },
]);

const techMicroServices = ref([
  {
    id: 1,
    title: "REST",
    image: "/services/full-stack/tech-stack/rest.png",
  },
  {
    id: 2,
    title: "GraphQL",
    image: "/services/full-stack/tech-stack/graphQL.png",
  },
  {
    id: 3,
    title: "",
    image: "/services/full-stack/tech-stack/grpc.png",
  },
  {
    id: 4,
    title: "SOAP",
    image: "/services/full-stack/tech-stack/soap.png",
  },
  {
    id: 5,
    title: "WebSockets",
    image: "/services/full-stack/tech-stack/websockets.png",
  },
  {
    id: 6,
    title: "",
    image: "/services/full-stack/tech-stack/kafka.png",
  },
  {
    id: 7,
    title: "RabbitMQ",
    image: "/services/full-stack/tech-stack/rabbitmq.png",
  },
]);

const techStackDatabaseSolutions = ref([
  {
    id: 1,
    title: "SQL",
    image: "/services/full-stack/tech-stack/sql.png",
  },
  {
    id: 2,
    title: "MySQL",
    image: "/services/full-stack/tech-stack/mysql.webp",
  },
  {
    id: 3,
    title: "Ms SQL",
    image: "/services/full-stack/tech-stack/ms-sql.png",
  },
  {
    id: 4,
    title: "PostgreSQL",
    image: "/services/full-stack/tech-stack/postgresql.webp",
  },
  {
    id: 5,
    title: "NO SQL",
    image: "/services/full-stack/tech-stack/nosql.png",
  },
  {
    id: 6,
    title: "",
    image: "/services/full-stack/tech-stack/mongoDB.png",
  },
  {
    id: 7,
    title: "AWS Dynamo DB",
    image: "/services/full-stack/tech-stack/dynamoDB.png",
  },
  {
    id: 8,
    title: "Oracle",
    image: "/services/full-stack/tech-stack/oracle.svg",
  },
]);

const techStackCloudDevOps = ref([
  {
    id: 1,
    title: "AWS",
    image: "/services/full-stack/tech-stack/aws.png",
  },
  {
    id: 2,
    title: "Azure",
    image: "/services/full-stack/tech-stack/aurora.png",
  },
  {
    id: 3,
    title: "Google Cloud",
    image: "/services/full-stack/tech-stack/google-cloud.svg",
  },
  {
    id: 4,
    title: "Digital Ocean",
    image: "/services/full-stack/tech-stack/ocean.png",
  },
  {
    id: 5,
    title: "",
    image: "/services/full-stack/tech-stack/docker.png",
  },
  {
    id: 6,
    title: "Kubernetes",
    image: "/services/full-stack/tech-stack/kubernates.png",
  },
  {
    id: 7,
    title: "Terraform",
    image: "/services/full-stack/tech-stack/teraform.svg",
  },
  {
    id: 8,
    title: "Github Action",
    image: "/services/full-stack/tech-stack/github-actions.png",
  },
  {
    id: 9,
    title: "Jenkins",
    image: "/services/full-stack/tech-stack/jenkins.svg",
  },
  {
    id: 10,
    title: "",
    image: "/services/full-stack/tech-stack/ansible.png",
  },
  {
    id: 11,
    title: "CI/CD pipelines",
    image: "/services/full-stack/tech-stack/ci-cd-pipelines.png",
  },
  {
    id: 12,
    title: "Automation tools",
    image: "/services/full-stack/tech-stack/playwright.webp",
  },
  {
    id: 13,
    title: "Linux",
    image: "/services/full-stack/tech-stack/linux.png",
  },
  {
    id: 14,
    title: "Windows Server",
    image: "/services/full-stack/tech-stack/windows.svg",
  },
]);

const techStackAiMl = ref([
  {
    id: 1,
    title: "",
    image: "/services/full-stack/tech-stack/mlops.png",
  },
  {
    id: 2,
    title: "LLMops",
    image: "/services/full-stack/tech-stack/llmops.png",
  },
  {
    id: 3,
    title: "LLMs",
    image: "/services/full-stack/tech-stack/llms.png",
  },
  {
    id: 4,
    title: "OpenAI API",
    image: "/services/full-stack/tech-stack/openai.png",
  },
  {
    id: 5,
    title: "Cursor",
    image: "/services/full-stack/tech-stack/cursor.png",
  },
  {
    id: 6,
    title: "Github-copilot",
    image: "/services/full-stack/tech-stack/github-copilot.png",
  },
  {
    id: 7,
    title: "Claude",
    image: "/services/full-stack/tech-stack/claude.png",
  },
  {
    id: 8,
    title: "Windserf",
    image: "/services/full-stack/tech-stack/windserf.png",
  },
]);
</script>
<template>
  <div class="md:mt-[180px] mt-[100px]">
    <h2 class="sixth-h-tag">Technology stack</h2>

    <div class="flex gap-2.5 md:gap-10 lg:gap-[55px] mt-20">
      <div class="h-auto bg-[#999999] w-[5px]"></div>

      <div class="space-y-20">
        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">
            Artificial Intelligence and Machine Learning
          </h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="aiMl in techStackAiMl"
              :key="aiMl.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold"
            >
              <img
                :class="aiMl.title === '' ? 'max-h-[86px]' : 'max-h-[58px]'"
                :src="aiMl.image"
                :alt="aiMl.title"
              />
              <p class="whitespace-nowrap">
                {{ aiMl.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Programming Languages</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackProgrammingLanguages"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Backend Frameworks</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackBackendFrameworks"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-center items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Frontend Frameworks</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackFrontendFrameworks"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Full Stack Frameworks</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackFullStackFrameworks"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p>{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">
            Mobile Technologies (iOS, Android & Cross-Platform)
          </h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackMobileTechnologies"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[125px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center pb-3">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Desktop Solutions</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackDesktopTechnologies"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Microservices & APIs</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techMicroServices"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-center items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Database Solutions</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="databaseSolution in techStackDatabaseSolutions"
              :key="databaseSolution.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  databaseSolution.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="databaseSolution.image"
                :alt="databaseSolution.title"
              />
              <p
                class="whitespace-nowrap"
                :class="
                  databaseSolution.title === 'AWS Dynamo DB'
                    ? 'text-lg'
                    : 'text-xl'
                "
              >
                {{ databaseSolution.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Cloud & DevOps</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="cloudDevOps in techStackCloudDevOps"
              :key="cloudDevOps.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold"
            >
              <img
                :class="
                  cloudDevOps.title === '' ? 'max-h-[86px]' : 'max-h-[58px]'
                "
                :src="cloudDevOps.image"
                :alt="cloudDevOps.title"
              />
              <p
                class="whitespace-nowrap"
                :class="
                  cloudDevOps.title === 'Automation tools'
                    ? 'text-lg'
                    : 'text-xl'
                "
              >
                {{ cloudDevOps.title }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>

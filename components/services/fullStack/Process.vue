<script setup>
const viewPortController1 = () => {
  const animatedTextOne = document.getElementById("animatedTextOne");
  const animatedTextTwo = document.getElementById("animatedTextTwo");

  // First Observer
  const observer = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        } else {
          entry.target.classList.remove("final");
        }
      });
    }
    // {
    //   root: null, // The viewport is the root
    //   rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
    //   threshold: 0.5, // Trigger when 50% of the element is visible
    // }
  );

  // Second Observer
  const observerTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        } else {
          entry.target.classList.remove("final");
        }
      });
    }
    // {
    //   root: null, // The viewport is the root
    //   rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
    //   threshold: 0.5, // Trigger when 50% of the element is visible
    // }
  );

  observer.observe(animatedTextOne);
  // Start observing with the first observer
  observerTwo.observe(animatedTextTwo);
};

onMounted(() => {
  viewPortController1();
});
</script>

<template>
  <div class="md:mt-[180px] mt-[100px]">
    <div
      class="flex min-[992px]:flex-row flex-col min-[992px]:space-y-0 space-y-10 justify-between min-[992px]:mb-[80px] mb-[60px]"
    >
      <div class="whitespace-nowrap overflow-hidden">
        <p
          id="animatedTextOne"
          class="animated_text_one sixth-h-tag font-bold !text-[#999]"
        >
          Process of
        </p>
        <p
          id="animatedTextTwo"
          class="instrument-italic sixth-h-tag text-[#f1f1f2]"
        >
          Full Stack Development
        </p>
      </div>
    </div>
    <div
      class="four-grid-item min-[992px]:ml-[22px] min-[800px]:ml-[15px] md:ml-[-8px]"
    >
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[10px] mb-[20px] min-[992px]:w-[80px] min-[992px]:h-[80px] w-[70px] h-[70.77px]"
          src="/public/services/full-stack/process/analyse.svg"
          alt="Analysis & Planning"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-bold">
          Analysis & Planning
        </p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[10px] mb-[20px] min-[992px]:w-[80px] min-[992px]:h-[80px] w-[70px] h-[70.77px]"
          src="/public/services/full-stack/process/design-development.svg"
          alt="Design & Development"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-bold">
          Design & Development
        </p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[10px] mb-[20px] min-[992px]:w-[80px] min-[992px]:h-[80px] w-[70px] h-[70.77px]"
          src="/public/services/full-stack/process/testing-development.svg"
          alt="Testing & Deployment"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-bold">
          Testing & Deployment
        </p>
      </div>
      <div
        class="work-single-item flex flex-col items-center min-[992px]:min-w-[auto] md:min-w-[200px] min-w-[145.5px]"
      >
        <img
          class="min-[992px]:mb-[10px] mb-[20px] min-[992px]:w-[80px] min-[992px]:h-[80px] w-[70px] h-[70.77px]"
          src="/public/services/full-stack/process/support-maintenance.svg"
          alt="Support & Maintenance"
        />
        <p class="xl:text-2xl md:text-xl text-lg text-[#F1F1F2] font-bold">
          Support & Maintenance
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.four-grid-item {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.work-single-item {
  text-align: center;
  border: 1px solid #d6dce1;
  border-radius: 128px;
  margin-left: -22px;
  padding: 30px 40px 20px;
}
.work-single-item:first-child {
  /* margin-left: 0px; */
}
@media screen and (min-width: 1440px) {
  .work-single-item {
    padding: 36px 32px;
  }
}

@media screen and (max-width: 991px) {
  .work-single-item {
    margin-left: -15px;
    padding: 15px 30px;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
    grid-template-columns: 1fr 1fr;
    margin-right: 0;
  }
  .work-single-item {
    margin-left: 0;
  }
}

@media screen and (max-width: 479px) {
  .work-single-item {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
  }
}

#animatedTextOne {
  transition: all 0.5s ease-in-out;
  transform: translateX(-10.417vw);
}
#animatedTextTwo {
  transition: all 0.5s ease-in-out;
  transform: translateX(10.417vw);
}

#animatedTextOne.final {
  transform: translateX(0vw);
}
#animatedTextTwo.final {
  transform: translateX(0vw);
}
</style>

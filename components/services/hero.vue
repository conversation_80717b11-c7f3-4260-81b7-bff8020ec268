<script setup></script>

<template>
  <section
    class="container-fluid pt-[150px] lg:pt-[196px] flex flex-row justify-center items-start"
  >
    <div class="lg:max-w-[96%] w-full">
      <h1 class="primary-h-tag text-up-animation text-center">
        <span class="text-[#999]">Expert </span>
        <span class="instrument-italic">Software Development </span>
        <span class="text-[#999]">and </span>
        <span class="instrument-italic">IT Staff Augmentation </span>
        <span class="text-[#999]">Service </span>
      </h1>
      <p class="primary-p-tag pt-5 text-up-animation text-center">
        At Devxhub, we specialize in custom software development, IT staff
        augmentation, SaaS & MVP development, and AI-powered solutions. Our
        expert team helps businesses build scalable, secure, and
        high-performance digital products, from enterprise software to
        cloud-based applications. Whether you're a startup, SME, or enterprise,
        we provide tailored tech solutions to drive your growth and innovation.
      </p>
      <p class="primary-p-tag pt-6 text-up-animation text-center">
        Build Faster, Scale Smarter, and Innovate Seamlessly.
      </p>
      <NuxtLink
        to="/contact-us"
        class="text-up-animation w-[260px] h-[46px] px-[31px] py-[9px] text-lg flex justify-center items-center text-[#1D1A20] font-medium mt-[64px] bg-[#FFD700] rounded-full justify-self-center"
        >Start Your Project Today</NuxtLink
      >
    </div>
    <div></div>
  </section>
</template>

<style lang="scss" scoped>
.title-animation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-animation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
.text-up-animation,
.img-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.img-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

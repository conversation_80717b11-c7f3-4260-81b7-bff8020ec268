<script setup lang="ts">
import { useRuntimeConfig } from "nuxt/app";

const config = useRuntimeConfig();

interface Service {
  id: number;
  title: string;
  subTitle: string;
  description: string;
  image: string;
  pageUrl?: string;
  livePageUrl?: string;
  serviceItems: {
    id: number;
    name: string;
    url: string;
  }[];
  serviceNotes?: {
    id: number;
    note: string;
  }[];
}
const services: Service[] = [
  {
    id: 1,
    title: "IT Staff Augmentation",
    subTitle: "Hire Top-Tier Developers to Scale Your Team Efficiently",
    description: `Looking for skilled developers to accelerate your projects? Devxhub’s <span class="text-[#f1f1f2]">IT staff augmentation</span> services provide access to top software engineers, ensuring seamless integration with your existing team. Reduce hiring time, cut operational costs, and achieve faster development cycles.`,
    image: "/services/service/service-1.png",
    pageUrl: "/services/it-staff-augmentation",
    serviceItems: [
      {
        id: 1,
        name: "Frontend Developers",
        url: "",
      },
      {
        id: 2,
        name: "Backend Developers",
        url: "",
      },
      {
        id: 3,
        name: "Mobile App Developers",
        url: "",
      },
      {
        id: 4,
        name: "MERN Stack Developers",
        url: "",
      },
      {
        id: 41,
        name: "Full Stack Developers",
        url: "",
      },
      {
        id: 5,
        name: "Java Developers",
        url: "",
      },
      {
        id: 6,
        name: ".NET Core Developers",
        url: "",
      },
      {
        id: 7,
        name: "Software Architects",
        url: "",
      },
      {
        id: 8,
        name: "AI/ML Engineers",
        url: "",
      },
      {
        id: 9,
        name: "DevOps & Cloud Engineers",
        url: "",
      },
    ],
    // serviceNotes: [
    //   {
    //     id: 1,
    //     note: "Boost productivity with our remote development team",
    //   },
    //   {
    //     id: 2,
    //     note: "Flexible hiring models: Part-time, full-time, project-based",
    //   },
    // ],
  },
  {
    id: 2,
    title: "Full-Stack Development",
    subTitle: "Custom Web & Mobile App Development for Businesses",
    description: `At Devxhub, we offer <span class="text-[#F1F1F2]">end-to-end full-stack development</span>, delivering high-performance, scalable, and secure applications tailored to your business needs. From startups to enterprises, our team builds robust solutions that enhance user experience and business growth.`,
    image: "/services/service/service-2.png",
    pageUrl: "/services/full-stack-development",
    serviceItems: [
      {
        id: 1,
        name: "Web Application Development",
        url: "",
      },
      {
        id: 2,
        name: " Frontend Development",
        url: "",
      },
      {
        id: 3,
        name: " Backend Development",
        url: "",
      },
      {
        id: 4,
        name: "Mobile App Development",
        url: "",
      },
      {
        id: 5,
        name: "User Interface (UI) & User Experience (UX) Design",
        url: "",
      },
      {
        id: 6,
        name: "API Development & Third-Party Integrations",
        url: "",
      },
      {
        id: 7,
        name: " DevOps Solutions",
        url: "",
      },
    ],
    // serviceNotes: [
    //   {
    //     id: 1,
    //     note: "Modern UI/UX for better engagement",
    //   },
    //   {
    //     id: 2,
    //     note: "SEO-friendly and all device responsive web applications",
    //   },
    // ],
  },
  {
    id: 3,
    title: "Custom & Enterprise Software Development",
    subTitle: "Tailored Software Solutions for Business Growth",
    description: `We specialize in developing <span class="text-[#F1F1F2]">custom software</span> and <span class="text-[#F1F1F2]">enterprise solutions</span> to streamline operations, enhance efficiency, and drive digital transformation. Whether you need <span class="text-[#F1F1F2]">ERP, CRM, or SaaS platforms</span>, we build software that aligns perfectly with your business goals.`,
    image: "/services/service/service-3.png",
    pageUrl: "/services/custom-software-development",
    serviceItems: [
      {
        id: 1,
        name: "Enterprise Resource Planning (ERP) Software",
        url: "",
      },
      {
        id: 2,
        name: "Customer Relationship Management (CRM) Systems",
        url: "",
      },
      {
        id: 3,
        name: "Healthcare (HMS), Human Resource (HRM) Software",
        url: "",
      },
      {
        id: 4,
        name: "E-commerce & Marketplace Development",
        url: "",
      },
      {
        id: 5,
        name: "Business Automation & Workflow Optimization",
        url: "",
      },
    ],
    // serviceNotes: [
    //   {
    //     id: 1,
    //     note: "Scalable, secure, and cloud-based solution",
    //   },
    //   {
    //     id: 2,
    //     note: "AI-powered automation for smarter business processes",
    //   },
    // ],
  },
  {
    id: 4,
    title: "MVP, SaaS & End-to-End Product Development",
    subTitle: "From Idea to Market – Build Your Startup Faster",
    description: `Want to launch a <span class="text-[#F1F1F2]">Minimum Viable Product (MVP)</span> quickly? Devxhub’s <span class="text-[#F1F1F2]">SaaS development</span> and <span class="text-[#F1F1F2]">end-to-end product engineering</span> ensure your idea becomes a fully functional, scalable product ready for market success.`,
    image: "/services/service/service-4.png",
    pageUrl: "/services/mvp-saas-end-to-end-development",
    serviceItems: [
      {
        id: 1,
        name: "MVP Development for Startups",
        url: "",
      },
      {
        id: 2,
        name: "SaaS (Software as a Service) Development",
        url: "",
      },
      {
        id: 3,
        name: "Cloud-Native Applications",
        url: "",
      },
      {
        id: 4,
        name: "End-to-End Product Development",
        url: "",
      },
      {
        id: 5,
        name: "Product Scaling & Maintenance",
        url: "",
      },
    ],
    // serviceNotes: [
    //   {
    //     id: 1,
    //     note: "Fast-track your startup with a lean, scalable MVP",
    //   },
    //   {
    //     id: 2,
    //     note: "Subscription-based software models with cloud integration",
    //   },
    // ],
  },
  {
    id: 5,
    title: "AI/ML Development & Integration",
    subTitle: "Transform Your Business with AI-Powered Solutions",
    description: `Leverage <span class="text-[#F1F1F2]">Artificial Intelligence (AI) and Machine Learning (ML)</span> to gain insights, automate processes, and enhance customer experiences. Our AI solutions integrate seamlessly with your existing systems.`,
    image: "/services/service/service-5.png",
    pageUrl: "/services/ai-ml-development-integration",
    serviceItems: [
      {
        id: 1,
        name: "OpenAI API, ChatGPT Integration & implementation",
        url: "",
      },
      {
        id: 2,
        name: "Predictive Analytics & Business Intelligence Development",
        url: "",
      },
      {
        id: 3,
        name: " Artificial Intelligence and AI Chatbot Development",
        url: "",
      },
      {
        id: 4,
        name: "Computer Vision & Natural Language Processing (NLP)",
        url: "",
      },
      {
        id: 5,
        name: "Data Science & AI-Based Automation",
        url: "",
      },
    ],
    // serviceNotes: [
    //   {
    //     id: 1,
    //     note: "Increase efficiency with AI-powered automation",
    //   },
    //   {
    //     id: 2,
    //     note: "Gain actionable insights with data-driven AI solutions",
    //   },
    // ],
  },
  {
    id: 6,
    title: "DevOps & Cloud Solutions",
    subTitle: "Optimize Deployment with Scalable Cloud Solutions",
    description: `Maximize agility and efficiency with our <span class="text-[#F1F1F2]">DevOps and cloud computing services</span>. We help businesses implement <span class="text-[#F1F1F2]">CI/CD pipelines, containerization, and cloud migration</span> for seamless development operations.`,
    image: "/services/service/service-6.png",
    pageUrl: "/services/devops-cloud-solutions",
    serviceItems: [
      {
        id: 1,
        name: "Cloud Infrastructure Setup",
        url: "",
      },
      {
        id: 2,
        name: "Cloud Application Development",
        url: "",
      },
      {
        id: 3,
        name: "Kubernetes & Docker Containerization",
        url: "",
      },
      {
        id: 4,
        name: "Continuous Integration & Deployment (CI/CD)",
        url: "",
      },
      {
        id: 5,
        name: "Serverless Application Development",
        url: "",
      },
    ],
    // serviceNotes: [
    //   {
    //     id: 1,
    //     note: "Faster releases with automated deployment pipelines",
    //   },
    //   {
    //     id: 2,
    //     note: "Scalable cloud-based solutions for modern enterprises",
    //   },
    // ],
  },
];
</script>

<template>
  <section
    v-for="(service, index) in services"
    :key="service.id"
    class="container-fluid"
  >
    <div
      class="lg:py-[180px] md:py-[84px] py-[64px] grid auto-cols-auto gap-[60px] grid-cols-1 lg:grid-cols-2 border-b-[0.25px] border-[#ffffff1a]"
    >
      <img
        :src="service.image"
        :alt="service.title"
        class="w-full h-auto text-up-animation max-h-[676px]"
        :class="{ 'lg:order-last': index % 2 !== 0 }"
      />
      <div class="">
        <h2 class="sixth-h-tag text-up-animation">{{ service.title }}</h2>
        <h3
          class="third-h-tag !text-[#999] !font-normal mt-5 text-up-animation"
        >
          {{ service.subTitle }}
        </h3>
        <p
          class="secondary-p-tag mt-5 text-up-animation"
          v-html="service.description"
        ></p>
        <ul class="mt-10 flex-col flex primary-p-tag">
          <li
            v-for="(item, index) in service.serviceItems"
            :key="item.id"
            class="text-up-animation flex items-center justify-between first:pt-0 last:pb-0 pt-5 pb-6 border-b-[0.25px] border-[#ffffff1a] last:border-none"
          >
            <div class="flex items-center space-x-5">
              <span class="list-no transform translate-y-[3px]">{{
                (index + 1).toString().padStart(2, "0")
              }}</span>
              <p class="font-medium">{{ item.name }}</p>
            </div>
            <ClientOnly>
              <fa
                class="w-[24px] h-[18px]"
                :icon="['fas', 'arrow-right-long']"
              />
            </ClientOnly>
          </li>
        </ul>

        <NuxtLink v-if="service.pageUrl" :to="service.pageUrl">
          <button
            class="w-[150px] h-[46px] mt-[60px] bg-[#FFD700] text-[#1D1A20] rounded-full flex justify-center items-center font-medium"
          >
            See more
          </button>
          <span class="sr-only">{{ service.title }}</span>
        </NuxtLink>

        <div class="space-y-2 mt-5">
          <p
            v-for="note in service.serviceNotes"
            :key="note.id"
            class="primary-p-tag font-semibold !text-white text-up-animation flex space-x-4 items-center"
          >
            <ClientOnly>
              <fa class="text-base" :icon="['fas', 'check']" /> </ClientOnly
            ><span class="text-xl">{{ note.note }}</span>
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
.list-no {
  font-family: "Neue Machina", sans-serif;
}
.title-animation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-animation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
.text-up-animation,
.img-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.img-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

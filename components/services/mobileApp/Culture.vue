<script setup>
import { onMounted, ref } from "vue";

const coreValues = ref([
  {
    title: "Always Within Reach",
    description:
      "Your app lives on users' phones, keeping your brand close and accessible anytime, anywhere.",
  },
  {
    title: "Instant User Engagement",
    description:
      "Push notifications and in-app messaging help you reach users in real time and boost interaction.",
  },
  {
    title: "Offline Access",
    description: `Even without the internet, users can access essential features, improving reliability and trust.`,
  },
  {
    title: "Increase Brand Loyalty",
    description: `A smooth, intuitive app experience helps users stick around and strengthens brand perception.`,
  },
  {
    title: "Faster Performance",
    description: `Mobile apps load and respond faster than mobile websites, providing a better experience.`,
  },
  {
    title: "Improved Customer Support",
    description: `Integrate chatbots, support tickets, and FAQs to provide instant, in-app assistance.`,
  },
]);

const viewPortEnter = () => {
  const textUpAnimation = document.querySelectorAll(".text-animation");
  const textGradient = document.querySelectorAll(".core-values");

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  const textGradientObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textGradient.forEach((item) => {
    textGradientObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div
    class="case-grid-item md:mt-[180px] mt-[80px] md:space-x-10 md:space-y-0 space-y-[40px]"
  >
    <div
      class="flex flex-col md:space-y-4 space-y-2 max-w-[413px] md:w-[30%] w-full"
    >
      <p
        class="text-animation text-[#999] lg:text-[20px] lg:leading-[30px] md:text-lg text-xl"
      >
        Exclusive Advantages of
        <br />
        <span class="instrument-italic">Mobile App Development</span>
      </p>
    </div>
    <div
      class="grid-item gap-y-6 gap-x-0 md:gap-y-10 md:gap-x-[67px] md:w-[70%] w-full"
    >
      <div
        v-for="(coreValue, index) in coreValues"
        :key="index"
        class="text-animation flex flex-col space-y-2 md:space-y-2.5 pb-6 md:pb-10 border-b border-[#ffffff1a] last:border-none min-[992px]:[&:nth-last-child(-n+2)]:border-none last:pb-0 min-[992px]:[&:nth-last-child(-n+2)]:pb-0"
      >
        <h2 class="text-xl md:text-2xl font-bold text-[#f1f1f2]">
          {{ coreValue.title }}
        </h2>
        <p class="text-[#999] text-sm md:text-base">
          {{ coreValue.description }}
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.case-grid-item {
  // grid-column-gap: 16px;
  // grid-row-gap: 16px;
  // grid-template-rows: auto;
  // grid-template-columns: 1fr 1.8fr;
  // grid-auto-columns: 1fr;
  // display: grid;
  display: flex;
}
.text-gradient {
  background: linear-gradient(158deg, #833ab4 10.67%, #ffd700 84.22%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
// .grid-item.gap-70 {
//   grid-column-gap: 70px;
//   grid-row-gap: 65px;
// }
.core-values {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.core-values.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
.text-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    flex-direction: column;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
  // .grid-item.gap-70 {
  //   grid-column-gap: 0px;
  //   grid-row-gap: 40px;
  // }
}
</style>

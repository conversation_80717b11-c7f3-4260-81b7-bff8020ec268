<script setup lang="ts">
import { ref } from "vue";

const fullStackTechnologies = ref([
  {
    id: 1,
    title: "Dar<PERSON>",
    image: "/services/mobile-app/tech-stack/dart.svg",
  },
  {
    id: 2,
    title: "FlutterFlow",
    image: "/services/full-stack/tech-stack/flutter-flow.png",
  },
  {
    id: 2,
    title: "Firebase",
    image: "/services/mobile-app/tech-stack/firebase.svg",
  },
  {
    id: 3,
    title: "FireStore",
    image: "/services/mobile-app/tech-stack/firestore.svg",
  },
  {
    id: 4,
    title: "Cloud Functions",
    image: "/services/mobile-app/tech-stack/cloud-functions.svg",
  },
  // {
  //   id: 5,
  //   title: "Objective-C",
  //   image: "/services/mobile-app/tech-stack/Objective.svg",
  // },
  // {
  //   id: 6,
  //   title: "JavaScript",
  //   image: "/services/mobile-app/tech-stack/Javascript.svg",
  // },
  // {
  //   id: 7,
  //   title: "TypeScript",
  //   image: "/services/mobile-app/tech-stack/TypeScript.png",
  // },

  // {
  //   id: 2,
  //   title: "FlutterFlow",
  //   image: "/services/mobile-app/tech-stack/flutter-flow.svg",
  // },
  // {
  //   id: 3,
  //   title: "Firebase",
  //   image: "/services/mobile-app/tech-stack/firebase.svg",
  // },
  // {
  //   id: 4,
  //   title: "Firestore",
  //   image: "/services/mobile-app/tech-stack/firestore.svg",
  // },
  // {
  //   id: 5,
  //   title: "Cloud Functions",
  //   image: "/services/mobile-app/tech-stack/cloud-functions.svg",
  // },
]);

const techStackMobileTechnologies = ref([
  {
    id: 1,
    title: "Flutter",
    image: "/services/full-stack/tech-stack/flutter.png",
  },
  {
    id: 2,
    title: "FlutterFlow",
    image: "/services/full-stack/tech-stack/flutter-flow.png",
  },
  {
    id: 3,
    title: "React Native",
    image: "/services/full-stack/tech-stack/react-native.webp",
  },
]);

const cloudDevopsTechnologies = ref([
  {
    id: 1,
    title: "AWS",
    image: "/services/full-stack/tech-stack/aws.png",
  },
  {
    id: 2,
    title: "Google Cloud",
    image: "/services/mobile-app/tech-stack/google-cloud.svg",
  },
  {
    id: 5,
    title: "",
    image: "/services/full-stack/tech-stack/docker.png",
  },
  {
    id: 3,
    title: "CI/CD",
    image: "/services/mobile-app/tech-stack/ci-cd.png",
  },
  // {
  //   id: 4,
  //   title: "AI-powered",
  //   image: "/services/mobile-app/tech-stack/ai-powered.png",
  // },
]);

const techStackBackendFrameworks = ref([
  {
    id: 1,
    title: "NodeJS",
    image: "/services/full-stack/tech-stack/node-js.svg",
  },
  {
    id: 2,
    title: "Laravel",
    image: "/services/full-stack/tech-stack/laravel.svg",
  },
  {
    id: 3,
    title: "Django",
    image: "/services/full-stack/tech-stack/django.svg",
  },
]);

const techStackDatabaseSolutions = ref([
  {
    id: 1,
    title: "Firebase",
    image: "/services/mobile-app/tech-stack/firebase.svg",
  },
  {
    id: 2,
    title: "FireStore",
    image: "/services/mobile-app/tech-stack/firestore.svg",
  },
  {
    id: 3,
    title: "Cloud Functions",
    image: "/services/mobile-app/tech-stack/cloud-functions.svg",
  },
  {
    id: 4,
    title: "MySQL",
    image: "/services/mobile-app/tech-stack/my-sql.png",
  },
  {
    id: 5,
    title: "PostgreSQL",
    image: "/services/full-stack/tech-stack/postgresql.webp",
  },
  {
    id: 6,
    title: "",
    image: "/services/full-stack/tech-stack/mongoDB.png",
  },
]);

const techStackCloudDevOps = ref([
  {
    id: 1,
    title: "Xcode",
    image: "/services/mobile-app/tech-stack/Xcode.png",
  },
  {
    id: 2,
    title: "Android Studio",
    image: "/services/mobile-app/tech-stack/Android Studio.svg",
  },
  {
    id: 3,
    title: "VS Code",
    image: "/services/mobile-app/tech-stack/Visual_Studio_Code.svg",
  },

  // {
  //   id: 1,
  //   title: "AWS",
  //   image: "/services/full-stack/tech-stack/aws.png",
  // },
  // {
  //   id: 2,
  //   title: "Google Cloud",
  //   image: "/services/mobile-app/tech-stack/google-cloud.svg",
  // },
  // {
  //   id: 3,
  //   title: "",
  //   image: "/services/full-stack/tech-stack/docker.png",
  // },

  // {
  //   id: 4,
  //   title: "CI/CD",
  //   image: "/services/mobile-app/tech-stack/ci-cd.png",
  // },
]);

const techStackAiMl = ref([
  {
    id: 1,
    title: "",
    image: "/services/full-stack/tech-stack/mlops.png",
  },
  {
    id: 2,
    title: "LLMops",
    image: "/services/full-stack/tech-stack/llmops.png",
  },
  {
    id: 3,
    title: "LLMs",
    image: "/services/full-stack/tech-stack/llms.png",
  },
  {
    id: 4,
    title: "OpenAI API",
    image: "/services/full-stack/tech-stack/openai.png",
  },
  {
    id: 5,
    title: "Cursor",
    image: "/services/full-stack/tech-stack/cursor.png",
  },
  {
    id: 6,
    title: "Github-copilot",
    image: "/services/full-stack/tech-stack/github-copilot.png",
  },
  {
    id: 7,
    title: "Claude",
    image: "/services/full-stack/tech-stack/claude.png",
  },
  {
    id: 8,
    title: "Windserf",
    image: "/services/full-stack/tech-stack/windserf.png",
  },
]);
</script>
<template>
  <div class="md:mt-[200px] mt-[100px]">
    <h2 class="sixth-h-tag">Tech Stack & Tools</h2>

    <div class="flex gap-2.5 md:gap-10 lg:gap-[55px] mt-20">
      <div class="h-auto bg-[#999999] w-[5px]"></div>

      <div class="space-y-20">
        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Full Stack Technologies</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in fullStackTechnologies"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-medium text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[50px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center mt-2">
                {{ programmingLanguage.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Mobile Technologies</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackMobileTechnologies"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="text-center">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Backend</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in techStackBackendFrameworks"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[50px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p
                class="whitespace-nowrap mt-2"
                :class="
                  programmingLanguage.title === 'AWS Dynamo DB'
                    ? 'text-lg'
                    : 'text-xl'
                "
              >
                {{ programmingLanguage.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Database Solutions</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="databaseSolution in techStackDatabaseSolutions"
              :key="databaseSolution.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold text-xl"
            >
              <img
                :class="
                  databaseSolution.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[50px]'
                "
                :src="databaseSolution.image"
                :alt="databaseSolution.title"
              />
              <p
                class="whitespace-nowrap mt-2"
                :class="
                  databaseSolution.title === 'AWS Dynamo DB'
                    ? 'text-lg'
                    : 'text-xl'
                "
              >
                {{ databaseSolution.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">
            Artificial Intelligence and Machine Learning
          </h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="aiMl in techStackAiMl"
              :key="aiMl.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold"
            >
              <img
                :class="aiMl.title === '' ? 'max-h-[86px]' : 'max-h-[58px]'"
                :src="aiMl.image"
                :alt="aiMl.title"
              />
              <p class="whitespace-nowrap">
                {{ aiMl.title }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">Cloud & Devops</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="programmingLanguage in cloudDevopsTechnologies"
              :key="programmingLanguage.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-medium text-xl"
            >
              <img
                :class="
                  programmingLanguage.title === ''
                    ? 'max-h-[86px]'
                    : 'max-h-[58px]'
                "
                :src="programmingLanguage.image"
                :alt="programmingLanguage.title"
              />
              <p class="">{{ programmingLanguage.title }}</p>
            </div>
          </div>
        </div>

        <!-- <div class="space-y-10 text-left">
          <h3 class="fifth-h-tag">IDEs</h3>
          <div class="flex flex-wrap justify-start gap-5">
            <div
              v-for="cloudDevOps in techStackCloudDevOps"
              :key="cloudDevOps.id"
              class="bg-[#F1F1F2] rounded-[10px] w-[157px] h-[118px] flex flex-col py-4 justify-between items-center text-[#1D1A20] font-bold"
            >
              <img
                :class="
                  cloudDevOps.title === '' ? 'max-h-[86px]' : 'max-h-[50px]'
                "
                :src="cloudDevOps.image"
                :alt="cloudDevOps.title"
              />
              <p
                class="whitespace-nowrap mt-2"
                :class="
                  cloudDevOps.title === 'Automation tools'
                    ? 'text-lg'
                    : 'text-xl'
                "
              >
                {{ cloudDevOps.title }}
              </p>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped></style>

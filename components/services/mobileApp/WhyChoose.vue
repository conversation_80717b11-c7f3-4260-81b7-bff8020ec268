<script setup>
const headerTitle = ref(`<div class="overflow-hidden">
          <p class="text-[20px] leading-[30px] text-[#999999] !title-animation">Why Choose</p>
          <p class="text-[20px] leading-[30px]  text-[#999999] !title-animation"><span class="instrument-italic">Devxhub</span> for <span class='instrument-italic'>Mobile App</span></p>
          <p class="text-[20px] leading-[30px]  text-[#999999] !title-animation">Developement?</p>
            </div>
            `);
const whyChooseDevxhub = ref([
  {
    image: "/services/mobile-app/why-choose/leadership.svg",
    title: "Strong Leadership",
    description:
      "From idea to launch, we own every step of your app's journey. Our team leads with purpose, delivering smart, scalable, and future-ready mobile solutions.",
  },
  {
    image: "/services/mobile-app/why-choose/full-stack-mobile-expertise.svg",
    title: "Full-Stack Mobile Expertise",
    description:
      "We bring deep expertise across the entire mobile stack, including front-end design and back-end logic. If you need a native or cross-platform application, we can help.",
  },
  {
    image: "/services/mobile-app/why-choose/cost-effective.svg",
    title: "Cost-Effective Without Compromise",
    description:
      "Our strategic talent base in Bangladesh and other tech hubs allows us to offer high-quality mobile development without cutting corners.",
  },
  {
    image: "/services/mobile-app/why-choose/customize-high-tech-solutions.svg",
    title: "Customized, high-tech solutions",
    description:
      "We don't have a one-size-fits-all solution. Utilizing the latest technologies and design trends, we customize our apps for your business needs.",
  },
  {
    image: "/services/mobile-app/why-choose/ongoing-commmunication.svg",
    title: "Clear, Ongoing Communication",
    description:
      "Stay in the loop with regular updates, real-time collaboration, and transparent reporting. We keep you informed, involved, and in control.",
  },
  {
    image: "/services/mobile-app/why-choose/end-to-end-mobile-support.svg",
    title: "End-to-End Mobile App Support",
    description:
      "Throughout every phase of your mobile app's lifecycle, Devxhub offers complete support - from strategy and design to launch, maintenance, and scaling.",
  },
]);

const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  // what makes us difference section animation
  const headerTitleAnimation = document.querySelectorAll(".title-aimation");
  console.log(headerTitleAnimation, "headerTitleAnimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  headerTitleAnimation.forEach((item) => {
    observerThree.observe(item);
  });
};

onMounted(() => {
  setTimeout(() => {
    viewPortEnter();
  }, 1000);
});
</script>

<template>
  <div>
    <ServicesWhyChooseDevxhub
      class="relative md:mt-[180px] mt-[120px]"
      :header-title="headerTitle"
      :core-values="whyChooseDevxhub"
    />
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 0.5fr 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  grid-column-gap: 70px;
  grid-row-gap: 65px;
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
  }
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
</style>

<script setup>
const props = defineProps({
  coreValues: {
    type: Object,
    default: () => {},
  },
  headerTitle: {
    type: String,
    default: "",
  },
});
const viewPortEnter = () => {
  const titleAnimation = document.querySelectorAll(".title-up-animation");
  const textUpAnimation = document.querySelectorAll(".text-up-animation");

  const titleAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  titleAnimation.forEach((item) => {
    titleAnimationObserver.observe(item);
  });

  const textUpAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  textUpAnimation.forEach((item) => {
    textUpAnimationObserver.observe(item);
  });

  // what makes us difference section animation
  const headerTitleAnimation = document.querySelectorAll(".title-aimation");
  console.log(headerTitleAnimation, "headerTitleAnimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  headerTitleAnimation.forEach((item) => {
    observerThree.observe(item);
  });
};

onMounted(() => {
  setTimeout(() => {
    viewPortEnter();
  }, 1000);
});
</script>

<template>
  <div class="w-full bg-black pt-[64px] md:pt-[80px] overflow-hidden">
    <div class="container-fluid relative">
      <div class="case-grid-item">
        <div v-html="headerTitle"></div>
        <div class="grid-item gap-70 w-full pb-[104px]">
          <div
            v-for="(coreValue, index) in coreValues"
            :key="index"
            class="border-b border-[#ffffff1a] text-up-animation last:border-b-0"
            :class="[
              index % 2 === 0
                ? 'min-[1440px]:pr-[50.5px] min-[992px]:pr-[25px]'
                : 'min-[1440px]:pl-[50.5px] min-[992px]:pl-[25px]',
              index === 4 ? 'responsive-border-bottom' : '',
            ]"
          >
            <div
              class="flex-col items-start md:space-y-5 space-y-5 lg:pb-10 pb-5 last:pb-0 [>div:nth-last-child(2)]:pb-0"
            >
              <img
                v-if="coreValue.image"
                class="lg:w-[50px] lg:h-[50px] w-10 h-10"
                :src="coreValue.image"
                :alt="coreValue.title"
              />
              <div class="flex flex-col md:space-y-2.5 space-y-2">
                <h2 class="third-h-tag">
                  {{ coreValue.title }}
                </h2>
                <p class="forth-p-tag lg:pb-10 pb-5">
                  {{ coreValue.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <img
        src="/public/whyChooseUs/why-choose-us.svg"
        alt="devxhub logo"
        class="absolute left-[-105px] bottom-[-30px] md:block hidden"
      />
    </div>
  </div>
</template>

<style scoped>
.case-grid-item {
  @apply min-[1440px]:gap-x-[120px] xl:gap-x-[70px] gap-x-[30px];
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: minmax(250px, 0.541fr) 1.5fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.grid-item {
  z-index: 0;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}
.grid-item.gap-70 {
  /* min-[1440px]:gap-x-[101px] gap-x-[50px] */
  @apply lg:!gap-y-10 !gap-y-5;
  /* grid-column-gap: 101px;
  grid-row-gap: 65px; */
}
@media screen and (min-width: 1024px) {
  .grid-item.gap-70 {
    @apply gap-y-0;
  }
}
@media screen and (max-width: 991px) {
  .grid-item {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
  .grid-item {
    grid-template-columns: 1fr;
    @apply pt-10;
  }
}
@media screen and (max-width: 425px) {
  .grid-item {
    /* min-[1440px]:gap-x-[101px] gap-x-[50px] */
    @apply pt-8;

    /* grid-column-gap: 101px;
  grid-row-gap: 65px; */
  }
}
@media screen and (min-width: 989px) {
  .responsive-border-bottom {
    border-bottom: 0;
  }
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}

.grid-item > div:nth-last-child(2) {
  padding-bottom: 0px;
}
</style>

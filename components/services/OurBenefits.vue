<script setup lang="ts">
import { ref } from "vue";

const ourBenefits = ref([
  {
    id: 1,
    bgColor: "#f3f1ee",
    title: "Dedicated Team",
    description: `We are an In-house team of experts, each bringing unique talents and insights from various fields and projects to the table.`,
    image: "/services/our-benefits/dedicatedTeam.png",
  },
  {
    id: 2,
    bgColor: "#f3f1ee",
    title: "Transparency",
    description:
      "Stay informed with clear communication, real-time progress tracking, and regular updates, ensuring accountability and full project control.",
    image: "/services/our-benefits/transparency.png",
  },
  {
    id: 3,
    bgColor: "#f3f1ee",
    title: "Scalability & Flexibility",
    description: `Need to scale up, down, or take a break? No worries—you’re always in control, hassle-free.`,
    image: "/services/our-benefits/scalabilityFlexibility.png",
  },
  {
    id: 4,
    bgColor: "#f3f1ee",
    title: "No Hiring Downtime",
    description:
      "Skip recruitment delays! Get a pre-vetted expert team ready to jump in and start delivering results immediately.",
    image: "/services/our-benefits/noHiringDowntime.png",
  },
  {
    id: 5,
    bgColor: "#1D1A20",
    title: "Schedule a free discovery call",
    description: "A 30-min discovery call to discuss your goals.",
    topImage: "/landing/Mask_group.svg",
  },
  {
    id: 6,
    bgColor: "#f3f1ee",
    title: "Certified Experts",
    description: `Our experienced developers go beyond coding, delivering innovative, high-quality solutions that fit your business needs & growth.`,
    image: "/services/our-benefits/certifiedExperts.png",
  },
  {
    id: 7,
    bgColor: "#1D1A20",
    title: "Schedule a free discovery call",
    description: "A 30-min discovery call to discuss your goals.",
    topImage: "/landing/Mask_group.svg",
  },
]);
</script>

<template>
  <div>
    <h3 class="sixth-h-tag font-bold !text-[#999]">
      Why Businesses
      <span class="instrument-italic text-[#f1f1f2]">Choose Us</span>
    </h3>
    <div
      class="grid lg:grid-cols-[1fr_1fr_1fr] sm:grid-cols-[1fr_1fr] grid-cols-[1fr] grid-rows-[auto_auto] md:gap-[18px] gap-5 md:pt-10 pt-8"
    >
      <div
        v-for="ourBenefit in ourBenefits"
        :key="ourBenefit.id"
        class="px-0 overflow-hidden flex flex-col items-center rounded-lg relative max-h-[340px]"
        :class="[
          ourBenefit.id !== 5 && ourBenefit.id !== 7
            ? 'pt-5 space-y-0 justify-between'
            : 'pt-[68px] pb-8',
          ourBenefit.id === 5 ? 'hidden md:flex' : '',
          ourBenefit.id === 7 ? 'md:hidden flex' : '',
          // Hide on mobile, show on md+
        ]"
        :style="{ backgroundColor: ourBenefit.bgColor }"
      >
        <img
          v-if="ourBenefit.topImage"
          class="w-full absolute inset-0 max-w-[351px] left-1/2 transform -translate-x-1/2"
          :src="ourBenefit.topImage"
          :alt="ourBenefit.title"
        />
        <div
          v-if="ourBenefit.id === 5 || ourBenefit.id === 7"
          class="max-w-[260px] text-center flex flex-col justify-center space-y-4"
        >
          <div
            style="
              transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1)
                rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
              opacity: 1;
              transform-style: preserve-3d;
            "
            class="clutch-badge-new flex px-3 py-1 items-center justify-start border border-[#fff] rounded-full gap-2 bg-[#1D1A20] max-w-[159px] mx-auto"
          >
            <img
              src="/public/landing/clutch.svg"
              loading="lazy"
              alt=""
              class="clutch-logo"
            />
            <img
              src="/public/landing/star-group.svg"
              loading="lazy"
              alt=""
              class="clutch_stars"
            />
            <div class="text-[#f1f1f2] font-bold">5.0</div>
          </div>
          <h4 class="text-2xl font-bold text-[#f1f1f2] !mt-[20px]">
            {{ ourBenefit.title }}
          </h4>
          <NuxtLink
            to="/contact-us#appointment"
            class="mx-auto !mt-[33px] w-[130px] h-10 text-base flex justify-center items-center text-[#1D1A20] bg-[#FFD700] rounded-full font-medium self-start final"
            >Book a call</NuxtLink
          >
        </div>
        <div
          v-if="ourBenefit.id === 5 || ourBenefit.id === 7"
          class="pt-[33px] pb-[20px]"
        >
          <p class="text-[#999] text-center">
            {{ ourBenefit.description }}
          </p>
        </div>
        <div
          v-if="ourBenefit.id !== 5 && ourBenefit.id !== 7"
          class="max-w-[336px] text-center flex flex-col space-y-2"
        >
          <h3 class="text-2xl text-[#1D1A20] font-bold">
            {{ ourBenefit.title }}
          </h3>
          <p class="text-color leading-[24px]">
            {{ ourBenefit.description }}
          </p>
        </div>
        <img
          v-if="ourBenefit.id !== 5 && ourBenefit.id !== 7"
          class="our-benefit-image w-full"
          :src="ourBenefit.image"
          :alt="ourBenefit.title"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-color {
  color: rgba(29, 26, 32, 0.7);
}
</style>

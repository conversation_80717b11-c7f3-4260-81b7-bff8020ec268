<script setup>
import { storeToRefs } from "pinia";
import { useWebdevChecklistStore } from "~/stores/webdevchecklist";
const { selectedCheckList, squeeze } = storeToRefs(useWebdevChecklistStore());
const { setSelectedCheckList, setSelectedCheckLists, setSqueeze } =
  useWebdevChecklistStore();
</script>

<template>
  <div class="flex flex-col min-h-full overflow-hidden">
    <div class="flex flex-col items-center text-white px-7">
      <div class="w-full h-[100px] flex items-center justify-start space-x-11">
        <BaseIconMenu class="min-w-8 cursor-pointer" @click="setSqueeze()" />
        <NuxtLink to="/">
          <BaseIconDEVxHUBLogo
            class="md:w-[170px] md:h-[32px] w-[32px] h-[32px] transition-all duration-500 ease-in-out"
            :class="squeeze ? ' opacity-100' : 'opacity-0'"
          />
        </NuxtLink>
      </div>
      <div class="flex flex-col md:mt-0 mt-4 w-full">
        <NuxtLink
          to="/checklist/web-developer-checklist"
          class="!mt-[34px] cursor-pointer flex justify-start items-center space-x-6 w-full"
          @click.stop="
            [
              setSelectedCheckList('developer'),
              setSelectedCheckLists('developer'),
              $emit('set-title', 'Web Developer Checklist'),
            ]
          "
        >
          <BaseIconDeveloperChecklist
            class="min-w-5"
            :color="selectedCheckList === 'developer' ? '#FDB21D' : '#F4F4F5'"
          />
          <h2
            class="text-base whitespace-nowrap transition-all duration-500 ease-in-out"
            :class="[
              selectedCheckList === 'developer'
                ? 'text-[#FDB21D] font-medium'
                : 'text-[#F4F4F5]',
              squeeze ? ' opacity-100' : 'opacity-0',
            ]"
          >
            Web Developer Checklist
          </h2>
        </NuxtLink>
        <NuxtLink
          to="/checklist/sqa-checklist"
          class="!mt-[42px] cursor-pointer flex justify-start items-center space-x-6 w-full"
          @click.stop="
            [
              setSelectedCheckList('sqa'),
              setSelectedCheckLists('sqa'),
              $emit('set-title', 'SQA Checklist'),
            ]
          "
        >
          <BaseIconSQAChecklist
            class="min-w-7"
            :color="selectedCheckList === 'sqa' ? '#FDB21D' : '#F4F4F5'"
          />
          <h2
            class="text-base whitespace-nowrap transition-all duration-500 ease-in-out"
            :class="[
              selectedCheckList === 'sqa'
                ? 'text-[#FDB21D] font-medium'
                : 'text-[#F4F4F5]',
              squeeze ? ' opacity-100' : 'opacity-0',
            ]"
          >
            SQA Checklist
          </h2>
        </NuxtLink>
        <NuxtLink
          to="/checklist/ba-checklist"
          class="!mt-[42px] cursor-pointer flex justify-start items-center space-x-6 w-full"
          @click.stop="
            [
              setSelectedCheckList('ba'),
              setSelectedCheckLists('ba'),
              $emit('set-title', 'BA Checklist'),
            ]
          "
        >
          <BaseIconBAChecklist
            class="min-w-5"
            :color="selectedCheckList === 'ba' ? '#FDB21D' : '#F4F4F5'"
          />
          <h2
            class="text-base whitespace-nowrap transition-all duration-500 ease-in-out"
            :class="[
              selectedCheckList === 'ba'
                ? 'text-[#FDB21D] font-medium'
                : 'text-[#F4F4F5]',
              squeeze ? ' opacity-100' : 'opacity-0',
            ]"
          >
            BA Checklist
          </h2>
        </NuxtLink>
        <NuxtLink
          to="/checklist/ui-ux-checklist"
          class="!mt-[42px] cursor-pointer flex justify-start items-center space-x-6 w-full"
          @click.stop="
            [
              setSelectedCheckList('uiux'),
              setSelectedCheckLists('uiux'),
              $emit('set-title', 'UI/UX Checklist'),
            ]
          "
        >
          <BaseIconUIUXChecklist
            class="min-w-5"
            :color="selectedCheckList === 'uiux' ? '#FDB21D' : '#F4F4F5'"
          />
          <h2
            class="text-base whitespace-nowrap transition-all duration-500 ease-in-out"
            :class="[
              selectedCheckList === 'uiux'
                ? 'text-[#FDB21D] font-medium'
                : 'text-[#F4F4F5]',
              squeeze ? ' opacity-100' : 'opacity-0',
            ]"
          >
            UI/UX Checklist
          </h2>
        </NuxtLink>
      </div>
    </div>
    <div
      class="hidden items-center space-x-[5px] text-base font-normal text-white mt-0"
    >
      <p>Get the extension</p>
      <a
        class="underline"
        href="https://chrome.google.com/webstore/detail/web-developer-checklist/iahamcpedabephpcgkeikbclmaljebjp?hl=en-US"
        target="_blank"
        >Chrome</a
      >
      <p>|</p>
      <a
        class="underline"
        href="https://addons.mozilla.org/en-US/firefox/addon/webdeveloperchecklist/"
        target="_blank"
        >Firefox</a
      >
    </div>
  </div>
</template>

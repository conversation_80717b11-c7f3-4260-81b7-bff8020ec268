<script setup>
const props = defineProps({
  wrapperClass: {
    type: String,
    default: "w-full h-full flex flex-col justify-center items-center container-fluid"
  },
  items: {
    type: Array,
    default: () => []
  },
  defaultItems: {
    type: Boolean,
    default: true
  }
});

const loaderItems = computed(() => {
  if (!props.defaultItems) {
    return props.items;
  }

  const defaultWidths = ['max-w-[901px]', 'max-w-[701px]', 'max-w-[905px]', 'max-w-[705px]', 'w-[190px]'];
  const defaultHeights = ['h-10', 'h-10', 'h-8', 'h-8', 'h-10'];
  const defaultMargins = ['', 'mt-5', 'mt-[32px]', 'mt-3', 'mt-[80px]'];

  return Array.from({ length: 5 }, (_, index) => ({
    width: defaultWidths[index],
    height: defaultHeights[index],
    marginTop: defaultMargins[index],
    extraClasses: index === 4 ? 'px-[52px] py-[12px] rounded-full' : ''
  }));
});
</script>

<template>
  <div :class="`animate-pulse ${wrapperClass}`">
    <div
      v-for="(item, index) in loaderItems"
      :key="index"
      :class="`w-full bg-slate-400 ${item.width} ${item.height} ${item.marginTop} ${item.extraClasses || ''}`"
    ></div>
  </div>
</template>

<style>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
<template>
  <div
    class="phone-number-input"
    :style="{ '--color': color, '--background': background }"
  >
    <vue-tel-input
      id="vue-tel-input"
      v-model="phone"
      :class="inputClasses"
      :dropdown-options="dropdownOptions"
      :inputOptions="inputOptions"
      :styleClasses="'phoneInput'"
      :validCharactersOnly="true"
      :autoFormat="true"
      mode="international"
      @blur="formBlur(phone)"
      @validate="formValidation"
      @on-input="formInput()"
      @enter="emit('submit', { phone: finalPhone })"
      @country-changed="getFinalNumber()"
    ></vue-tel-input>
    <div
      id="phone-error"
      v-if="error"
      class="error text-sm text-left text-red-500"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>
<script setup>
import { useCountries } from "~/composables/useCountries";

const emit = defineEmits(["error", "submit"]);
const finalPhone = ref(null);
const phone = ref(null);
const error = ref(false);
const errorMessage = ref("");
const dropdownOptions = {
  disabled: true,
  showDialCodeInList: true,
  showDialCodeInSelection: false,
  showFlags: true,
  showSearchBox: false,
};
const inputOptions = {
  autofocus: false,
  maxlength: 20,
  // required: true,
  placeholder: "Number with country code  ",
  styleClasses: "telInput",
  // showDialCode: true
};

const { useAllCountriesModify } = useCountries();
const props = defineProps({
  color: {
    type: String,
    required: false,
    default: "#E4801D",
  },
  background: {
    type: String,
    required: false,
    default: "#fff",
  },

  inputClasses: {
    type: String,
    default: "",
  },
});

const formInput = () => {
  if (phone.value === "") {
    error.value = true;
    errorMessage.value = "The field is required";
    emit("error", { error: error.value, phone: phone.value });
  } else {
    if (phone.value.includes("+")) {
      const extracted = phone.value.substring(0, phone.value.indexOf(" "));
      const restOfNumber = phone.value.substring(phone.value.indexOf(" ") + 1);
      // phone.value = restOfNumber;
      finalPhone.value = extracted + " " + restOfNumber;
    }
    emit("error", { error: error.value, phone: finalPhone.value });
  }
};
const getFinalNumber = () => {
  setTimeout(() => {
    if (phone.value.includes("+")) {
      const extracted = phone.value.substring(0, phone.value.indexOf(" "));
      const restOfNumber = phone.value.substring(phone.value.indexOf(" ") + 1);
      phone.value = restOfNumber;
      finalPhone.value = extracted + " " + restOfNumber;
    }
    emit("error", { error: error.value, phone: finalPhone.value });
  });
};
const formValidation = (data) => {
  if (data.valid !== "undefined") {
    if (data.valid) {
      error.value = false;
      if (phone.value.includes("+")) {
        const extracted = phone.value.substring(0, phone.value.indexOf(" "));
        const restOfNumber = phone.value.substring(
          phone.value.indexOf(" ") + 1
        );
        // phone.value = restOfNumber;
        finalPhone.value = extracted + " " + restOfNumber;
      }
      emit("error", { error: error.value, phone: finalPhone.value });
    } else if (!data.valid && phone.value !== "") {
      error.value = true;
      errorMessage.value = "Phone number is invalid";
      emit("error", { error: error.value, phone: phone.value });
    } else if (!data.valid && phone.value === "") {
      error.value = false;
      emit("error", { error: error.value, phone: phone.value });
      // this.errorMessage = 'The field is required'
    } else {
      error.value = false;
      errorMessage.value = "";
      emit("error", { error: error.value, phone: phone.value });
    }
  } else {
    error.value = false;
    errorMessage.value = "";
    emit("error", { error: error.value, phone: phone.value });
  }
};
const formBlur = () => {
  if (phone.value === "") {
    error.value = true;
    errorMessage.value = "The field is required";
    emit("error", { error: error.value, phone: phone.value });
  }
};
defineExpose({
  formBlur,
  phone,
  error,
});
</script>
<style lang="scss" scoped>
.phone-number-input {
  $color: var(--color);
  color: black;
  text-align: center;

  .input-wrapper {
    $background-color: var(--background);
    background-color: $background-color;
    box-shadow: 0px 1px 3px rgba(142, 82, 0, 0.7);
    @apply relative flex content-between rounded-full;

    .country-input {
      min-width: 40px;
      max-width: 86px;
      color: $color;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      direction: rtl;
      @apply cursor-pointer text-right pl-10 rounded-tl-full rounded-bl-full font-semibold leading-none focus:outline-none;

      option {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        direction: ltr;
        @apply font-semibold text-left pl-0;

        &:disabled {
          @apply text-gray-500;
        }

        &:focus,
        &:checked,
        &:hover {
          color: white;
          background-color: $color !important;
        }
      }
    }

    .select-toggle {
      color: $color;
      @apply absolute top-1 left-4 pointer-events-none;

      svg {
        @apply text-3xl;
      }
    }

    .number-input {
      color: $color;
      background-color: $background-color;
      // &::placeholder {
      //   color: $color;
      //   @apply font-semibold;
      // }
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}

.phoneNumberInputPartner {
  $color: var(--color);

  .input-wrapper {
    @apply mt-2;
    border-radius: 8px !important;
    box-shadow: 0px 0px 0px;

    .country-input {
      @apply h-10 md:h-14;
      background-color: #d1d5db;
    }

    .select-toggle {
      @apply top-1 md:top-2.5;
    }
  }
}
.phoneInput{
  color: #000;
}
.customPhoneInputClass {
  border-left-width: 0;
  border-right-width: 0;
  border-top-width: 0;
  border-radius: 0px;
  border-bottom-width: 2px !important;
  @apply pb-2.5 mt-5 border-[#1D1A20];
}
.vue-tel-input:focus-within{
  box-shadow: none;
}
</style>

<template>
  <div class="rounded-full">
    <svg class="rounded-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 53.48 53.48">
      <defs></defs>
      <g id="TwitterLayer_2" data-name="TwitterLayer 2">
        <g id="TwitterLayer_1" data-name="TwitterLayer 1">
          <circle id="Ellipse_84" data-name="Ellipse 84" class="twitter-1" cx="26.74" cy="26.74" r="26.74" />
          <path id="Path_2048" data-name="Path 2048" class="twitter-2"
            d="M43.12,19.22a13.07,13.07,0,0,1-3.67,1,6.44,6.44,0,0,0,2.81-3.53,12.84,12.84,0,0,1-4.06,1.55,6.4,6.4,0,0,0-10.89,5.83,18.15,18.15,0,0,1-13.17-6.68,6.39,6.39,0,0,0,2,8.53,6.24,6.24,0,0,1-2.9-.8v.08a6.4,6.4,0,0,0,5.12,6.27,6.52,6.52,0,0,1-2.88.11,6.4,6.4,0,0,0,6,4.44A12.92,12.92,0,0,1,12,38.66a18.19,18.19,0,0,0,28-15.31c0-.28,0-.55,0-.83a12.88,12.88,0,0,0,3.19-3.31Z" />
        </g>
      </g>
    </svg>
  </div>
</template>
<style lang="scss" scoped>
@import "~/assets/scss/icon/twitter.scss";
</style>

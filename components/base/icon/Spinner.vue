<script setup>
const props = defineProps({
  color: {
    type: String,
    default: 'white',
  },
  bgcolor: {
    type: String,
    default: 'black',
  }
});
</script>

<template>
  <svg width="142" height="142" viewBox="0 0 142 142" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1428_160)">
<path d="M71 142C110.212 142 142 110.212 142 71C142 31.7878 110.212 0 71 0C31.7878 0 0 31.7878 0 71C0 110.212 31.7878 142 71 142Z" :fill="bgcolor"/>
<path d="M11.8216 60.4068L12.1269 59.0223L21.6977 61.1594L22.7201 56.5728L23.8845 56.8355L22.5497 62.8066L11.8145 60.4139L11.8216 60.4068Z" :fill="color"/>
<path d="M21.5626 48.5356L20.0077 52.5187L23.6997 53.9671L25.4392 49.5296L26.5468 49.9627L24.2961 55.7208L14.0508 51.7093L16.2163 46.1784L17.3239 46.6115L15.6767 50.8218L18.9143 52.0856L20.4692 48.1025L21.5626 48.5285V48.5356Z" :fill="color"/>
<path d="M19.9652 41.6699L18.2612 44.5454L17.2246 43.9277L21.3781 36.92L22.4147 37.5377L20.7036 40.4274L29.1313 45.4187L28.4 46.6541L19.9723 41.6628L19.9652 41.6699Z" :fill="color"/>
<path d="M24.0615 33.2351C24.9206 34.3711 25.872 35.9899 26.1915 36.8064L25.7229 37.6016C25.1762 36.7283 24.097 35.358 23.2734 34.4989L24.0615 33.2351Z" :fill="color"/>
<path d="M32.2478 38.0418C32.972 37.8501 33.8524 37.4099 34.5269 36.6999C35.5351 35.6491 35.5635 34.506 34.7612 33.7392C34.0157 33.0292 33.185 33.0434 31.7508 33.725C30.0468 34.5841 28.6126 34.7403 27.4908 33.6682C26.2554 32.4825 26.3122 30.5797 27.7961 29.0319C28.5771 28.2154 29.3297 27.8107 29.8693 27.6474L30.4373 28.7337C30.0468 28.8544 29.3013 29.1597 28.6126 29.8839C27.576 30.9702 27.8245 31.9997 28.3712 32.518C29.1096 33.228 29.9403 33.0931 31.4171 32.4044C33.2205 31.5595 34.5269 31.4956 35.6913 32.6103C36.9196 33.7818 37.1113 35.7059 35.315 37.5803C34.5837 38.3471 33.5542 38.9719 32.8655 39.121L32.2549 38.0347L32.2478 38.0418Z" :fill="color"/>
<path d="M47.7968 27.1575C47.4986 27.6616 46.7602 28.4426 45.6384 29.1668C43.0327 30.8495 40.0081 30.4661 38.0556 27.4344C36.1883 24.5376 36.8841 21.3142 39.7525 19.4682C40.9027 18.7227 41.7902 18.5026 42.2091 18.4671L42.5499 19.6244C41.9535 19.6954 41.208 19.951 40.4341 20.4409C38.2686 21.8396 37.7219 24.1542 39.2839 26.5753C40.7394 28.8402 42.983 29.4508 45.2337 28.0024C45.9579 27.5338 46.604 26.909 46.9306 26.3623L47.7897 27.1504L47.7968 27.1575Z" :fill="color"/>
<path d="M55.4804 18.3251C56.8791 21.8467 55.4804 24.5589 52.8747 25.5955C50.1767 26.6676 47.4503 25.3257 46.222 22.2301C44.9298 18.9854 46.1084 16.0602 48.8348 14.9739C51.6251 13.8663 54.2805 15.2863 55.4875 18.318L55.4804 18.3251ZM47.6136 21.6408C48.4798 23.8276 50.4394 25.3115 52.5197 24.4879C54.6142 23.6572 55.0331 21.2574 54.1101 18.9428C53.3007 16.9122 51.3979 15.2082 49.1969 16.0815C47.0101 16.9477 46.719 19.3972 47.6136 21.6408Z" :fill="color"/>
<path d="M56.8079 12.5741L58.1995 12.3043L60.0881 21.9248L64.696 21.0231L64.9232 22.1946L58.9166 23.3732L56.8008 12.5812L56.8079 12.5741Z" :fill="color"/>
<path d="M65.6191 11.1257L67.0391 11.0618L67.4864 20.8598L72.1795 20.6468L72.2363 21.8396L66.1232 22.1165L65.6191 11.1257Z" :fill="color"/>
<path d="M75.5585 18.6446L73.9965 21.939L72.541 21.7615L77.6033 11.3032L79.3002 11.5162L81.6716 22.8904L80.1664 22.7058L79.428 19.1274L75.5585 18.6517V18.6446ZM79.2718 17.9843L78.5902 14.7112C78.434 13.9657 78.3559 13.2983 78.2707 12.6522H78.2423C78.0009 13.277 77.7382 13.916 77.4471 14.555L75.9845 17.5796L79.2718 17.9843Z" :fill="color"/>
<path d="M86.0382 12.9149C86.6701 12.9788 87.6286 13.1776 88.58 13.4758C89.9361 13.9018 90.7313 14.413 91.2141 15.1514C91.633 15.7265 91.7608 16.4507 91.4981 17.2814C91.1786 18.2896 90.2272 18.9712 88.9989 19.0209V19.0493C89.9077 19.6102 90.8094 20.803 90.3195 22.365C90.0355 23.2667 89.4604 23.8418 88.7717 24.1826C87.8274 24.6228 86.5494 24.5589 84.8383 24.0193C83.9082 23.7211 83.2124 23.4371 82.7793 23.2312L86.0382 12.9149ZM84.4478 22.6632C84.6963 22.791 85.0797 22.933 85.5625 23.0821C86.9612 23.5223 88.4167 23.4229 88.8995 21.8964C89.3539 20.4622 88.3102 19.4824 86.8334 19.0209L85.7116 18.6659L84.4478 22.6632ZM86.0382 17.6364L87.2665 18.0269C88.7007 18.4813 89.7728 17.9985 90.0923 16.9903C90.4828 15.762 89.7018 14.981 88.3315 14.5479C87.7067 14.3491 87.3375 14.2852 87.1032 14.2639L86.0382 17.6364Z" :fill="color"/>
<path d="M101.643 25.4464C99.8252 28.7692 96.8431 29.4224 94.3794 28.0734C91.8305 26.6747 91.1277 23.7211 92.7252 20.803C94.4008 17.7429 97.4041 16.7773 99.9814 18.1902C102.615 19.6315 103.212 22.5851 101.643 25.4464ZM94.0742 21.4988C92.9453 23.5578 93.0518 26.0144 95.0114 27.0936C96.9852 28.1728 99.101 26.9729 100.301 24.779C101.352 22.862 101.451 20.306 99.3708 19.17C97.3118 18.0411 95.2386 19.3759 94.0742 21.4988Z" :fill="color"/>
<path d="M106.897 22.933C107.543 23.2809 108.388 23.8773 109.141 24.5021C110.305 25.4748 110.88 26.3126 111.015 27.2285C111.129 27.9598 110.887 28.7337 110.34 29.3869C109.41 30.5016 108.09 30.6578 106.954 30.2176L106.925 30.2531C107.387 31.0199 107.28 31.95 106.748 32.9937C106.045 34.3995 105.562 35.3864 105.47 35.855L104.341 34.9178C104.398 34.5628 104.817 33.6824 105.456 32.4257C106.173 31.0483 106.088 30.2318 105.129 29.3727L104.1 28.5136L101.047 32.1772L99.9531 31.2684L106.897 22.9259V22.933ZM104.781 27.69L105.896 28.6201C107.06 29.5928 108.338 29.5644 109.141 28.5988C110.049 27.5054 109.659 26.3694 108.516 25.3967C107.99 24.9565 107.571 24.6938 107.351 24.6015L104.781 27.69Z" :fill="color"/>
<path d="M110.106 35.8976L106.713 37.2324L105.775 36.1035L116.624 31.9358L117.718 33.2564L111.661 43.1822L110.689 42.0178L112.598 38.9009L110.099 35.9047L110.106 35.8976ZM113.266 37.9566L115.012 35.1024C115.41 34.4563 115.808 33.9096 116.191 33.3771L116.17 33.3487C115.566 33.6398 114.934 33.9309 114.281 34.1936L111.143 35.4006L113.266 37.9495V37.9566Z" :fill="color"/>
<path d="M121.005 39.9517L119.273 37.0904L120.309 36.4656L124.527 43.4378L123.49 44.0626L121.75 41.1871L113.372 46.2565L112.627 45.0282L121.005 39.9588V39.9517Z" :fill="color"/>
<path d="M122.312 53.2358L120.7 49.274L117.029 50.7721L118.833 55.1883L117.732 55.6356L115.396 49.9059L125.585 45.7524L127.829 51.2549L126.728 51.7022L125.017 47.5132L121.801 48.8267L123.419 52.7885L122.333 53.2287L122.312 53.2358Z" :fill="color"/>
<path d="M130.051 82.289L129.724 83.6735L120.182 81.4228L119.103 85.9952L117.945 85.7254L119.351 79.7685L130.058 82.2961L130.051 82.289Z" :fill="color"/>
<path d="M120.167 94.0466L121.772 90.0848L118.094 88.6009L116.305 93.0171L115.197 92.5698L117.519 86.8401L127.715 90.9652L125.485 96.4677L124.385 96.0204L126.082 91.8314L122.858 90.525L121.254 94.4868L120.167 94.0466Z" :fill="color"/>
<path d="M121.687 100.919L123.427 98.0581L124.456 98.6829L120.225 105.641L119.195 105.016L120.942 102.148L112.578 97.057L113.324 95.8287L121.687 100.919Z" :fill="color"/>
<path d="M117.498 109.312C116.653 108.169 115.715 106.535 115.41 105.712L115.886 104.924C116.418 105.804 117.49 107.189 118.3 108.055L117.498 109.312Z" :fill="color"/>
<path d="M109.369 104.405C108.645 104.583 107.758 105.023 107.076 105.719C106.061 106.756 106.011 107.899 106.806 108.673C107.538 109.39 108.368 109.383 109.817 108.722C111.528 107.884 112.969 107.742 114.077 108.829C115.298 110.029 115.22 111.931 113.722 113.458C112.934 114.26 112.174 114.665 111.634 114.821L111.08 113.728C111.471 113.607 112.216 113.309 112.912 112.599C113.963 111.527 113.722 110.49 113.182 109.965C112.444 109.248 111.62 109.368 110.136 110.043C108.319 110.866 107.019 110.916 105.862 109.787C104.648 108.602 104.477 106.677 106.295 104.817C107.033 104.058 108.07 103.447 108.759 103.305L109.355 104.398L109.369 104.405Z" :fill="color"/>
<path d="M93.6914 115.112C93.9967 114.615 94.7422 113.834 95.8711 113.124C98.4981 111.477 101.516 111.896 103.433 114.949C105.264 117.867 104.526 121.083 101.636 122.894C100.479 123.625 99.5844 123.831 99.1655 123.867L98.8389 122.702C99.4353 122.638 100.188 122.397 100.962 121.907C103.149 120.537 103.724 118.229 102.183 115.78C100.749 113.501 98.5194 112.854 96.2545 114.282C95.5232 114.743 94.87 115.361 94.5363 115.9L93.6914 115.105V115.112Z" :fill="color"/>
<path d="M85.8964 123.859C84.5403 120.324 85.9674 117.626 88.5873 116.617C91.2995 115.574 94.0117 116.944 95.2045 120.054C96.4541 123.313 95.2471 126.224 92.5065 127.275C89.702 128.354 87.0679 126.898 85.8964 123.852V123.859ZM93.7987 120.629C92.9538 118.435 91.0155 116.93 88.9281 117.732C86.8265 118.542 86.3792 120.927 87.2738 123.263C88.0548 125.308 89.9434 127.026 92.1515 126.181C94.3454 125.336 94.6649 122.894 93.7987 120.636V120.629Z" :fill="color"/>
<path d="M84.5108 129.582L83.1121 129.838L81.3371 120.196L76.715 121.048L76.502 119.876L82.5157 118.769L84.5108 129.582Z" :fill="color"/>
<path d="M75.6781 130.931L74.2581 130.981L73.9244 121.176L69.2242 121.332L69.1816 120.139L75.3018 119.933L75.671 130.931H75.6781Z" :fill="color"/>
<path d="M65.8251 123.299L67.4226 120.026L68.8781 120.224L63.6951 130.626L61.9982 130.392L59.7617 118.982L61.2669 119.188L61.9627 122.773L65.8322 123.299H65.8251ZM62.0976 123.916L62.7366 127.197C62.8857 127.942 62.9567 128.609 63.0348 129.263H63.0703C63.3188 128.645 63.5886 128.006 63.8868 127.367L65.3849 124.357L62.1047 123.909L62.0976 123.916Z" :fill="color"/>
<path d="M55.2744 128.908C54.6425 128.837 53.684 128.624 52.7397 128.311C51.3907 127.864 50.6026 127.346 50.1198 126.607C49.708 126.025 49.5873 125.301 49.8571 124.477C50.1908 123.469 51.1493 122.802 52.3776 122.766V122.738C51.4759 122.163 50.5884 120.956 51.0925 119.408C51.3907 118.506 51.9729 117.945 52.6616 117.604C53.6059 117.178 54.891 117.256 56.595 117.817C57.5251 118.123 58.2209 118.421 58.6469 118.627L55.2673 128.908H55.2744ZM55.3312 124.186L54.1029 123.781C52.6758 123.313 51.5966 123.781 51.2629 124.79C50.8582 126.011 51.6321 126.799 52.9953 127.253C53.613 127.459 53.9893 127.53 54.2165 127.551L55.3241 124.186H55.3312ZM56.9784 119.181C56.7299 119.046 56.3465 118.904 55.8637 118.747C54.4721 118.286 53.0166 118.378 52.5125 119.898C52.0439 121.325 53.0734 122.319 54.5431 122.802L55.6578 123.171L56.9713 119.188L56.9784 119.181Z" :fill="color"/>
<path d="M39.8239 116.191C41.6841 112.897 44.6732 112.279 47.1227 113.657C49.6503 115.084 50.3248 118.045 48.6847 120.948C46.9665 123.987 43.9561 124.91 41.4001 123.469C38.7873 121.992 38.2264 119.039 39.8239 116.191ZM47.3499 120.224C48.5072 118.179 48.4291 115.716 46.4837 114.622C44.5241 113.515 42.3941 114.693 41.1658 116.873C40.0937 118.776 39.9659 121.332 42.0249 122.489C44.0697 123.646 46.1642 122.333 47.3499 120.224Z" :fill="color"/>
<path d="M34.5283 118.634C33.8893 118.279 33.0515 117.675 32.306 117.036C31.1487 116.05 30.5878 115.205 30.4671 114.289C30.3606 113.557 30.6162 112.784 31.1629 112.137C32.1072 111.03 33.4278 110.895 34.5638 111.349L34.5922 111.314C34.1378 110.54 34.2585 109.617 34.8052 108.573C35.5294 107.175 36.0193 106.195 36.1187 105.726L37.2334 106.678C37.1695 107.033 36.7435 107.913 36.0832 109.155C35.3448 110.519 35.4229 111.335 36.3743 112.216L37.3896 113.082L40.4852 109.454L41.5644 110.377L34.5212 118.634H34.5283ZM36.7009 113.898L35.5933 112.954C34.436 111.967 33.1651 111.974 32.3486 112.933C31.4256 114.012 31.809 115.155 32.9379 116.142C33.4562 116.589 33.8751 116.852 34.0881 116.951L36.6938 113.898H36.7009Z" :fill="color"/>
<path d="M31.4736 105.634L34.8816 104.335L35.8046 105.478L24.9132 109.518L23.834 108.19L30.0039 98.3421L30.9624 99.5207L29.017 102.616L31.4736 105.641V105.634ZM28.3354 103.532L26.5604 106.365C26.1557 107.011 25.751 107.544 25.3605 108.076L25.3818 108.105C25.9924 107.821 26.6243 107.537 27.2775 107.281L30.4228 106.11L28.3354 103.539V103.532Z" :fill="color"/>
<path d="M20.6333 101.466L22.3302 104.349L21.2865 104.959L17.1543 97.9445L18.198 97.3339L19.902 100.231L28.3368 95.2607L29.0681 96.4961L20.6333 101.466Z" :fill="color"/>
<path d="M19.4757 88.1678L21.0448 92.1438L24.7368 90.6883L22.9902 86.2579L24.0978 85.8177L26.3698 91.5687L16.1387 95.6086L13.959 90.0848L15.0666 89.6446L16.728 93.8478L19.9585 92.5698L18.3894 88.5938L19.4828 88.1607L19.4757 88.1678Z" :fill="color"/>
<path d="M15.8762 79.9318C18.7504 79.9318 21.0805 77.6018 21.0805 74.7275C21.0805 71.8532 18.7504 69.5232 15.8762 69.5232C13.0019 69.5232 10.6719 71.8532 10.6719 74.7275C10.6719 77.6018 13.0019 79.9318 15.8762 79.9318Z" :fill="color"/>
<path d="M126.679 73.3004C129.553 73.3004 131.883 70.9703 131.883 68.0961C131.883 65.2218 129.553 62.8918 126.679 62.8918C123.805 62.8918 121.475 65.2218 121.475 68.0961C121.475 70.9703 123.805 73.3004 126.679 73.3004Z" :fill="color"/>
<!-- <path d="M63.2629 79.068L75.2416 75.8584C69.4498 72.4957 63.6647 69.1313 57.9239 65.7983C58.5295 64.7375 59.0758 63.7901 59.6228 62.8317C65.3432 66.1557 71.1274 69.5167 77.023 72.9454L73.7521 60.738L76.966 59.8769L81.7002 77.5449L64.1171 82.2562L63.2629 79.068Z" :fill="color"/> -->
</g>
<defs>
<clipPath id="clip0_1428_160">
<rect width="142" height="142" fill="white"/>
</clipPath>
</defs>
</svg>

</template>
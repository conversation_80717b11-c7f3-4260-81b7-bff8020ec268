<template>
  <div class="rounded-full">
    <svg class="rounded-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 53.48 53.48">
      <defs></defs>
      <g id="FacebookLayer_2" data-name="FacebookLayer 2">
        <g id="FacebookLayer_1" data-name="FacebookLayer 1">
          <circle id="Ellipse_85" data-name="Ellipse 85" class="facebook-1" cx="26.74" cy="26.74" r="26.74" />
          <path id="Path_2049" data-name="Path 2049" class="facebook-2"
            d="M33.46,27.78H28.7V45.26H21.46V27.78H18V21.64h3.44v-4a6.77,6.77,0,0,1,6.21-7.29,6.61,6.61,0,0,1,1.09,0l5.34,0v6H30.22a1.45,1.45,0,0,0-1.54,1.38h0a1.31,1.31,0,0,0,0,.28v3.61h5.4Z" />
        </g>
      </g>
    </svg>
  </div>
</template>
<style lang="scss" scoped>
@import "~/assets/scss/icon/facebook.scss";
</style>

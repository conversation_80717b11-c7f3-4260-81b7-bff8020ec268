<template>
  <div class="rounded-full">
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 60 60">
      <defs>
        <linearGradient id="linear-gradient" x1="-1963.86" y1="412.61" x2="-1964.57" y2="411.91"
          gradientTransform="matrix(60, 0, 0, -60, 117882.89, 24765.68)" gradientUnits="userSpaceOnUse">
          <stop offset="0" stop-color="#5a3f92" />
          <stop offset="0.39" stop-color="#c21976" />
          <stop offset="0.7" stop-color="#c84c4d" />
          <stop offset="1" stop-color="#e09a3b" />
        </linearGradient>
      </defs>
      <g id="InstagramLayer_2" data-name="InstagramLayer 2">
        <g id="InstagramLayer_1" data-name="InstagramLayer 1">
          <g id="instagram">
            <circle id="Ellipse_86" data-name="Ellipse 86" class="instagram-1" cx="30" cy="30" r="30" />
            <g id="instagram-2">
              <path id="Path_2054" data-name="Path 2054" class="instagram-2"
                d="M38.08,10H21.92A11.94,11.94,0,0,0,10,21.92V38.08A11.94,11.94,0,0,0,21.92,50H38.08A11.94,11.94,0,0,0,50,38.08V21.92A11.94,11.94,0,0,0,38.08,10ZM46,38.08a7.9,7.9,0,0,1-7.9,7.9H21.92a7.9,7.9,0,0,1-7.9-7.9V21.92a7.9,7.9,0,0,1,7.9-7.9H38.08a7.9,7.9,0,0,1,7.9,7.9Z" />
              <path id="Path_2055" data-name="Path 2055" class="instagram-2"
                d="M30,19.65A10.35,10.35,0,1,0,40.35,30,10.35,10.35,0,0,0,30,19.65Zm0,16.67A6.32,6.32,0,1,1,36.32,30h0A6.32,6.32,0,0,1,30,36.32Z" />
              <circle id="Ellipse_87" data-name="Ellipse 87" class="instagram-2" cx="40.36" cy="19.73" r="2.48" />
            </g>
          </g>
        </g>
      </g>
    </svg>
  </div>
</template>
<style lang="scss" scoped>
@import "~/assets/scss/icon/instagram.scss";
</style>

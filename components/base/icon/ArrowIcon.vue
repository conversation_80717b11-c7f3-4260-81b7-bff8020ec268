<script setup>
const props = defineProps({
  color: {
    type: String,
    default: 'white',
  },
});
</script>

<template>
  <svg width="142" height="142" viewBox="0 0 142 142" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1428_160)">
<path d="M63.2629 79.068L75.2416 75.8584C69.4498 72.4957 63.6647 69.1313 57.9239 65.7983C58.5295 64.7375 59.0758 63.7901 59.6228 62.8317C65.3432 66.1557 71.1274 69.5167 77.023 72.9454L73.7521 60.738L76.966 59.8769L81.7002 77.5449L64.1171 82.2562L63.2629 79.068Z" :fill="color"/>
</g>
<defs>
<clipPath id="clip0_1428_160">
<rect width="142" height="142" fill="white"/>
</clipPath>
</defs>
</svg>

</template>
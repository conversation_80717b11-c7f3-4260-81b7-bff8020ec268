<script setup>
defineProps({
  color: {
    type: String,
    default: "#F4F4F5",
  },
});
</script>

<template>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    :fill="color"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Layer_1" clip-path="url(#clip0_28_328)">
      <path
        id="Vector"
        d="M19.9998 3.03411V15.642C19.9796 15.7179 19.9595 15.7918 19.9411 15.8676C19.7578 16.5795 19.1749 17.0121 18.3849 17.0121C16.002 17.0121 13.6209 17.0121 11.238 17.0121H10.9869C11.0968 17.2802 11.2728 17.4466 11.4873 17.5575C11.7586 17.6981 12.0427 17.809 12.325 17.9292C12.8804 18.164 13.4504 18.3692 13.9912 18.6373C14.3798 18.8296 14.6437 19.1735 14.7775 19.6006C14.8637 19.8761 14.7775 20 14.4879 20C11.4946 20 8.49946 20 5.50615 20C5.21653 20 5.13771 19.8798 5.2257 19.5951C5.4035 19.0256 5.82143 18.678 6.33284 18.445C6.73977 18.2601 7.17236 18.1307 7.58662 17.9588C7.94589 17.809 8.30333 17.6518 8.64243 17.4614C8.79457 17.3764 8.89905 17.2026 9.0237 17.0694C9.01087 17.0491 8.99987 17.0306 8.98704 17.0103C8.91189 17.0103 8.83673 17.0103 8.76158 17.0103C6.38599 17.0103 4.01041 17.0103 1.63483 17.0103C0.628502 17.0103 0.0034445 16.3761 0.0034445 15.3629C0.0034445 12.3491 0.0034445 9.33715 0.0034445 6.32338C0.0034445 5.27318 -0.00205454 4.22298 0.00527751 3.17278C0.0107765 2.42581 0.527686 1.77129 1.26272 1.69733C1.89145 1.63262 2.533 1.68439 3.18006 1.68439V3.4945C3.15439 3.50559 3.1379 3.51854 3.11957 3.51854C2.86661 3.52038 2.61182 3.52223 2.35887 3.52408C2.00693 3.52408 1.95927 3.57215 1.95927 3.91606C1.95927 7.52704 1.95927 11.138 1.95927 14.7472C1.95927 14.8063 1.93911 14.8784 1.9666 14.921C2.01793 15.0005 2.09308 15.1206 2.16273 15.1225C2.65398 15.1391 3.14706 15.1317 3.66214 15.1317V14.8618C3.66214 10.3984 3.66214 5.93325 3.66214 1.46991C3.66214 1.37191 3.66214 1.27207 3.66764 1.17408C3.70063 0.636036 4.0654 0.188592 4.58414 0.0536193C4.65563 0.0351299 4.73078 0.0184894 4.80594 0C8.30882 0 11.8099 0 15.3128 0C15.4869 0.0813534 15.6794 0.138671 15.8352 0.249607C16.2164 0.521402 16.3466 0.915226 16.3466 1.37931C16.3411 5.87039 16.3429 10.3615 16.3429 14.8525C16.3429 14.9431 16.3429 15.0319 16.3429 15.1391C16.8122 15.1391 17.2539 15.1391 17.6957 15.1391C17.9853 15.1391 18.0458 15.0763 18.0458 14.7767C18.0458 11.1454 18.0458 7.51595 18.0458 3.88463C18.0458 3.59249 17.9798 3.52593 17.6883 3.52408C17.4042 3.52408 17.1183 3.52408 16.8232 3.52408V1.67699C16.8507 1.66775 16.869 1.6585 16.8873 1.6585C17.4281 1.6585 17.9688 1.64926 18.5077 1.6585C19.2446 1.67144 19.8311 2.18915 19.965 2.92318C19.9723 2.96016 19.9888 2.99713 20.0016 3.03226L19.9998 3.03411ZM8.06687 3.67385C8.03937 3.70528 8.03571 3.71083 8.03021 3.71452C7.99171 3.74041 7.95322 3.76444 7.91473 3.78848C7.06421 4.34686 6.51247 5.12342 6.26685 6.11445C6.23019 6.26236 6.18436 6.31414 6.02856 6.31598C5.67479 6.31968 5.56481 6.44171 5.56297 6.79486C5.56297 7.05741 5.55931 7.31996 5.57764 7.58251C5.59597 7.8617 5.68945 7.9486 5.97357 7.95969C6.25218 7.97079 6.53263 7.96709 6.81309 7.96339C7.07154 7.95969 7.21085 7.82102 7.21818 7.56032C7.22368 7.35139 7.22185 7.14061 7.21818 6.92983C7.20718 6.43616 7.18152 6.40473 6.7251 6.28825C6.7251 6.26421 6.7196 6.23833 6.7251 6.21429C7.05871 4.9829 7.83224 4.18046 9.03836 3.79588C9.13918 3.76444 9.18134 3.79588 9.23816 3.88278C9.55527 4.38754 10.2756 4.39678 10.5964 3.90127C10.6661 3.79218 10.7211 3.76814 10.8439 3.80697C11.5514 4.02699 12.127 4.43561 12.5669 5.03282C12.8437 5.40815 13.0417 5.82417 13.1333 6.29565C13.0472 6.31229 12.9775 6.32523 12.9097 6.34002C12.7429 6.377 12.6366 6.48054 12.6347 6.65064C12.6292 6.97791 12.6256 7.30702 12.6476 7.63428C12.6622 7.86355 12.7612 7.94675 12.9885 7.95599C13.2873 7.96894 13.5879 7.96709 13.8867 7.96154C14.125 7.95784 14.2588 7.82102 14.2643 7.57881C14.2716 7.3292 14.2716 7.0796 14.2643 6.82999C14.2533 6.41583 14.18 6.33632 13.7712 6.31783C13.6392 6.31229 13.6026 6.26236 13.5751 6.14033C13.3386 5.13636 12.785 4.35611 11.9309 3.78848C11.885 3.75705 11.8392 3.72562 11.7641 3.67385H13.1406C13.1553 3.70898 13.1645 3.74041 13.1773 3.76999C13.3386 4.14163 13.6851 4.32098 14.0865 4.24332C14.4641 4.16936 14.717 3.86059 14.7244 3.45937C14.7317 3.0637 14.4989 2.73089 14.1506 2.64399C13.7309 2.5386 13.3606 2.69761 13.192 3.07849C13.1425 3.18942 13.0765 3.19497 12.9812 3.19312C12.2791 3.19312 11.5753 3.18942 10.8732 3.19312C10.7559 3.19312 10.6917 3.16909 10.6368 3.05075C10.4974 2.74753 10.2372 2.60516 9.91271 2.60516C9.58827 2.60516 9.33165 2.74938 9.19234 3.05445C9.14285 3.16169 9.08785 3.19497 8.97604 3.19312C8.33265 3.18758 7.68743 3.18758 7.04405 3.19312C6.91207 3.19312 6.84425 3.15984 6.78376 3.03041C6.61879 2.67727 6.1917 2.52565 5.79576 2.65508C5.42916 2.77526 5.2147 3.15429 5.27519 3.58325C5.32835 3.96043 5.62713 4.23408 6.00656 4.25257C6.43915 4.27475 6.7251 4.07692 6.86074 3.67015H8.06687V3.67385ZM11.271 10.6351C11.3498 10.4595 11.381 10.306 11.4708 10.208C11.6797 9.98059 11.9052 9.76241 12.149 9.57197C12.3543 9.41111 12.3928 9.35195 12.27 9.12268C11.5972 7.8654 10.9245 6.60627 10.2445 5.35269C10.195 5.26209 10.0813 5.16224 9.99153 5.1567C9.91638 5.15115 9.80456 5.26209 9.7569 5.34899C9.07319 6.60072 8.39864 7.858 7.72226 9.11528C7.60861 9.32606 7.62511 9.40002 7.81574 9.54608C7.98071 9.67366 8.15668 9.7883 8.31249 9.92512C8.51779 10.1045 8.65343 10.3319 8.69376 10.6296C8.57644 10.6296 8.47379 10.6259 8.37115 10.6296C8.13102 10.6425 8.07603 10.6998 8.07603 10.9439C8.07603 11.3839 8.07237 11.824 8.07603 12.264C8.07603 12.4637 8.15302 12.5395 8.34915 12.5395C9.44896 12.5414 10.5488 12.5414 11.6486 12.5395C11.8447 12.5395 11.918 12.4619 11.9199 12.2622C11.9235 11.8092 11.9199 11.3562 11.9217 10.9032C11.9217 10.7294 11.8355 10.6481 11.6761 10.6388C11.5606 10.6314 11.4433 10.6388 11.2691 10.6388L11.271 10.6351ZM3.56316 15.8029C3.40735 15.8029 3.25154 15.7992 3.09574 15.8029C2.95093 15.8066 2.85928 15.8843 2.85745 16.0322C2.85561 16.1893 2.9436 16.2725 3.10307 16.2725C3.40185 16.2725 3.70063 16.2725 3.99941 16.2725C4.15522 16.2725 4.2542 16.2023 4.25603 16.0433C4.2597 15.8732 4.15339 15.7992 3.99208 15.7992C3.84911 15.7992 3.70613 15.7992 3.56316 15.7992V15.8029ZM16.55 16.2725C16.7059 16.2725 16.8617 16.2725 17.0175 16.2725C17.1696 16.2725 17.2484 16.1801 17.2484 16.0377C17.2484 15.8935 17.1641 15.8048 17.0156 15.8029C16.704 15.7992 16.3924 15.7974 16.0808 15.8029C15.936 15.8048 15.8517 15.888 15.8517 16.0396C15.8517 16.1893 15.9342 16.2688 16.0826 16.2725C16.2384 16.2762 16.3942 16.2725 16.55 16.2725ZM5.42733 16.2744C5.58314 16.2744 5.73894 16.2762 5.89291 16.2744C6.03956 16.2707 6.12571 16.1875 6.12754 16.0433C6.12754 15.8972 6.03955 15.8103 5.89658 15.8066C5.58497 15.8011 5.27519 15.8011 4.96358 15.8066C4.81877 15.8084 4.73078 15.8935 4.73078 16.0396C4.73078 16.1967 4.82243 16.2762 4.98007 16.2762C5.12855 16.2762 5.27885 16.2762 5.42733 16.2762V16.2744Z"
      />
      <path
        id="Vector_2"
        d="M6.04688 6.79301H6.73425V7.48082H6.04688V6.79301Z"
      />
      <path
        id="Vector_3"
        d="M13.1133 6.79116H13.7823V7.48082H13.1133V6.79116Z"
      />
      <path
        id="Vector_4"
        d="M9.92005 3.78293C9.72025 3.78293 9.57178 3.63502 9.57178 3.43718C9.57178 3.23934 9.72025 3.09143 9.92005 3.08958C10.118 3.08958 10.2665 3.2375 10.2665 3.43718C10.2665 3.63687 10.118 3.78478 9.92005 3.78293Z"
      />
      <path
        id="Vector_5"
        d="M6.04359 3.78293C5.84563 3.76999 5.72098 3.61653 5.73381 3.4076C5.74665 3.20791 5.90428 3.07479 6.10408 3.09143C6.30388 3.10992 6.43036 3.26153 6.4157 3.46676C6.40103 3.66645 6.24889 3.79588 6.04359 3.78108V3.78293Z"
      />
      <path
        id="Vector_6"
        d="M13.9145 3.78294C13.7128 3.78109 13.5772 3.63502 13.5827 3.42609C13.5863 3.2301 13.7238 3.09143 13.9163 3.08958C14.1161 3.08773 14.2609 3.2338 14.2591 3.43718C14.2591 3.64057 14.1143 3.78478 13.9145 3.78294Z"
      />
      <path
        id="Vector_7"
        d="M10.2721 6.38255C10.778 7.32366 11.2839 8.26477 11.7972 9.21697C11.2968 9.56272 10.921 9.99168 10.811 10.6296C10.5104 10.6296 10.2135 10.6296 9.9165 10.6296H9.56457C9.22912 10.6296 9.23096 10.6296 9.11181 10.3208C8.93401 9.85856 8.6224 9.50726 8.20264 9.21697C8.70855 8.26847 9.21446 7.32181 9.71854 6.377C9.7332 6.3807 9.74603 6.38439 9.7607 6.38994C9.7607 6.67653 9.7662 6.96126 9.7552 7.24785C9.7552 7.29592 9.69838 7.35694 9.65072 7.38652C9.35927 7.57511 9.23462 7.89683 9.32811 8.23888C9.41426 8.5495 9.66721 8.73995 9.99349 8.7381C10.3179 8.73625 10.5764 8.54026 10.6589 8.23334C10.7505 7.88758 10.6259 7.57511 10.3308 7.38282C10.2831 7.35139 10.2336 7.27743 10.2318 7.22197C10.2226 6.94647 10.2281 6.67098 10.2281 6.39549C10.2409 6.38994 10.2556 6.38439 10.2684 6.37885L10.2721 6.38255Z"
      />
      <path
        id="Vector_8"
        d="M8.55469 12.0533V11.1158H11.438V12.0533H8.55469Z"
      />
      <path
        id="Vector_9"
        d="M10.2338 8.01331C10.2246 8.15753 10.1531 8.25368 10.0083 8.26107C9.8672 8.26662 9.77555 8.19081 9.76455 8.04105C9.75355 7.89128 9.84703 7.78404 9.99184 7.78589C10.1403 7.78589 10.2136 7.87279 10.2338 8.01516V8.01331Z"
      />
    </g>
    <defs>
      <clipPath id="clip0_28_328">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>

# Deploy to DigitalOcean
name: Deploy to DigitalOcean

# Controls when the action will run.
on:
  workflow_dispatch:
  push:
    branches: [dev]

env:
  SERVER_IP: **************
  USERNAME: devxhub
  WORK_DIR: /home/<USER>/dev
  APP_NAME: dev
  NODE_VERSION: 20.x

  # Environment-specific URLs and keys
  NUXT_PUBLIC_SITE_URL: "https://dev.devxhub.com"
  NUXT_PUBLIC_APP_NAME: "Devxhub"
  NUXT_API_URL: "https://admin-dev.devxhub.com"
  NUXT_PUBLIC_API_URL: "https://admin-dev.devxhub.com"
  NUXT_PUBLIC_GTAG_ID: "GTM-KWQHD5N"
  NUXT_PUBLIC_REDIS_URL: "redis://default:JE86vlSE4xLGIJNDGd66LoAvdfNzvJV7rzUvjik3@127.0.0.1:6379/1"
  NUXT_PUBLIC_SENTRY_DSN: "https://<EMAIL>/4506138913275904"
  NUXT_PUBLIC_WORKFLOW: "dev"
  META_KEYWORDS: "Software Development & Solution, Team Extension Service, AI/ML Development, Web and Mobile App Development, DevOps and Cloud Solutions, Custom Software Development, Enterprise Software Development, SaaS, MVP, and End-to-End Software Development."
  NUXT_PUBLIC_RECAPTCHA_API_KEY: "6LfZELUqAAAAACJCXWvf5vWVuaHhIH93vtig2KPA"

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build-deploy:
    runs-on: ubuntu-latest
    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Checkout 🛎
        uses: actions/checkout@v4

      - name: Setup Node.js environment 🏗
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          check-latest: true
          cache: "yarn"

      - name: Create .node-version file
        run: echo "${{ env.NODE_VERSION }}" > .node-version

      - name: Install dependencies 👨🏻‍💻
        run: yarn install --frozen-lockfile --non-interactive --production=false

      - name: Generate .env file
        uses: SpicyPizza/create-envfile@v2
        with:
          envkey_GIT_COMMIT_SHA: ${{ github.sha }}
          envkey_NUXT_PUBLIC_SITE_URL: ${{ env.NUXT_PUBLIC_SITE_URL }}
          envkey_NUXT_PUBLIC_APP_NAME: "${{ env.NUXT_PUBLIC_APP_NAME }}"
          envkey_NUXT_API_URL: "${{ env.NUXT_API_URL }}"
          envkey_NUXT_PUBLIC_API_URL: "${{ env.NUXT_PUBLIC_API_URL }}"
          envkey_NUXT_PUBLIC_RECAPTCHA_API_KEY: "${{env.NUXT_PUBLIC_RECAPTCHA_API_KEY}}"
          envkey_NUXT_PUBLIC_GTAG_ID: "${{ env.NUXT_PUBLIC_GTAG_ID }}"
          envkey_NUXT_PUBLIC_REDIS_URL: "${{ env.NUXT_PUBLIC_REDIS_URL }}"
          envkey_NUXT_PUBLIC_SENTRY_DSN: "${{ env.NUXT_PUBLIC_SENTRY_DSN }}"
          envkey_NUXT_PUBLIC_WORKFLOW: ${{ env.NUXT_PUBLIC_WORKFLOW }}
          envkey_META_KEYWORDS: ${{ env.META_KEYWORDS }}

      - name: Build application
        run: yarn build

      - name: Set up SSH key
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_KEY }}

      - name: Rsync project files
        run: |
          rsync -avz --delete .output/ ${{ env.USERNAME }}@${{ env.SERVER_IP }}:${{ env.WORK_DIR }}/.output
          rsync -avz --delete .env ${{ env.USERNAME }}@${{ env.SERVER_IP }}:${{ env.WORK_DIR }}/.env
          rsync -avz --delete .node-version ${{ env.USERNAME }}@${{ env.SERVER_IP }}:${{ env.WORK_DIR }}/.node-version
          rsync -avz --delete ecosystem.config.cjs ${{ env.USERNAME }}@${{ env.SERVER_IP }}:${{ env.WORK_DIR }}/ecosystem.config.cjs
        env:
          RSYNC_RSH: "ssh -o StrictHostKeyChecking=no"

  run:
    needs: build-deploy
    runs-on: ubuntu-latest
    steps:
      - name: Run the application using PM2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SERVER_IP }}
          username: ${{ env.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            export PATH="$HOME/.local/share/fnm:$PATH"
            eval "$(fnm env)"
            source ~/.bashrc
            cd ${{ env.WORK_DIR }}
            if pm2 show ${{ env.APP_NAME }} > /dev/null; then
              pm2 restart ${{ env.APP_NAME }} --update-env
            else
              pm2 start ecosystem.config.cjs --only ${{ env.APP_NAME }}
            fi

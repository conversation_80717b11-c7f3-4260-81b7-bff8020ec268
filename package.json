{"name": "devx<PERSON>b", "version": "0.0.1", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@nuxt/devtools": "^1.3.1", "@nuxt/image-edge": "1.3.0-28493895.369b747", "@nuxtjs/google-fonts": "^3.0.0-1", "@nuxtjs/tailwindcss": "^6.12.0", "@vueuse/core": "^10.9.0", "@vueuse/nuxt": "^10.9.0", "aos": "^2.3.4", "sass": "^1.62.1", "vue3-carousel": "^0.3.1"}, "dependencies": {"@dargmuesli/nuxt-cookie-control": "^8.5.1", "@nuxtjs/seo": "^2.0.0-rc.10", "@pinia/nuxt": "^0.5.1", "@sentry/vue": "^7.76.0", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "date-fns": "^3.6.0", "lenis": "^1.1.20", "maska": "^2.1.9", "nuxt": "^3.11.2", "nuxt-gtag": "^3.0.2", "pinia": "^2.0.33", "vue": "^3.4.21", "vue-router": "^4.3.0", "vue-tel-input": "^8.3.1", "vue-toastification": "2.0.0-rc.5"}, "packageManager": "yarn@1.22.21"}
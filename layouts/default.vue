<script setup>
import <PERSON><PERSON> from "lenis";
const route = useRoute();
const config = useRuntimeConfig();
const showCookie = ref(false);
useHead(() => ({
  htmlAttrs: {
    prefix: "og: https://ogp.me/ns#",
  },
  link: [
    {
      key: "canonical",
      rel: "canonical",
      href: config.public.siteUrl + route.path,
    },
  ],
  script: [
    {
      type: "application/ld+json",
      children: JSON.stringify({
        "@context": "http://schema.org",
        "@type": "Organization",
        name: "PROMPTEQUATION LDA",
        url: "https://www.devxhub.com/",
        sameAs: [
          "https://www.facebook.com/devxhubcom/",
          "https://www.linkedin.com/company/devxhubcom/",
          "https://www.instagram.com/devxhubcom/",
          "https://twitter.com/devxhub",
          "https://www.pinterest.com/devxhub_com",
          "http://youtube.com/@devxhub",
        ],
        logo: "https://www.devxhub.com/new-logo.png",
        image: "https://www.devxhub.com/new-logo.png",
        description:
          "Devxhub is one of the best Software, Web, and Mobile Application Development Outsourcing Companies in Asia, Bangladesh leading globally. We help to transform your idea into a digital business.",
        foundingDate: "2019",
        address: [
          {
            "@type": "PostalAddress",
            streetAddress: "158/27, Boalia, Kazla",
            addressLocality: "Rajshahi",
            addressRegion: "Rajshahi",
            postalCode: "6204",
            addressCountry: {
              "@type": "Country",
              name: "Bangladesh",
            },
          },
          {
            "@type": "PostalAddress",
            streetAddress: "903 1st Street North #1061",
            addressLocality: "Hopkins",
            addressRegion: "MN",
            postalCode: "55343",
            addressCountry: {
              "@type": "Country",
              name: "United States",
            },
          },
          {
            "@type": "PostalAddress",
            streetAddress: "Viulutie 1 A 1",
            addressLocality: "Helsinki",
            addressRegion: "Uusimaa",
            postalCode: "00420",
            addressCountry: {
              "@type": "Country",
              name: "Finland",
            },
          },
        ],
        contactPoint: [
          {
            "@type": "ContactPoint",
            telephone: "+880 1912 027073",
            contactType: "Client Service Bangladesh",
          },
          {
            "@type": "ContactPoint",
            telephone: "****** 300-7711",
            contactType: "Client Service US",
          },
          {
            "@type": "ContactPoint",
            telephone: "+358 402545717",
            contactType: "Client Service Finland",
          },
        ],
        founder: {
          "@type": "Person",
          name: "Hadisur Rahman",
        },
        foundingLocation: {
          "@type": "Place",
          name: "Rajshahi, Bangladesh",
        },
      }),
    },
  ],
}));
useSchemaOrg([
  defineWebPage({
    "@context": "http://schema.org",
    "@type": "WebPage",
    "@id": `${config.public.siteUrl}/#webpage`,
    url: `${config.public.siteUrl}`,
    inLanguage: "en-US",
    name: "Welcome to Devxhub",
    dateModified: "2024-05-20T19:01:00",
    datePublished: "2024-01-01T12:00:00",
    description:
      "Devxhub is one of the best Software, Web, and Mobile Application Development Outsourcing Companies in Asia, Bangladesh leading globally. We help to transform your idea into a digital business.",
    isPartOf: {
      "@type": "WebSite",
      "@id": `${config.public.siteUrl}/#website`,
      url: `${config.public.siteUrl}`,
      name: "Devxhub Website",
      inLanguage: "en-US",
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      "@id": `${config.public.siteUrl}/#breadcrumb`,
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          item: {
            "@type": "WebPage",
            "@id": `${config.public.siteUrl}/blog#webpage`,
            url: `${config.public.siteUrl}/blog`,
            name: "Devxhub Blog",
          },
        },
        {
          "@type": "ListItem",
          position: 2,
          item: {
            "@type": "WebPage",
            "@id": `${config.public.siteUrl}`,
            url: `${config.public.siteUrl}`,
            name: "Welcome to Devxhub",
          },
        },
      ],
    },
    primaryImageOfPage: {
      "@type": "ImageObject",
      "@id": `${config.public.siteUrl}/new-logo.png#primaryimage`,
      url: `${config.public.siteUrl}/new-logo.png`,
      width: "100%",
      height: "100%",
      caption: "Welcome to Devxhub",
    },
    author: {
      "@type": "Person",
      "@id": `${config.public.siteUrl}/p/`,
      url: `${config.public.siteUrl}/p/`,
    },
  }),
]);

import { useHeaderStore } from "~/stores/header";
// pinia
const { setMobileMenu } = useHeaderStore();
const showChatOptions = ref(false);
let windowWidth = ref("");

const toggleChatOption = () => {
  showChatOptions.value = !showChatOptions.value;
  if (showChatOptions.value) {
    // facebookPlugin();
  }
};
onMounted(() => {
  if (route?.query?.cookie === "true") {
    showCookie.value = true;
  } else {
    showCookie.value = false;
  }
  // facebookPlugin();
  windowWidth.value = window.innerWidth;

  window.addEventListener("resize", function (e) {
    windowWidth.value = window.innerWidth;
    // facebookPlugin();
  });

  // setTimeout(() => {
  // facebookPlugin();
  // }, 500);

  const lenis = new Lenis({
    smooth: true, // Enable smooth scrolling
    lerp: 0.1, // Adjust for smooth effect (0 = instant, 1 = no smoothing)
    duration: 2, // Controls overall animation smoothness
    gestureOrientation: "vertical", // Ensures touch gestures work correctly
    touchMultiplier: 1.5, // Increase sensitivity for touch screens
    wheelMultiplier: 1, // Default scroll speed
    smoothTouch: true,
  });
  function raf(time) {
    lenis.raf(time);
    requestAnimationFrame(raf);
  }
  requestAnimationFrame(raf);
});
</script>

<template>
  <div id="mainApp" class="bg-[#1A1139]">
    <Header
      role="banner"
      ref="header"
      class="z-10 fixed top-0 left-0 right-0 w-full"
    />
    <div @click="setMobileMenu(false)">
      <div role="main">
        <slot />
      </div>
      <Footer role="contentinfo" />

      <div v-if="windowWidth > 600 && false" class="relative">
        <!-- Facebook Chat Plugin -->
        <div class="!fixed !bottom-[20px] !right-[24px] w-[60px] h-[60px]">
          <div id="fb-root"></div>
          <div id="fb-customer-chat" class="fb-customerchat"></div>
          <a href="http://m.me/106014354867607" target="_blank">
            <BaseIconFbMessenger class="w-[60px] h-[60px] cursor-pointer" />
          </a>
        </div>

        <!-- WhatsApp US -->
        <NuxtLink
          to="https://api.whatsapp.com/send?phone=8801326506464"
          target="_blank"
          aria-label="whatsapp"
        >
          <BaseIconWhatsApp
            class="fixed bottom-[100px] right-[24px] w-[60px] h-[60px] z-10"
          />
        </NuxtLink>
      </div>

      <div v-else-if="false" class="fixed w-full !bottom-0 z-10">
        <!-- Facebook Chat Plugin -->
        <div
          v-show="showChatOptions"
          class="!fixed !bottom-[85px] !right-[24px] w-[60px] h-[60px] z-[100]"
        >
          <div id="fb-root"></div>
          <div id="fb-customer-chat" class="fb-customerchat"></div>
          <a href="http://m.me/106014354867607" target="_blank">
            <BaseIconFbMessenger class="w-[60px] h-[60px] cursor-pointer" />
          </a>
        </div>

        <!-- WhatsApp US -->
        <NuxtLink
          to="https://api.whatsapp.com/send?phone=8801326506464"
          target="_blank"
          aria-label="whatsapp"
        >
          <BaseIconWhatsApp
            v-if="showChatOptions"
            class="absolute bottom-[160px] right-[24px] w-[60px] h-[60px] z-10"
          />
        </NuxtLink>

        <div class="relative">
          <!-- Discussion Icon -->
          <div
            v-if="!showChatOptions"
            @click="toggleChatOption"
            class="absolute bottom-[0px] md:bottom-[10px] right-[24px] w-[60px] h-[60px] z-10"
          >
            <BaseIconDiscussion
              class="w-[40px] md:w-[60px] h-[40px] md:h-[60px] absolute right-0"
            />
          </div>

          <!-- Cross Icon -->
          <div
            v-if="showChatOptions"
            @click="toggleChatOption"
            class="absolute bottom-[10px] right-[24px] w-[60px] h-[60px] flex justify-center items-center z-10 bg-yellow-400 rounded-full"
          >
            <ClientOnly>
              <fa class="w-6 !h-6" :icon="['fa-solid', 'times']" />
            </ClientOnly>
          </div>
        </div>
      </div>
    </div>
    <CookieControl v-if="showCookie">
      <template> </template>
      <template #bar>
        <h2>Cookies</h2>
        <p class="mb-0 text-xl text-white">
          Devxhub uses information collected through cookies or in other forms
          to improve the experience on our site and pages and analyze how it is
          used. We do not sell your personal information. You can find out more
          in our
          <nuxt-link to="/privacy-policy" class="text-blue-600 underline"
            >privacy policy</nuxt-link
          >
        </p>
      </template>
    </CookieControl>
  </div>
</template>

<style>
@import "~/assets/scss/default.scss";
.yellowBlackHighLight {
  @apply px-1;
  background: yellow;
  color: black;
  border: 2px solid yellow;
  border-radius: 5px;
}
.blueWhiteHighLight {
  @apply px-1;
  background: rgb(27, 20, 86);
  color: white;
  border: 2px solid rgb(27, 20, 86);
  border-radius: 5px;
}
.lightBlueWhiteHighLight {
  @apply px-1;
  background: #3ecb39;
  color: black;
  border: 2px solid #3ecb39;
  border-radius: 5px;
}
.redBlackHighLight {
  @apply px-1;
  background: rgb(228, 61, 61);
  color: black;
  border: 2px solid rgb(228, 61, 61);
  border-radius: 5px;
}
.technologyStackHighLight {
  @apply px-1;
  background: #fdb21d;
  color: black;
  border: 2px solid #fdb21d;
  border-radius: 5px;
}
iframe {
  bottom: 20px !important;
}
.fb_dialog_content iframe {
  bottom: 20px !important;
}

@media (max-width: 600px) {
  iframe {
    bottom: 84px !important;
  }
  .fb_dialog_content iframe {
    bottom: 84px !important;
  }
}

.blog-content p,
.blog-content span,
.blog-content a {
  font-family: "Aeonik", Georgia, "Times New Roman", Times, serif !important;
  @apply md:leading-8 leading-7;
  color: #242424 !important;
}
.blog-content a {
  text-decoration: underline;
}
.blog-content p strong {
  font-weight: 600;
}
.blog-content p img {
  margin-left: auto;
  margin-right: auto;
}
.blog-content h1,
.blog-content h1 strong,
.blog-content h1 a,
.blog-content h1 a strong {
  font-weight: 600;
  @apply md:leading-[30px] leading-6 md:!text-2xl !text-xl;
  color: #242424 !important;
}
.blog-content h2,
.blog-content h2 span,
.blog-content h2 strong,
.blog-content h2 a,
.blog-content h2 a strong .blog-content h3,
.blog-content h3 span,
.blog-content h3 strong,
.blog-content h3 a,
.blog-content h3 a strong {
  font-weight: 600;
  @apply md:leading-6 leading-5 md:!text-xl !text-base;
  color: #242424 !important;
}
.blog-content .ql-syntax,
.blog-content pre {
  background: #f2f2f2;
  padding: 20px;
  overflow-x: auto;
  color: #242424;
  font-weight: 400;
  font-size: 16px;
  line-height: 18.88px;
  word-break: break-word;
  word-wrap: break-word;
  letter-spacing: -0.022em;
  white-space: pre-wrap;
  margin-top: 56px !important;
  font-family: "Source Code Pro", "Courier Prime", monospace;
}
.hero_section_carousal_aeonik {
  /* font-family: "Aeonik", sans-serif; */
  font-family: "Roboto", sans-serif;
}
.hero_section_carousal .carousel__viewport {
  height: 94% !important;
}
.industry-carousel .carousel__viewport {
  overflow: visible !important;
}
.hero_section_carousal .carousel__track {
  height: 100%;
}
.hero_section_carousal .carousel__pagination-button {
  padding: 0px !important;
  padding-left: 40px !important;
}
.hero_section_carousal
  .carousel__pagination-button[aria-label="Navigate to slide 1"] {
  padding: 0px !important;
}
.hero_section_carousal .carousel__pagination-button::after {
  background-color: #999999 !important;
  width: 40px !important;
  height: 40px !important;
  @apply !rounded-full;
}
.hero_section_carousal .carousel__pagination-button--active::after {
  background-color: #ffd700 !important;
}
.vti__dropdown {
  padding-left: 0px;
  padding-top: 0px;
  padding-bottom: 0px;
}
.vti__dropdown {
  display: none;
}
.vti__input {
  padding-left: 0px;
}
.cookieControl__BarContainer {
  box-shadow: rgba(0, 0, 0, 0.25) 14px 0px 28px,
    rgba(0, 0, 0, 0.22) 10px 0px 10px;
  @apply border-t-[0.5px] border-[#d9d9d9] border-opacity-30 flex-col space-y-4 justify-center items-center;
}
.cookieControl__BarContainer > div:nth-child(1) {
  @apply w-full;
  p {
    @apply w-full max-w-full;
  }
}
.cookieControl__ModalButtons {
  justify-content: center;
}
.cookieControl__BarButtons button,
.cookieControl__ModalButtons button,
.cookieControl__ModalClose {
  @apply rounded-full h-10 min-w-32 whitespace-nowrap;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.cookieControl__ModalContent {
  overflow-y: auto;
}
.cookieControl__ModalContent input + button:hover {
  background-color: #333;
}
.cookieControl__ControlButton {
  display: none !important;
  background-color: #e4801d;
  left: 20px;
  svg {
    color: #fff;
  }
}
.cookieControl__ControlButton:hover {
  background-color: #fff;
  left: 20px;
  svg {
    color: #e4801d;
  }
}

/* /assets/css/google-translate.css */

#google_translate_element {
  position: relative;
  /* top: 2rem;
  right: 1rem; */
  z-index: 9999;
  width: 100px;
}
.goog-te-gadget-simple {
  width: 100% !important;
  border: none !important;
  height: 40px !important;
  justify-content: center;
  align-content: center;
  border-radius: 9999px;
  padding-inline: 6px;
}
/* Optional: Reduce branding, not fully removable per Google TOS */
.goog-logo-link,
.goog-te-gadget span {
  /* display: none !important; */
}

.goog-te-gadget {
  /* color: transparent !important; */
}
.VIpgJd-ZVi9od-ORHb-OEVmcd {
  visibility: hidden !important;
}
.goog-te-gadget-icon {
  display: none !important;
}
.VIpgJd-ZVi9od-xl07Ob-lTBxed {
  display: flex !important;
}
.VIpgJd-ZVi9od-xl07Ob-OEVmcd {
  /* width: 94px !important; */
  /* left: 1197px !important; */
}
.VIpgJd-ZVi9od-xl07Ob-OEVmcd > body > .VIpgJd-ZVi9od-vH1Gmf {
  width: 100% !important;
  border: none !important;
}
</style>

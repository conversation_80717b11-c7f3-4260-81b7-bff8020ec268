<script setup>
import { storeToRefs } from "pinia";
import { useWebdevChecklistStore } from "~/stores/webdevchecklist";
const { squeeze, checklistTitle } = storeToRefs(useWebdevChecklistStore());
const { setSqueeze } = useWebdevChecklistStore();
const title = ref("Web Developer Checklist");
const setTitle = (item) => {
  title.value = item;
};
</script>

<template>
  <div>
    <div class="relative">
      <div @click="setSqueeze('squeeze')">
        <div class="bg-[#120736] header md:h-[100px] h-[100px] py-0 px-2">
          <DeveloperChecklistHeader
            class="transition-all duration-500 ease-in-out"
            :class="
              squeeze ? 'md:ml-[320px] ml-[80px]' : 'md:ml-[120px] ml-[80px]'
            "
            :title="checklistTitle"
          />
        </div>
        <div
          role="main"
          class="transition-all duration-500 ease-in-out min-h-[600px]"
          :class="
            squeeze ? 'md:ml-[320px] ml-[80px]' : 'md:ml-[120px] ml-[80px]'
          "
        >
          <slot @set-title="setTitle()" />
        </div>
      </div>
      <div
        class="checklist_sidebar transition-all duration-500 ease-in-out"
        :class="squeeze ? 'w-[304px]' : 'md:w-26 w-[80px]'"
      >
        <DeveloperChecklistSidebar @set-title="setTitle" />
      </div>
    </div>
    <Footer role="contentinfo" />
  </div>
</template>

<style>
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Quicksand:wght@300&family=Source+Serif+4:opsz,wght@8..60,200;8..60,300;8..60,400;8..60,500;8..60,600;8..60,700;8..60,800;8..60,900&family=Tinos:wght@700&display=swap");
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Hairline.otf")
    format("opentype");
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Thin.otf")
    format("opentype");
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-ExtraLight.otf")
    format("opentype");
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Light.otf")
    format("opentype");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Book.otf")
    format("opentype");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Medium.otf")
    format("opentype");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-SemiBold.otf")
    format("opentype");
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-ExtraBold.otf")
    format("opentype");
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Bold.otf")
    format("opentype");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Heavy.otf")
    format("opentype");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Proxima Nova";
  src: url("/Proxima-Nova-Font/Rene-Bieder-Milliard-Black.otf")
    format("opentype");
  font-weight: 1000;
  font-style: normal;
}

body {
  /* font-family: "Montserrat", sans-serif; */
  font-family: "Proxima Nova", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  background: #1a1139;
  color: #fff;
  padding-bottom: 30px;
  -webkit-font-smoothing: antialiased;
}
.header {
  box-shadow: 0px 4px 4px 0px #00000040;
  /* box-shadow: rgba(0, 0, 0, 0.25) 0px 14px 28px,
    rgba(0, 0, 0, 0.22) 0px 10px 10px;
  @apply border-b-[0.5px] border-[#d9d9d9] border-opacity-30; */
}
.checklist_sidebar {
  @apply absolute top-0 left-0 h-[130%];
  border-radius: 0px 10px 10px 0px;
  background: #120736;
  box-shadow: 4px 0px 20px 0px rgba(0, 0, 0, 0.16);
}
</style>

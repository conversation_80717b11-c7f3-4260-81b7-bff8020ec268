<script setup>
import { useRuntimeConfig } from "nuxt/app";

const config = useRuntimeConfig();
const title = "About Devxhub | Your Trusted Software Development Partner";
const description =
  "Learn about Devxhub’s mission, values, and commitment to delivering innovative software solutions that empower businesses worldwide.";
const keyWords =
  "Devxhub, about Devxhub, software development company, tech partner, software solutions";

useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});
useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const features = ref([
  {
    id: 1,
    icon: "/images/homepage/hero/circle.svg",
    title: "Full Transparency",
  },
  {
    id: 2,
    icon: "/images/homepage/hero/infinite.svg",
    title: "Expert Team",
  },
  {
    id: 3,
    icon: "/images/homepage/hero/graph.svg",
    title: "Daily & Weekly Reports",
  },
  {
    id: 4,
    icon: "/images/homepage/hero/loop.svg",
    title: "Flexible & On-Demand",
  },
]);

const faqData = ref([
  {
    id: 101,
    slectedItem: true,
    title: "What services does Devxhub offer?",
    description: `<p>Devxhub provides IT staff augmentation, full-stack development, mobile app development, custom and enterprise software solutions, MVP and SaaS development, AI/ML integration, and DevOps/cloud solutions.</p>`,
  },
  {
    id: 102,
    slectedItem: false,
    title: "How experienced is the Devxhub team?",
    description: `<p>Devxhub has over 5 years of service with a skilled team of 55+ professionals, including developers, software engineers, QA specialists, and project managers.</p>`,
  },
  {
    id: 103,
    slectedItem: false,
    title: "What industries does Devxhub serve?",
    description: `<p>We partner with Fortune 200+ companies, startups, and AI-driven ventures across various industries, delivering scalable and secure technology solutions.</p>`,
  },
  {
    id: 104,
    slectedItem: false,
    title: "How does Devxhub ensure project transparency?",
    description: `<p>Devxhub provides complete transparency with daily and weekly reports, clear communication, and video updates, allowing clients to track progress closely.</p>`,
  },
  {
    id: 105,
    slectedItem: false,
    title:
      "What makes Devxhub different from other software development companies?",
    description: `<p>The Devxhub difference is our multi-dimensional approach, which combines deep user insights, data-driven insights, and innovative technologies. With a relentless focus on security, reliability, and performance, we deliver scalable, end-to-end solutions that help you grow.</p>`,
  },
  {
    id: 106,
    slectedItem: false,
    title: "Can Devxhub support remote or on-demand staffing needs?",
    description: `<p>Yes, we offer flexible and on-demand team augmentation to integrate easily with your existing teams or projects.</p>`,
  },
  {
    id: 107,
    slectedItem: false,
    title: "What is Devxhub’s approach to security?",
    description: `<p>We prioritize data protection, confidentiality, and system integrity to ensure secure and reliable software solutions tailored to your needs.</p>`,
  },
  {
    id: 108,
    slectedItem: false,
    title: "How do I get started with Devxhub?",
    description: `<p>Simply email or phone us, submit an inquiry form, or book a direct call with a dedicated manager. We will discuss your project and requirements.</p>`,
  },
]);

const route = useRoute();
onMounted(() => {
  setTimeout(() => {
    const allAboutUs = document.querySelectorAll(".aboutSection");
    const allCareerSection = document.querySelectorAll(".counter-single-item");
    const observer = new window.IntersectionObserver((entries) => {
      entries.forEach((singleDiv) => {
        if (singleDiv.isIntersecting) {
          singleDiv.target.classList.remove("notInViewPort");
          singleDiv.target.classList.add("nowInViewPort");
        }
        // else if (!singleDiv.isIntersecting) {
        //   singleDiv.target.classList.add("notInViewPort");
        //   singleDiv.target.classList.remove("nowInViewPort");
        // }
      });
    });
    allAboutUs.forEach((item) => {
      observer.observe(item);
    });

    const counterWrap = document.getElementById("counterWrap");
    const text1 = document.getElementById("text1");
    const text2 = document.getElementById("text2");
    const text3 = document.getElementById("text3");
    const text4 = document.getElementById("text4");

    // First Observer
    const observerThree = new window.IntersectionObserver(
      (entries, observerInstance) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("final");
            text1.classList.add("final");
            text2.classList.add("final");
          } else {
            entry.target.classList.remove("final");
            text1.classList.remove("final");
            text2.classList.remove("final");
          }
        });
      },
      {
        root: null, // The viewport is the root
        rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
        threshold: 0.5, // Trigger when 50% of the element is visible
      }
    );

    // First Observer
    const observerTwo = new window.IntersectionObserver(
      (entries, observerInstance) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("final");
            text1.classList.add("final");
            text2.classList.add("final");
          } else {
            entry.target.classList.remove("final");
            text1.classList.remove("final");
            text2.classList.remove("final");
          }
        });
      },
      {
        root: null, // The viewport is the root
        rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
        threshold: 0.5, // Trigger when 50% of the element is visible
      }
    );

    // if (!isDesktop.value) {
    // First Observer
    const textObserverOne = new window.IntersectionObserver(
      (entries, observerInstance) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("final");
          } else {
            entry.target.classList.remove("final");
          }
        });
      },
      {
        root: null, // The viewport is the root
        rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
        threshold: 0.5, // Trigger when 50% of the element is visible
      }
    );

    // Second Observer
    const textObserverTwo = new window.IntersectionObserver(
      (entries, observerInstance) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("final");
            // observerInstance.disconnect();
          } else {
            entry.target.classList.remove("final");
          }
        });
      },
      {
        root: null, // The viewport is the root
        rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
        threshold: 0.5, // Trigger when 50% of the element is visible
      }
    );

    textObserverOne.observe(text3);
    textObserverTwo.observe(text4);
    // }

    allCareerSection.forEach((item) => {
      observerThree.observe(item);
    });

    // Start observing with the first observer
    observerTwo.observe(counterWrap);
  }, 700);
});
</script>

<template>
  <section class="container-fluid">
    <a class="link hidden" href="#"></a>
    <div class="pt-[160px] md:pt-[220px]">
      <div
        class="flex flex-col items-center pb-[95px] border-b border-[#ffffff1a]"
      >
        <div
          style="
            transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
              rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
            opacity: 1;
            transform-style: preserve-3d;
          "
          class="clutch-badge-new flex px-5 h-10 items-center justify-center border border-[#0000004d] rounded-full gap-2 bg-white"
        >
          <img
            src="https://cdn.prod.website-files.com/6399db4313c7980ffe940ca2/6666d249823832399f19d797_Clutch.svg"
            loading="lazy"
            alt=""
            class="clutch-logo"
          />
          <div class="flex items-center space-x-[5px]">
            <BaseIconStar class="size-4 text-[#1D1A20]" />
            <BaseIconStar class="size-4 text-[#1D1A20]" />
            <BaseIconStar class="size-4 text-[#1D1A20]" />
            <BaseIconStar class="size-4 text-[#1D1A20]" />
            <BaseIconStar class="size-4 text-[#1D1A20]" />
          </div>
          <div class="text-xl font-bold text-[#1D1A20] font-aeonik">5.0</div>
        </div>

        <h1
          class="max-w-[800px] text-center font-bold text-[#f1f1f2] secondary-h-tag mt-5 xl:!leading-[72px]"
        >
          <span class="text-[#999]">Your Trusted go-to </span
          ><span class="instrument-italic"
            >Team<br class="hidden min-[450px]:block" />
            Augmentation
          </span>
          <span class="text-[#999]">and </span
          ><span class="instrument-italic"
            >Software <br class="hidden min-[450px]:block" />
            Development
          </span>
          <span class="text-[#999]">Partner</span>
        </h1>
        <p
          class="max-w-[890px] text-base lg:text-xl text-[#999] mt-5 text-center"
        >
          A team of expert developers, software engineers, quality assurance
          specialists, and managers is ready to join your team and achieve
          growth together.
        </p>
      </div>
      <div
        class="grid md:hidden grid-cols-2 md:grid-cols-4 text-[#999999] gap-x-10 gap-y-4 justify-items-start z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="mobile"
        />
      </div>
      <div
        class="hidden md:flex text-[#999999] gap-10 justify-center items-center z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="desktop"
        />
      </div>
    </div>

    <div
      class="md:block hidden container-fluid we-are-devxhub-container py-[122px] relative overflow-hidden"
    >
      <div id="text1" class="whitespace-nowrap top-0 w-full">We are</div>
      <div id="counterWrap" class="md:!grid !hidden counter-wrap max-h-[337px]">
        <div
          id="card1"
          class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">001</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Years of Service
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              5+
            </p>
          </div>
        </div>
        <div
          id="card2"
          class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">002</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Team members
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              55+
            </p>
          </div>
        </div>
        <div
          id="card3"
          class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">003</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Location
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              3+
            </p>
          </div>
        </div>
        <div
          id="card4"
          class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">004</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Ongoing Projects
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              20+
            </p>
          </div>
        </div>
        <div
          id="card5"
          class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">005</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Happy Clients
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              200+
            </p>
          </div>
        </div>
      </div>
      <div id="text2" class="whitespace-nowrap bottom-0 w-full">Devxhub</div>
    </div>

    <AboutWhoWeAre />
    <AboutOurMission />
    <AboutOurCulture />
    <div
      class="md:hidden block container-fluid mt-[64px] relative overflow-hidden"
    >
      <p id="text3" class="w-full">We are</p>
      <p id="text4" class="w-full">Devxhub</p>
      <div class="md:hidden counter-wrap-mobile mt-[62px]">
        <div
          class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">001</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Years of Service
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              5+
            </p>
          </div>
        </div>
        <div
          class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">002</p>
              <p
                class="lg:pb-[210px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Team members
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              55+
            </p>
          </div>
        </div>
        <div
          class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">003</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Location
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              3+
            </p>
          </div>
        </div>
        <div
          class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">004</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Ongoing Projects
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              20+
            </p>
          </div>
        </div>
        <div
          class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
        >
          <div class="flex-grow">
            <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
              <p class="flex-grow number">005</p>
              <p
                class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[66px]"
              >
                Happy Clients
              </p>
            </div>
            <p
              class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
            >
              200+
            </p>
          </div>
        </div>
      </div>
    </div>
    <HomeWhatMakeUs class="md:pt-[180px] pt-[80px]" />

    <HomeHowWeWork class="md:pt-[180px] pt-[80px]" />
    <ServicesOurBenefits class="container-fluid md:pt-[100px] pt-[64px]" />
    <AboutExcellenceStandard />
    <AboutClientReview class="md:pt-[150px] pt-[80px]" />
    <HomeFaq :faqData="faqData" />
    <!-- <HomeWhatMakeUs
      v-if="config.public.workflow === 'dev'"
      class="container-fluid pt-[150px]"
    /> -->
  </section>
</template>
<style scoped>
.nowInViewPort {
  @apply transition-all ease-in duration-1000 opacity-100;
}
.notInViewPort {
  @apply transition-all ease-in duration-300 opacity-0;
  transform: translate3d(0, 100px, 0) scale(0.6);
}
@media (min-width: 1921px) {
  .extra-lg {
    padding-left: 200px;
  }
  .exl-right {
    padding-right: 200px;
  }
}

.counter-wrap {
  z-index: 1;
  grid-column-gap: 24px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 0, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}
.counter-single-item {
  z-index: 0;
  background-color: #fff;
  width: 100%;
  /* transition: all 0.5s ease-in-out 0.5s; */
  position: relative;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  transition: transform 0.5s ease-in-out 0.5s, clip-path 0.5s ease-in-out,
    background-color 0.5s ease-in-out, color 0.5s ease-in-out;
}
.counter-single-item .number {
  @apply mb-[7vw] text-xl;
  font-family: "Neue Machina", sans-serif;
}
.counter-single-item .large-number {
  @apply mb-[0vw] xl:text-[54px] text-[38px] pt-5 xl:leading-[50px] leading-[34px];
  font-family: "Neue Machina", sans-serif;
}

#card1 {
  transform: translateX(211%);
  /* background: red; */
}

#card2 {
  transform: translateX(106%);
  /* background: blue; */
}

#card4 {
  transform: translateX(-106%);
  /* background: orange; */
}

#card5 {
  transform: translateX(-211%);
  /* background: yellow; */
}

#card1,
#card2,
#card4,
#card5 {
  opacity: 1;
  /* background: red; */
}

#card3 {
  z-index: 1;
}

#card1.final,
#card2.final,
#card4.final,
#card5.final {
  transform: translateX(0);
  opacity: 1;
  /* background: red; */
}
.counter-single-item:hover {
  background-color: black;
  color: white;
  clip-path: polygon(5% 5%, 95% 2%, 95% 95%, 5% 98%);
}
.counter-wrap.final {
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
}

.counter-single-item-mobile {
  background-color: #fff;
  width: 100%;
  /* transition: all 0.5s ease-in-out 0.5s; */
  position: relative;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  transition: clip-path 0.5s ease-in-out, background-color 0.5s ease-in-out,
    color 0.5s ease-in-out;
}
.counter-single-item-mobile .number {
  @apply mb-[7vw];
  font-family: "Neue Machina", sans-serif;
}
.counter-single-item-mobile .large-number {
  @apply mb-[0vw] xl:text-[54px] text-[38px] pt-5 xl:leading-[50px] leading-[34px];
  font-family: "Neue Machina", sans-serif;
}
.counter-single-item-mobile:hover {
  background-color: black;
  color: #f1f1f2;
  clip-path: polygon(5% 5%, 95% 2%, 95% 95%, 5% 98%);
}

@media screen and (max-width: 991px) {
  .counter-wrap {
    grid-column-gap: 10px;
    padding-left: 20px;
    padding-right: 20px;
  }
}
@media screen and (max-width: 767px) {
  .counter-wrap-mobile {
    white-space: nowrap;
    z-index: 1;
    grid-column-gap: 24px;
    grid-row-gap: 16px;
    grid-template-rows: auto;
    grid-auto-columns: 1fr;
    display: grid;
    position: relative;
    overflow: hidden;
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media screen and (max-width: 479px) {
  .counter-wrap-mobile {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    grid-template-columns: 1fr 1fr;
  }
}
#text1 {
  padding-left: 16.667vw;
  @apply lg:text-[200px] md:text-[120px] lg:mb-[-40px] md:mb-[-30px];
  font-weight: 700;
  line-height: 100%;
  color: #999;
  transform: translate3d(0vw, 6.417vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
  transition: all 0.6s ease-in-out;
}
#text2 {
  text-align: right;
  margin-top: -66px;
  padding-right: 16.042vw;
  @apply lg:text-[200px] md:text-[120px] lg:mt-[-66px] md:mt-[-44px];
  font-weight: 700;
  line-height: 100%;
  color: #999;
  transform: translate3d(0vw, -6.417vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
  transition: all 0.6s ease-in-out;
}
#text1.final {
  transform: translate3d(-10.417vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
}
#text2.final {
  transform: translate3d(10.417vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
}
@media (max-width: 767px) {
  #text3 {
    padding-left: 0px;
    margin: 0px;
    @apply text-[52px] leading-[60px];
    font-weight: 700;
    color: #999;
    transform: translate3d(-10.417vw, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
    transition: all 0.6s ease-in-out;
  }
  #text4 {
    padding-left: 32.346px;
    margin: 0px;
    text-align: left;
    @apply text-[52px] leading-[60px];
    font-weight: 700;
    color: #999;
    transform: translate3d(10.417vw, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
    transition: all 0.6s ease-in-out;
  }
  #text3.final {
    transform: translate3d(0vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
  }
  #text4.final {
    transform: translate3d(0vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
  }
}

.we-are-devxhub-container {
  background-image: url("/public/landing/we-are-devxhub.svg");
  background-size: cover;
}
</style>

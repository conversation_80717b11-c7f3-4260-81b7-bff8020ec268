<script setup>
const metaDescription =
  "<PERSON><PERSON><PERSON><PERSON> is your trusted partner for software development and team extension, offering custom IT solutions, skilled talent, and full-cycle digital services";
const metaKeywords =
  "software development, team extension, custom IT solutions, skilled talent, full-cycle digital services";

useSeoMeta({
  ogDescription: () => metaDescription.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: metaKeywords,
    },
  ],
});

import { useRuntimeConfig } from "nuxt/app";
import "vue3-carousel/dist/carousel.css";

const config = useRuntimeConfig();

const route = useRoute();
const overview = ref([
  {
    id: 1,
    image: "/home/<USER>/years-of-experience.svg",
    field: "Years of experience",
    fieldValue: 5,
  },
  {
    id: 2,
    image: "/home/<USER>/clients.svg",
    field: "Respected Clients",
    fieldValue: 50,
  },
  {
    id: 3,
    image: "/home/<USER>/dedicated-engineers.svg",
    field: "Dedicated Engineers",
    fieldValue: 52,
  },
  {
    id: 4,
    image: "/home/<USER>/ongoing-project.svg",
    field: "Ongoing Project",
    fieldValue: 20,
  },
  {
    id: 5,
    image: "/home/<USER>/successful-projects.svg",
    field: "Successful Projects",
    fieldValue: 150,
  },
]);
const animateNumber = () => {
  overview.value.forEach((item) => {
    let counts = setInterval(updated, 0);
    let start = 0;
    let end = item.fieldValue;
    function updated() {
      item.fieldValue = ++start;
      if (start === end) {
        clearInterval(counts);
      }
    }
  });
};
const features = ref([
  {
    id: 1,
    icon: "/images/homepage/hero/circle.svg",
    title: "Full Transparency",
  },
  {
    id: 2,
    icon: "/images/homepage/hero/infinite.svg",
    title: "Expert Team",
  },
  {
    id: 3,
    icon: "/images/homepage/hero/graph.svg",
    title: "Daily & Weekly Reports",
  },
  {
    id: 4,
    icon: "/images/homepage/hero/loop.svg",
    title: "Flexible & On-Demand",
  },
]);

const imageStates = ref({
  hero: false,
  clutch: false,
});
const show = ref(route.path === "/");
// const show = ref(false);

const showCarousal = ref(false);
const activeIndex = ref(0);
const handleImageLoad = (key) => {
  imageStates.value[key] = true;
};
const companies = ref([
  // {
  //   image: "/landing/hero-image.webp",
  //   name: "hero_image",
  //   description: `<div
  //       class="text-center sm:text-left flex flex-col space-y-4 sm:space-y-6 lg:space-y-8 w-full z-[1]"
  //     >
  //       <h2
  //         class="font-semibold text-4xl lg:text-[40px] text-[#FDB21D] leading-[50px] lg:leading-[66px]"
  //       >
  //         Web Design & Development and Provider of Dedicated Team and Developer
  //       </h2>
  //       <p
  //         class="text-base sm:text-xl lg:text-[24px] leading-[42px] lg:leading-[42px] text-white"
  //       >
  //         Let your ideas come to life with elegance and professionalism.
  //       </p>
  //       <a href="/contact-us" rel="noopener noreferrer"
  //         tabindex="-1"
  //         aria-label="Contact Us"
  //         class="flex justify-center mx-auto sm:ml-0 w-40 xl:w-48 h-12 bg-[#FAAF04] px-5 py-3 font-bold text-[#1D1A20] text-base rounded-sm whitespace-nowrap"
  //       >
  //         <span>Contact Us</span>
  //       </a>
  //     </div>`,
  // },
  // {
  //   image: "/landing/hire-hero.webp",
  //   name: "hire_developers",
  //   description: `<div
  //       class="text-center sm:text-left flex flex-col space-y-4 sm:space-y-6 lg:space-y-8 w-full z-[1] text-3xl md:text-4xl lg:text-5xl"
  //     >
  //       <h2 class="lg:text-6xl font-[500] text-white">
  //         Hire
  //       </h2>
  //       <h2 class="font-[500] text-[#FDB21D]">Top Rated Developers</h2>
  //       <h2
  //         class="font-semibold text-white leading-[50px] lg:leading-[66px]"
  //       >
  //         On Contract
  //       </h2>
  //       <p
  //         class="text-base sm:text-xl lg:text-[24px] leading-[42px] lg:leading-[42px] text-white"
  //       >
  //         Expert level Software Developers, UI/UX Designers, QAs, Frontend & Backend Developers, DevOps engineers are ready to join your team remotely.
  //       </p>
  //       <a href="/contact-us" rel="noopener noreferrer"
  //         tabindex="-1"
  //         aria-label="Hire Developers"
  //         class="flex justify-center mx-auto sm:ml-0 w-40 xl:w-48 h-12 bg-[#FAAF04] px-5 py-3 font-bold text-[#1D1A20] text-base rounded-sm whitespace-nowrap"
  //       >
  //         <span>Hire Developers</span>
  //       </a>
  //     </div>`,
  // },
  // {
  //   image: "/landing/tech_solution.webp",
  //   name: "tech_solution",
  //   description: `<div
  //       class="text-center sm:text-left flex flex-col space-y-4 sm:space-y-6 lg:space-y-8 w-full z-[1] text-3xl md:text-4xl lg:text-5xl"
  //     >
  //       <h2 class="lg:text-6xl font-[500] text-white">
  //         We Provide
  //       </h2>
  //       <h2 class="font-[500] text-[#FDB21D]">Tech Solutions & Help You</h2>
  //       <h2
  //         class="font-semibold text-white leading-[50px] lg:leading-[66px]"
  //       >
  //         to Grow Your Business
  //       </h2>
  //       <p
  //         class="text-base sm:text-xl lg:text-[24px] leading-[42px] lg:leading-[42px] text-white"
  //       >
  //         Get custom software development and web service at the best price from
  //         a company making success stories for over 10 years.
  //       </p>
  //       <div class="flex justify-center md:justify-start">
  //         <a href="#ourServices" rel="noopener noreferrer"
  //           tabindex="-1"
  //           aria-label="Our Services"
  //           class="mx-auto sm:ml-0 flex justify-center items-center w-40 xl:w-48 h-12 bg-[#FAAF04] px-4 py-1 md:py-2 font-bold text-[#1D1A20] text-base rounded-sm whitespace-nowrap"
  //         >
  //           <span>Our Services</span> <span class="pl-2 font-bold text-2xl leading-[1rem] pb-[3px]">→</span>
  //         </a>
  //       </div>
  //     </div>`,
  // },
  // {
  //   image: "/landing/SQA.webp",
  //   name: "SQA",
  //   description: `<div
  //         class="text-center sm:text-left flex flex-col space-y-4 sm:space-y-6 lg:space-y-8 w-full z-[1] text-3xl md:text-4xl lg:text-5xl"
  //       >
  //         <h2 class="lg:text-6xl font-[500] text-white">Hire</h2>
  //         <h2 class="font-[500] text-[#FDB21D]">Top Rated SQA Engineers </h2>
  //         <h2
  //           class="font-semibold text-white leading-[50px] lg:leading-[66px]"
  //         >
  //           On Contract
  //         </h2>
  //         <p
  //           class="text-base sm:text-xl lg:text-[24px] leading-[42px] lg:leading-[42px] text-white"
  //         >
  //           Empower your software with the prowess of our experienced
  //           engineers, guaranteeing peak performance through meticulous
  //           quality assurance.
  //         </p>
  //         <a href="/contact-us" rel="noopener noreferrer"
  //           tabindex="-1"
  //           aria-label="Hire SQA"
  //           class="mx-auto sm:ml-0 flex justify-center w-40 xl:w-48 h-12 bg-[#FAAF04] px-5 py-3 font-bold text-[#1D1A20] text-base rounded-sm whitespace-nowrap"
  //         >
  //           <span>Hire SQA</span>
  //         </a>
  //       </div>`,
  // },
  // {
  //   description: `<div
  //         class="text-center flex flex-col items-center justify-center w-full h-full z-[1]"
  //       >
  //         <h2 class="hero_section_carousal_aeonik max-w-[1280px] text-4xl md:text-5xl lg:text-7xl md:leading-[68px] lg:leading-[88px] font-[700] text-[#F1F1F2]"><span class="text-[#999]">In Need of</span> Skilled Developers<span class="text-[#999]"> with Innovative</span> Software
  //         Solutions <span class="text-[#999]">at a</span> Lower Cost?</h2>
  //         <p
  //           class="hero_section_carousal_aeonik max-w-[981px] mt-[32px] text-xl md:text-[28px] lg:text-[32px] md:leading-[36px] lg:leading-[42px] text-[#999]"
  //         >
  //             We provide dedicated remote development teams of top Bangladeshi developers for industries with unique challenges and needs.
  //         </p>
  //       </div>`,
  // },
  // {
  //   description: `<div
  //         class=" flex items-start justify-start md:gap-[66px] h-full z-[1] relative"
  //       >
  //         <div class="z-[1]">
  //           <h2 class=" xl:max-w-[836px] lg:max-w-[605px] text-[38px] leading-[45px]  lg:text-[48px] lg:leading-[52px]
  //         xl:text-7xl xl:leading-[72px] font-[700] text-[#F1F1F2]"><span class="text-[#999]">In Need of</span> <span class="instrument-italic">Skilled Developers</span><span class="text-[#999]"> with Innovative</span> <span class="instrument-italic">Software Solutions</span>
  //          <span class="text-[#999]">at a</span> <span class="instrument-italic">Lower Cost?</span></h2>
  //         <p
  //           class="hero_section_carousal_aeonik xl:max-w-[860px] lg:max-w-[600px]  mt-[20px] text-xl lg:leading-[28px] text-[#999999]"
  //         >
  //           We provide dedicated remote development teams of top Bangladeshi developers for industries with unique challenges and needs.
  //         </p>
  //         <a href="/contact-us" rel="noopener noreferrer"
  //           tabindex="-1"
  //           aria-label="Book a Free Consultation"
  //           class=" flex justify-center items-center mx-auto md:mx-0 w-[260px] h-[46px] mt-[73px] bg-[#FFD700] px-[30px] py-[9px] font-medium text-[#1D1A20] text-base md:text-lg rounded-full whitespace-nowrap"
  //         >
  //           <span>Book a Free Consultation</span>
  //         </a>
  //         </div>
  //         <div class="hidden lg:block absolute lg:top-[19%] xl:top-[15%] right-[40px] left-auto bottom-auto w-[274px] transform scale-[1.5]">
  //           <video
  //             autoplay
  //             loop
  //             muted
  //             playsinline
  //           >
  //             <source src="/landing/video/color_glass_BG_cropped.mp4" type="video/mp4"">
  //             Your browser does not support the video tag.
  //           </video>
  //         </div>
  //       </div>`,
  // },
  {
    description: `<div
          class=" flex items-start justify-start md:gap-[66px] h-full z-[1]"
        >
          <div class="z-[1]">
            <h1 class=" text-center md:text-left xl:max-w-[836px] lg:max-w-[750px] text-[38px] leading-[45px]  lg:text-[48px] lg:leading-[52px]
          xl:text-7xl xl:leading-[72px] font-[700] text-[#F1F1F2]"><span class="text-[#999]">In Need of</span> <span class="instrument-italic">Skilled Developers</span><span class="text-[#999]"> with Innovative</span> <span class="instrument-italic">Software Solutions</span>
           <span class="text-[#999]">at a</span> <span class="instrument-italic">Lower Cost?</span></h1>
          <p
            class="hero_section_carousal_aeonik xl:max-w-[890px] lg:max-w-[750px]  mt-[20px] text-lg  lg:leading-[28px] text-center md:text-left text-[#999999]"
          >
            We provide dedicated remote development teams of top Bangladeshi developers for industries with unique challenges and needs.
          </p>
          <a href="/contact-us" rel="noopener noreferrer"
            tabindex="-1"
            aria-label="Book a Free Consultation"
            class=" flex justify-center items-center mx-auto md:mx-0 md:w-[260px] h-[46px] mt-[73px] bg-[#FFD700] px-[30px] py-[9px] font-medium text-[#1D1A20] text-base md:text-lg rounded-full whitespace-nowrap"
          >
            <span>Book a Free Consultation</span>
          </a>
          </div>
          <div class="lg:block hidden relative">
            <ContentLoader
              v-if="!imageStates.hero"
              :default-items="false"
              :items="[
                {
                  extraClasses: 'md:w-[274px] w-[170px] h-[96px] md:h-[347px] aspect-square object-contain scale-[1.5]',
                }
              ]"
            />
            <img
              src="/landing/video/logo_animation.gif"
              loading="lazy"
              alt="Devxhub logo static fallback"
              class="md:w-[274px] w-[170px] h-[96px] md:h-[347px] aspect-square object-contain transform scale-[1.5] transition-opacity duration-300 bg-transparent"
              @load="handleImageLoad('hero')"
              :class="{ 
                'opacity-0': !imageStates.hero, 
                'opacity-100': imageStates.hero 
              }"
            />
        </div>
        </div>`,
  },
  // {
  //   description: `<div
  //         class="text-center flex flex-col items-center justify-center w-full h-full z-[1]"
  //       >
  //         <h2 class="hero_section_carousal_aeonik max-w-[901px] text-4xl md:text-5xl lg:text-7xl md:leading-[68px] lg:leading-[88px] font-[700] text-[#F1F1F2]">Looking for <span class="text-[#FFD700]">Innovative </span><span class="text-[#8CAE33]">Software Solutions!</span></h2>
  //         <p
  //           class="hero_section_carousal_aeonik max-w-[905px] mt-[32px] text-xl md:text-[28px] lg:text-[32px] md:leading-[36px] lg:leading-[42px] text-[#F1F1F2]"
  //         >
  //           We provide cost-effective software solutions for industries with unique challenges and specific requirements.
  //         </p>
  //         <a href="/contact-us" rel="noopener noreferrer"
  //           tabindex="-1"
  //           aria-label="Book a Free Consultation"
  //           class="hero_section_carousal_aeonik flex justify-center items-center mt-[80px] bg-[#FFD700] px-[52px] py-[12px] font-normal text-[#1D1A20] text-base md:text-lg lg:text-xl rounded-full whitespace-nowrap"
  //         >
  //           <span>Book a Free Consultation</span>
  //         </a>
  //       </div>`,
  // },
]);

const txt = ref("");
const toRotate = JSON.stringify([
  "Web Design & Development and Provider of Dedicated Team and Developer.",
]);
const period = 20;
const isDeleting = ref(false);
const loopNum = ref(0);

const tick = () => {
  const i = loopNum.value % JSON.parse(toRotate).length;
  const fullTxt = JSON.parse(toRotate)[i];

  if (isDeleting.value) {
    txt.value = fullTxt.substring(0, txt.value.length - 1);
  } else {
    txt.value = fullTxt.substring(0, txt.value.length + 1);
  }

  let delta = 200 - Math.random() * 100;

  if (isDeleting.value) {
    delta /= 2;
  }

  if (!isDeleting.value && txt.value === fullTxt) {
    // isDeleting.value = true;
    delta = period;
  } else if (isDeleting.value && txt.value === "") {
    isDeleting.value = false;
    loopNum.value++;
    delta = 500;
  }

  setTimeout(tick, delta);
};

const txtSQA = ref("");
const toRotateSQA = JSON.stringify(["Top Rated SQA 🙌    "]);
const periodSQA = 20;
const isDeletingSQA = ref(false);
const loopNumSQA = ref(0);

const tickSQA = () => {
  const i = loopNumSQA.value % JSON.parse(toRotateSQA).length;
  const fullTxt = JSON.parse(toRotateSQA)[i];

  if (isDeletingSQA.value) {
    txtSQA.value = fullTxt.substring(0, txtSQA.value.length - 1);
  } else {
    txtSQA.value = fullTxt.substring(0, txtSQA.value.length + 1);
  }

  let deltaSQA = 200 - Math.random() * 100;

  if (isDeletingSQA.value) {
    deltaSQA /= 2;
  }

  if (!isDeletingSQA.value && txtSQA.value === fullTxt) {
    // isDeletingSQA.value = true;
    deltaSQA = periodSQA;
  } else if (isDeletingSQA.value && txtSQA.value === "") {
    isDeletingSQA.value = false;
    loopNumSQA.value++;
    deltaSQA = 500;
  }

  setTimeout(tickSQA, deltaSQA);
};

const txtDeveloper = ref("");
const toRotateDeveloper = JSON.stringify(["Top Rated Developers 🙌    "]);
const periodDeveloper = 20;
const isDeletingDeveloper = ref(false);
const loopNumDeveloper = ref(0);

const tickDeveloper = () => {
  const i = loopNumDeveloper.value % JSON.parse(toRotateDeveloper).length;
  const fullTxt = JSON.parse(toRotateDeveloper)[i];

  if (isDeletingDeveloper.value) {
    txtDeveloper.value = fullTxt.substring(0, txtDeveloper.value.length - 1);
  } else {
    txtDeveloper.value = fullTxt.substring(0, txtDeveloper.value.length + 1);
  }

  let deltaDeveloper = 200 - Math.random() * 100;

  if (isDeletingDeveloper.value) {
    deltaDeveloper /= 2;
  }

  if (!isDeletingDeveloper.value && txtDeveloper.value === fullTxt) {
    // isDeletingDeveloper.value = true;
    deltaDeveloper = periodDeveloper;
  } else if (isDeletingDeveloper.value && txtDeveloper.value === "") {
    isDeletingDeveloper.value = false;
    loopNumDeveloper.value++;
    deltaDeveloper = 500;
  }

  setTimeout(tickDeveloper, deltaDeveloper);
};

const txtSolution = ref("");
const toRotateSolution = JSON.stringify(["Tech Solutions & Help You    "]);
const periodSolution = 20;
const isDeletingSolution = ref(false);
const loopNumSolution = ref(0);

const tickSolution = () => {
  const i = loopNumSolution.value % JSON.parse(toRotateSolution).length;
  const fullTxt = JSON.parse(toRotateSolution)[i];

  if (isDeletingSolution.value) {
    txtSolution.value = fullTxt.substring(0, txtSolution.value.length - 1);
  } else {
    txtSolution.value = fullTxt.substring(0, txtSolution.value.length + 1);
  }

  let deltaSolution = 200 - Math.random() * 100;

  if (isDeletingSolution.value) {
    deltaSolution /= 2;
  }

  if (!isDeletingSolution.value && txtSolution.value === fullTxt) {
    // isDeletingSolution.value = true;
    deltaSolution = periodSolution;
  } else if (isDeletingSolution.value && txtSolution.value === "") {
    isDeletingSolution.value = false;
    loopNumSolution.value++;
    deltaSolution = 500;
  }

  setTimeout(tickSolution, deltaSolution);
};

const handleInit = () => {
  if (route.name === "index" || !show.value) {
    show.value = route.path === "/";
  }
};

const viewPortController = () => {
  const allCareerSection = document.querySelectorAll(".counter-single-item");
  const counterWrap = document.getElementById("counterWrap");
  const text1 = document.getElementById("text1");
  const text2 = document.getElementById("text2");
  const text3 = document.getElementById("text3");
  const text4 = document.getElementById("text4");

  // First Observer
  const observer = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          text1.classList.add("final");
          text2.classList.add("final");
        } else {
          entry.target.classList.remove("final");
          text1.classList.remove("final");
          text2.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  // Second Observer
  const observerTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        } else {
          entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );
  // if (!isDesktop.value) {
  // First Observer
  const textObserverOne = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        } else {
          entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  // Second Observer
  const textObserverTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        } else {
          entry.target.classList.remove("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -20% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0.5, // Trigger when 50% of the element is visible
    }
  );

  textObserverOne.observe(text3);
  textObserverTwo.observe(text4);
  // }

  allCareerSection.forEach((item) => {
    observerTwo.observe(item);
  });

  // Start observing with the first observer
  observer.observe(counterWrap);

  // what makes us difference section animation
  const titleAnimation = document.querySelectorAll(".title-aimation");
  const observerThree = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px -10% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  titleAnimation.forEach((item) => {
    observerThree.observe(item);
  });

  const descripAnimation = document.querySelectorAll(".descrip-aimation");
  const observerFour = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 5% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  descripAnimation.forEach((item) => {
    observerFour.observe(item);
  });
};

const fullTimes = ref([
  {
    image: "/landing/tech/Asset_1.svg",
    title: "Feel Like an In-House Team",
    description:
      "Senior full-time developers seamlessly integrate with your team, ensuring full control, efficiency, and smooth collaboration.",
  },
  {
    image: "/landing/tech/Asset_2.svg",
    title: "Taking the Lead",
    description:
      "Devxhub developers proactively solve challenges, drive innovation, and ensure seamless execution with strong leadership and technical expertise.",
  },
  {
    image: "/landing/tech/Asset_3.svg",
    title: "Achieve More with Lower Costs",
    description: `Hire skilled developers from Bangladesh at lower costs than the US/EU, enabling business growth, risk reduction, and higher profits.
`,
  },
  {
    image: "/landing/tech/Asset_4.svg",
    title: "Agile, Adaptive, and Flexible",
    description: `Our agile approach ensures fast turnaround, continuous improvement, and scalable teams that adapt to evolving business needs effortlessly.`,
  },
  {
    image: "/landing/tech/Asset_5.svg",
    title: "Transparency and Reporting",
    description:
      "Clear communication, detailed progress tracking, and regular updates keep you aligned, ensuring accountability and control over your projects.",
  },
  {
    image: "/landing/tech/Asset_6.svg",
    title: "Global Expertise, Collaboration Across Time Zones",
    description:
      "Our expert developers work seamlessly across industries and time zones, solving complex challenges with innovative solutions.",
  },
  // {
  //   image: "/landing/tech/Asset_7.svg",
  //   title: "Customized Solutions",
  //   description:
  //     "Devxhub designs tech solutions tailored to your unique business needs, ensuring maximum impact and efficiency.",
  // },
  // {
  //   image: "/landing/tech/Asset_8.svg",
  //   title: "End-to-End Services",
  //   description:
  //     "From concept to deployment, we provide comprehensive solutions, including software development, system integration, and ongoing support ranging from Startup to large-scale applications.",
  // },
  // {
  //   image: "/landing/tech/Asset_9.svg",
  //   title: "Scalable and Future-Proof",
  //   description:
  //     "Devxhub builds scalable solutions designed to grow with your business and adapt to evolving technological landscapes.",
  // },
  // {
  //   image: "/landing/tech/Asset_10.svg",
  //   title: "Agile and Adaptive Approach",
  //   description:
  //     "We follow agile methodologies, ensuring flexibility, faster turnaround, and continuous improvement throughout the project lifecycle.",
  // },
]);
const changeSpinnerStyle = ref(false);
const headerTitle = ref(`<div class="overflow-hidden md:block hidden">
              <p class="text-[20px] title-animation">
            <span class="text-[#999999] text-[20px]">Why Select</span> <br />
            <span class="instrument-italic text-[#F1F1F2]"
              >Full-time Developers</span
            >
            <br />
            <span>
              <span class="text-[#999999]">and</span>
              <span class="instrument-italic text-[#F1F1F2]">
                Tech Solutions </span
              >
            </span>
            <br />
            <span class="text-[#999999]">from Devxhub?</span>
          </p>
            </div>
            <div class="overflow-hidden block md:hidden">
              <p class="text-[18px] leading-[28px] title-animation max-w-[305px]">
            <span class="text-[#999999] text-[20px]">Why Select</span> 
            <span class="instrument-italic text-[#F1F1F2]"
              >Full-time Developers</span
            >
            
            <span>
              <span class="text-[#999999]">and</span>
              <span class="instrument-italic text-[#F1F1F2]">
                Tech Solutions </span
              >
            </span>
           
            <span class="text-[#999999]">from Devxhub?</span>
          </p>
            </div>
            `);
onMounted(() => {
  handleInit();
  tick();
  tickDeveloper();
  tickSolution();
  tickSQA();
  viewPortController();
});

const faqData = ref([
  {
    id: 101,
    slectedItem: true,
    title: "Does Devxhub offer AI and machine learning solutions?",
    description: `<p>Yes, they provide AI-driven solutions such as predictive analytics, business intelligence, AI chatbot development, and NLP integrations.</p>`,
  },
  {
    id: 102,
    slectedItem: false,
    title: "How can I initiate a project with Devxhub?",
    description: `<p>You can start by visiting their service pages, following the order system, or contacting them directly through their website.</p>`,
  },
  {
    id: 103,
    slectedItem: false,
    title: "What is the typical project workflow at Devxhub?",
    description: `<p>The process includes defining requirements, planning, development, thorough testing, documentation, deployment, user training, review, support, and project closure.</p>`,
  },
  {
    id: 104,
    slectedItem: false,
    title: "Does Devxhub offer support after project completion?",
    description: `<p>Yes, we provide ongoing support and maintenance services, including monthly or yearly support plans upon request.</p>`,
  },
  {
    id: 105,
    slectedItem: false,
    title: "How does Devxhub ensure project transparency?",
    description: `<p>We maintain clear communication, detailed progress tracking, and regular updates to keep clients aligned and informed.</p>`,
  },
  {
    id: 106,
    slectedItem: false,
    title: "Can Devxhub handle full-stack development?",
    description: `<p>Yes, we offer end-to-end full-stack development services, delivering scalable and secure applications tailored to business needs.</p>`,
  },
  {
    id: 107,
    slectedItem: false,
    title: "Who can become a customer at Devxhub?",
    description: `<p>Anyone seeking IT services can become a customer, regardless of business size or nature.</p>`,
  },
  {
    id: 108,
    slectedItem: false,
    title: "How does Devxhub handle payments?",
    description: `<p>We accept various payment methods, including Payoneer, Wise.com, MoneyGram, Remitly, direct bank transfers, wire transfers, and debit/credit cards.</p>`,
  },
  {
    id: 109,
    slectedItem: false,
    title: "How can I get support from Devxhub?",
    description: `<p>For immediate assistance, you can submit a ticket through their support page or call us directly.</p>`,
  },
  {
    id: 110,
    slectedItem: false,
    title: "Does Devxhub offer dedicated project managers?",
    description: `<p>Yes, we assign dedicated managers to oversee projects, ensuring smooth communication and execution.</p>`,
  },
]);
</script>
<template>
  <div class="h-full">
    <div class="md:pt-[220px] pt-[120px] w-full h-full md:min-h-[600px]">
      <!-- <div
        class="w-[112px] h-[112px] absolute md:top-24 top-16 right-0 sun-image"
      >
        <img
          class="w-full aspect-square object-contain"
          src="~/assets/img/landing/sun.png"
          alt="sun-image"
        />
      </div> -->

      <div class="relative carousel-div w-full h-full overflow-hidden">
        <ContentLoader
          v-if="!show"
          :default-items="false"
          :items="[
            {
              width: '100%',
              height: 'h-8',
              marginTop: '',
            },
            {
              width: '100%',
              height: 'h-8',
              marginTop: 'mt-4',
              extraClasses: 'rounded-lg',
            },
          ]"
        />
        <div v-if="show">
          <div
            class="flex flex-col md:items-center justify-center overflow-hidden container-fluid h-full md:pt-[0px] pt-[0px] md:pb-[0px] pb-[0px]"
            v-for="(company, index) in companies"
            :key="index"
          >
            <div class="block md:hidden">
              <HomeRatingSection :is-mobile="true" />
            </div>
            <div
              :class="`w-full h-full ${index}`"
              v-html="company.description"
            ></div>
            <!-- <div class="md:hidden flex justify-between items-center mt-5">
              <div class="space-y-[5px] pt-5 md:hidden">
                <div class="flex justify-start items-center">
                  <img
                    width="57"
                    height="16"
                    src="/public/landing/icons/clutch.svg"
                    alt="clutch"
                  />
                </div>
                <div class="flex space-x-2.5 items-center">
                  <p class="text-[16px] text-[#F1F1F2] font-bold">5.0</p>
                  <div class="flex space-x-[5px] text-[#FFD700] text-[12px]">
                    <ClientOnly>
                      <fa :icon="['fas', 'star']" />
                      <fa :icon="['fas', 'star']" />
                      <fa :icon="['fas', 'star']" />
                      <fa :icon="['fas', 'star']" />
                      <fa :icon="['fas', 'star']" />
                    </ClientOnly>
                  </div>
                </div>
              </div>
              <div
                class="lg:w-[190px] lg:h-[190px] w-[142px] h-[142px] relative block md:hidden"
                @mouseenter="changeSpinnerStyle = true"
                @mouseleave="changeSpinnerStyle = false"
              >
                <BaseIconSpinner
                  class="absolute lg:w-[190px] lg:h-[190px] w-[142px] h-[142px] top-0 left-0 transition-all duration-[10s] ease-in-out"
                  :class="
                    changeSpinnerStyle
                      ? 'rotate-animation-zero'
                      : 'rotate-animation'
                  "
                  :color="changeSpinnerStyle ? 'black' : 'white'"
                  :bgcolor="changeSpinnerStyle ? '#FFD700' : 'black'"
                />
                <BaseIconArrowIcon
                  class="lg:w-[190px] lg:h-[190px] w-[142px] h-[142px] absolute top-0 left-0 transform transition-all duration-[0.2s] ease-linear fill-white"
                  :class="changeSpinnerStyle ? 'rotate-[60deg]' : ''"
                  :color="changeSpinnerStyle ? 'black' : 'white'"
                />
              </div>
            </div> -->
            <div
              class="grid grid-cols-2 text-[#999999] gap-x-10 gap-y-4 justify-items-start z-[1] md:hidden pt-[50px] pb-[64px] whitespace-nowrap"
            >
              <HomeHeroFeatureIcon
                v-for="feature in features"
                :key="feature.id"
                :icon="feature.icon"
                :title="feature.title"
                size="mobile"
              />
            </div>
            <div
              class="relative flex md:flex-row md:space-y-0 space-y-6 flex-col md:justify-between md:items-center md:w-full"
            >
              <div class="md:block hidden">
                <HomeRatingSection :is-mobile="false" />
              </div>
              <div
                class="hidden lg:w-[190px] lg:h-[190px] w-[142px] h-[142px]"
                @mouseenter="changeSpinnerStyle = true"
                @mouseleave="changeSpinnerStyle = false"
              >
                <BaseIconSpinner
                  class="absolute lg:w-[190px] lg:h-[190px] w-[142px] h-[142px] top-0 left-0 transition-all duration-[10s] ease-in-out"
                  :class="
                    changeSpinnerStyle
                      ? 'rotate-animation-zero'
                      : 'rotate-animation'
                  "
                  :color="changeSpinnerStyle ? 'black' : 'white'"
                  :bgcolor="changeSpinnerStyle ? '#FFD700' : 'black'"
                />
                <BaseIconArrowIcon
                  class="lg:w-[190px] lg:h-[190px] w-[142px] h-[142px] absolute top-0 left-0 transform transition-all duration-[0.2s] ease-linear fill-white"
                  :class="changeSpinnerStyle ? 'rotate-[60deg]' : ''"
                  :color="changeSpinnerStyle ? 'black' : 'white'"
                />
              </div>
            </div>
            <div
              class="md:flex hidden text-[#999999] gap-10 justify-center items-center z-[1] pt-[60px] pb-10"
            >
              <HomeHeroFeatureIcon
                v-for="feature in features"
                :key="feature.id"
                :icon="feature.icon"
                :title="feature.title"
                size="desktop"
              />
            </div>
          </div>
        </div>
        <!-- <ClientOnly>
          <carousel
            :wrap-around="false"
            :autoplay="0"
            :items-to-show="1"
            :transition="500"
            :pauseAutoplayOnHover="true"
            napAlign="center"
            @init="handleInit"
            class="h-full hero_section_carousal"
          >
            <template v-if="show">
              <slide
                class="flex flex-col-reverse md:flex-row items-center justify-end md:justify-center overflow-hidden container-fluid h-full md:pt-[0px] pt-[0px] md:pb-[0px] pb-[0px]"
                v-for="(company, index) in companies"
                :key="index"
              >
                <div
                  :class="`w-full h-full max-w-[1280px] ${index}`"
                  v-html="company.description"
                ></div>
              </slide>
            </template>

            <template #addons>
              <Pagination class="!mt-0" />
            </template>
          </carousel>
        </ClientOnly> -->
      </div>
    </div>

    <div class="">
      <ServicesWhyChooseDevxhub
        class="relative md:pt-[80px] pt-16"
        :header-title="headerTitle"
        :core-values="fullTimes"
      />

      <div
        class="md:block hidden container-fluid we-are-devxhub-container pt-[199px] pb-[127px] relative overflow-hidden"
      >
        <div id="text1" class="whitespace-nowrap top-0 w-full">We are</div>
        <div
          id="counterWrap"
          class="md:!grid !hidden counter-wrap max-h-[337px]"
        >
          <div
            id="card1"
            class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow mb-[7.5vw] number">001</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Years of Service
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                5+
              </p>
            </div>
          </div>
          <div
            id="card2"
            class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">002</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Team members
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                55+
              </p>
            </div>
          </div>
          <div
            id="card3"
            class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">003</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Location
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                3+
              </p>
            </div>
          </div>
          <div
            id="card4"
            class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">004</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Ongoing Projects
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                20+
              </p>
            </div>
          </div>
          <div
            id="card5"
            class="counter-single-item counter-single-item-animation flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">005</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Happy Clients
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                200+
              </p>
            </div>
          </div>
        </div>
        <div id="text2" class="whitespace-nowrap bottom-0 w-full">Devxhub</div>
      </div>

      <div
        class="md:hidden block container-fluid mt-[64px] relative overflow-hidden"
      >
        <p id="text3" class="w-full">We are</p>
        <p id="text4" class="w-full">Devxhub</p>
        <div class="md:hidden counter-wrap-mobile mt-[62px]">
          <div
            class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">001</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Years of Service
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                5+
              </p>
            </div>
          </div>
          <div
            class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">002</p>
                <p
                  class="lg:pb-[210px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Team members
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                55+
              </p>
            </div>
          </div>
          <div
            class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">003</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Location
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                3+
              </p>
            </div>
          </div>
          <div
            class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">004</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Ongoing Projects
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                20+
              </p>
            </div>
          </div>
          <div
            class="counter-single-item-mobile flex flex-col text-[#1D1A20] lg:py-[20px] py-4"
          >
            <div class="flex-grow">
              <div class="flex-col lg:px-[20px] px-4 md:h-[180px] lg:h-auto">
                <p class="flex-grow number">005</p>
                <p
                  class="lg:pb-[20px] pb-4 md:pb-2 lg:text-2xl md:text-lg text-base flex items-end h-[48px]"
                >
                  Happy Clients
                </p>
              </div>
              <p
                class="lg:px-[20px] px-4 xl:pt-[22.993px] lg:pt-[15px] pt-[11px] border-solid border-[#ececec] border-t xl:text-6xl text-[38px] number large-number"
              >
                200+
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- <div
        class="container-fluid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 py-[50px] md:pt-[136px] pt-[96px] justify-center gap-10"
      >
        <div
          class="text-center items-center lg:text-left flex sm:justify-normal justify-self-center sm:justify-self-start w-[260px] sm:w-full space-x-4 space-y-1 lg:space-y-2"
          v-for="item in overview"
          :key="item.id"
        >
          <div class="mt-3">
            <img
              class="w-12 lg:min-w-[48px] h-12 lg:min-h-[48px]"
              :src="item.image"
              :alt="item.field"
              srcset=""
            />
          </div>
          <div class="flex flex-col space-y-3">
            <h2
              class="text-3xl lg:text-[40px] font-semibold text-[#FDB21D] text-left"
            >
              {{ item.fieldValue }} +
            </h2>
            <p class="text-xl lg:text-2xl text-white text-left">
              {{ item.field }}
            </p>
          </div>
        </div>
      </div> -->

      <div
        class="container-fluid md:pt-[81px] pt-[58px] grid grid-cols-3 lg:grid-cols-7 items-start gap-4 sm:gap-7 text-base text-white sm:text-2xl font-semibold text-center"
      >
        <div class="flex flex-col items-center space-y-7">
          <div
            class="flex justify-center items-center w-24 h-24 sm:h-36 sm:w-36 xl:w-40 xl:h-40"
          >
            <img
              class="w-full h-full rounded-full"
              loading="lazy"
              src="~/assets/img/landing/1.svg"
              alt="qualified Engineer"
            />
          </div>
          <p class="w-24 sm:w-36 xl:w-40">Qualified Engineers</p>
        </div>
        <div class="flex items-center justify-center">
          <img
            class="pt-12 sm:pt-12 w-24 sm:w-32 h-14 sm:h-24 md:h-26 lg:h-[90px]"
            loading="lazy"
            src="~/assets/img/landing/h-line.svg"
            alt="h-line"
          />
        </div>
        <div class="flex flex-col items-center space-y-7">
          <div
            class="flex justify-center items-center w-24 h-24 sm:h-36 sm:w-36 xl:w-40 xl:h-40"
          >
            <img
              class="w-full h-full rounded-full"
              loading="lazy"
              src="~/assets/img/landing/2.svg"
              alt="dedicated_team"
            />
          </div>
          <p class="w-24 sm:w-36 xl:w-40">Dedicated Team</p>
        </div>
        <div class="hidden lg:flex items-center justify-center">
          <img
            class="pt-12 sm:pt-20 w-24 sm:w-32 h-14 md:h-26 lg:h-[90px]"
            loading="lazy"
            src="~/assets/img/landing/h-line.svg"
            alt="h-line"
          />
        </div>
        <div class="lg:hidden flex items-center justify-center">
          <img
            class="h-24 sm:h-32 w-2"
            loading="lazy"
            src="~/assets/img/landing/v-line.svg"
            alt="v-line"
          />
        </div>
        <div class="lg:hidden"></div>
        <div class="lg:hidden flex items-center justify-center">
          <img
            class="h-24 sm:h-32 w-2"
            loading="lazy"
            src="~/assets/img/landing/v-line.svg"
            alt="v-line"
          />
        </div>
        <div class="flex flex-col items-center space-y-7">
          <div
            class="flex justify-center items-center w-24 h-24 sm:h-36 sm:w-36 xl:w-40 xl:h-40"
          >
            <img
              class="w-full h-full rounded-full hidden lg:block"
              loading="lazy"
              src="~/assets/img/landing/3.svg"
              alt="Devxhub Continuous Supervision"
            />
            <img
              class="w-full h-full rounded-full block lg:hidden"
              loading="lazy"
              src="~/assets/img/landing/4.svg"
              alt="Devxhub Collaborative Process"
            />
          </div>
          <p class="w-24 sm:w-36 xl:w-40">
            <span class="hidden lg:block">Collaborative Process</span>
            <span class="block lg:hidden">Continuous Supervision</span>
          </p>
        </div>
        <div class="flex items-center justify-center">
          <img
            class="pt-12 sm:pt-20 w-24 sm:w-32 h-14 sm:h-24 md:h-26 lg:h-[90px]"
            loading="lazy"
            src="~/assets/img/landing/h-line.svg"
            alt="h-line"
          />
        </div>
        <div class="flex flex-col items-center space-y-7">
          <div
            class="flex justify-center items-center w-24 h-24 sm:h-36 sm:w-36 xl:w-40 xl:h-40"
          >
            <img
              class="w-full h-full rounded-full hidden lg:block"
              loading="lazy"
              src="~/assets/img/landing/4.svg"
              alt="Devxhub Continuous Supervision"
            />
            <img
              class="w-full h-full rounded-full block lg:hidden"
              loading="lazy"
              src="~/assets/img/landing/3.svg"
              alt="Devxhub Collaborative Process"
            />
          </div>
          <p class="w-24 sm:w-36 xl:w-40">
            <span class="hidden lg:block">Continuous Supervision</span>
            <span class="block lg:hidden">Collaborative Process</span>
          </p>
        </div>
      </div>

      <HomeService
        id="ourServices"
        class="container-fluid md:pt-[200px] pt-[64px]"
      />
      <HomeWhatMakeUs class="container-fluid md:pt-[180px] pt-[64px]" />
      <!-- <HomeTechnologyStack class="container-fluid pt-[150px]" /> -->
      <!-- <HomeExpertiseArea class="container-fluid md:pt-[180px] pt-[64px]" /> -->
      <HomeExpertiseSection class="container-fluid" />
      <!-- <HomeIndustryExpertise class="container-fluid pt-[150px]" /> -->
      <ServicesFullStackIndustries class="container-fluid" />
      <!-- <HomeOurHospitality class="container-fluid" /> -->
      <!-- <HomeHowWeWork v-if="config.public.workflow === 'dev'" class="container-fluid pt-[150px]" /> -->
      <!-- <HomeWhatMakeUs v-if="config.public.workflow === 'dev'" class="container-fluid pt-[150px]" /> -->
      <HomeTrustedCompanies class="md:pt-[180px] pt-[64px]" />
      <HomeTestimonialCarousel class="md:pt-[180px] pt-[64px]" />
      <ServicesOurBenefits class="container-fluid md:pt-[100px] pt-[64px]" />
      <HomeFaq :faqData="faqData" />
      <!-- <HomeLatestBlog class="container-fluid" /> -->
    </div>
  </div>
</template>

<style scoped>
/* .intro {
  background-image: url("~/assets/img/landing/intro.webp");
  background-origin: center;
  background-repeat: no-repeat;
  background-size: cover;
} */

@media (max-width: 1280px) and (min-width: 768px) {
  /* .intro-image {
    height: 75vw;
  } */
}
@media (max-width: 767px) {
  .sun-image {
    width: 29px;
    height: 30px;
  }
}

.counter-wrap {
  z-index: 1;
  grid-column-gap: 24px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 0, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}
.counter-single-item {
  z-index: 0;
  background-color: #fff;
  width: 100%;
  /* transition: all 0.5s ease-in-out 0.5s; */
  position: relative;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  transition: transform 0.5s ease-in-out 0.5s, clip-path 0.5s ease-in-out,
    background-color 0.5s ease-in-out, color 0.5s ease-in-out;
}
.counter-single-item .number {
  @apply mb-[7vw] text-xl;
  font-family: "Neue Machina", sans-serif;
}
.counter-single-item .large-number {
  @apply mb-[0vw] xl:text-[54px] text-[38px] pt-5 xl:leading-[50px] leading-[34px];
  font-family: "Neue Machina", sans-serif;
}

#card1 {
  transform: translateX(211%);
  /* background: red; */
}

#card2 {
  transform: translateX(106%);
  /* background: blue; */
}

#card4 {
  transform: translateX(-106%);
  /* background: orange; */
}

#card5 {
  transform: translateX(-211%);
  /* background: yellow; */
}

#card1,
#card2,
#card4,
#card5 {
  opacity: 1;
  /* background: red; */
}

#card3 {
  z-index: 1;
}

#card1.final,
#card2.final,
#card4.final,
#card5.final {
  transform: translateX(0);
  opacity: 1;
  /* background: red; */
}
.counter-single-item:hover {
  background-color: black;
  color: white;
  clip-path: polygon(5% 5%, 95% 2%, 95% 95%, 5% 98%);
}
.counter-wrap.final {
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
}

.counter-single-item-mobile {
  background-color: #fff;
  width: 100%;
  /* transition: all 0.5s ease-in-out 0.5s; */
  position: relative;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  transition: clip-path 0.5s ease-in-out, background-color 0.5s ease-in-out,
    color 0.5s ease-in-out;
}
.counter-single-item-mobile .number {
  @apply mb-[7vw];
  font-family: "Neue Machina", sans-serif;
}
.counter-single-item-mobile .large-number {
  @apply mb-[0vw] xl:text-[54px] text-[38px] pt-5 xl:leading-[50px] leading-[34px];
  font-family: "Neue Machina", sans-serif;
}
.counter-single-item-mobile:hover {
  background-color: black;
  color: #f1f1f2;
  clip-path: polygon(5% 5%, 95% 2%, 95% 95%, 5% 98%);
}

.three-grid-item {
  /* z-index: 0; */
  grid-column-gap: 94px;
  grid-row-gap: 106px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
  position: relative;
}

@media screen and (max-width: 991px) {
  .counter-wrap {
    grid-column-gap: 10px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .three-grid-item {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}
@media screen and (max-width: 767px) {
  .counter-wrap-mobile {
    white-space: nowrap;
    z-index: 1;
    grid-column-gap: 24px;
    grid-row-gap: 16px;
    grid-template-rows: auto;
    grid-auto-columns: 1fr;
    display: grid;
    position: relative;
    overflow: hidden;
    grid-template-columns: 1fr 1fr 1fr;
  }
  .three-grid-item {
    grid-row-gap: 88px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 479px) {
  .counter-wrap-mobile {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    grid-template-columns: 1fr 1fr;
  }
  .three-grid-item {
    grid-template-columns: 1fr;
  }
}

.title-aimation {
  transform: translate3d(0px, -100%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, -7deg);
  transform-style: preserve-3d;
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.title-aimation.final {
  transform: translate3d(0px, 0%, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  opacity: 1;
}
.descrip-aimation {
  transform: translateY(70px);
  opacity: 0;
  transition: all 0.8s ease-in-out;
}
.descrip-aimation.final {
  transform: translateY(0px);
  opacity: 1;
}
#text1 {
  padding-left: 16.667vw;
  @apply lg:text-[200px] md:text-[120px] lg:mb-[-40px] md:mb-[-30px];
  font-weight: 700;
  line-height: 100%;
  color: #999;
  transform: translate3d(0vw, 6.417vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
  transition: all 0.6s ease-in-out;
}
#text2 {
  text-align: right;
  margin-top: -66px;
  padding-right: 16.042vw;
  @apply lg:text-[200px] md:text-[120px] lg:mt-[-66px] md:mt-[-44px];
  font-weight: 700;
  line-height: 100%;
  color: #999;
  transform: translate3d(0vw, -6.417vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
  transition: all 0.6s ease-in-out;
}
#text1.final {
  transform: translate3d(-10.417vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
}
#text2.final {
  transform: translate3d(10.417vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
  will-change: transform;
}
@media (max-width: 767px) {
  #text3 {
    padding-left: 0px;
    margin: 0px;
    @apply text-[52px] leading-[60px];
    font-weight: 700;
    color: #999;
    transform: translate3d(-10.417vw, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
    transition: all 0.6s ease-in-out;
  }
  #text4 {
    padding-left: 32.346px;
    margin: 0px;
    text-align: left;
    @apply text-[52px] leading-[60px];
    font-weight: 700;
    color: #999;
    transform: translate3d(10.417vw, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
    transition: all 0.6s ease-in-out;
  }
  #text3.final {
    transform: translate3d(0vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
  }
  #text4.final {
    transform: translate3d(0vw, 0vw, 0px) scale3d(1, 1, 1) rotateX(0deg)
      rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-style: preserve-3d;
    will-change: transform;
  }
}
.rotate-animation-zero {
  transform: rotate(0deg);
}
.rotate-animation {
  /* transform: rotate(0deg); */
  animation: spin 10s linear infinite;
}
@keyframes spin {
  100% {
    transform: rotate(-360deg);
  }
}
.we-are-devxhub-container {
  background-image: url("/public/landing/we-are-devxhub.svg");
  background-size: cover;
}
@media (max-width: 1279px) {
  .text-responsive {
    font-size: 48px;
    line-height: 52px;
  }
}
</style>

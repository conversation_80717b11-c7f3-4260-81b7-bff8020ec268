<script setup>
const title = "Our Work | Devxhub’s Successful Projects";
const description =
  "Discover how Devxhub has helped clients achieve their goals through innovative software solutions across various industries.";
const keyWords =
  "Devxhub projects, case studies, software solutions, client success stories";

useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const caseStudies = ref([
  {
    id: 1,
    title: "Cartzie",
    description: "On Demand Food Delivery",
    image: "/work/cartzie.png",
    tags: ["Mobile App", "UI Design", "E-commerce"],
    link: "/case-study/cartzie",
  },
  {
    id: 2,
    title: "Panther",
    description: "Website Design for SIEM Platform",
    image: "/work/panther.png",
    tags: ["UX Research", "UI Design", "Cybersecurity"],
    link: "/case-study/panther",
  },
  {
    id: 3,
    title: "Flixpay",
    description:
      "Pay after delivery options and installment plans in a smooth one-click purchase experience.",
    image: "/work/flixpay.png",
    tags: ["App Design", "User Research", "Fintech"],
    link: "/case-study/flixpay",
  },
]);

const viewPortEnter = () => {
  const boxAnimation = document.querySelectorAll(".box-up-animation");

  const boxAnimationObserver = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0% 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );
  boxAnimation.forEach((item) => {
    boxAnimationObserver.observe(item);
  });
};

onMounted(() => {
  viewPortEnter();
});
</script>

<template>
  <div class="md:pt-40 lg:pt-44 pt-32 container-fluid text-[#F1F1F2]">
    <div class="box-up-animation">
      <h1
        class="hero_section_carousal_aeonik max-w-[1280px] text-4xl md:text-5xl lg:text-7xl md:leading-[68px] lg:leading-[88px] font-[700]"
      >
        <span class="text-[#999]">Our</span
        ><span class="instrument-italic"> Case Study</span>
      </h1>

      <p class="subtitle">
        An Experienced IT Staff Augmentation and Software Development Company
        building high-end products that grow your business exponentially.
      </p>

      <div class="relative w-fit">
        <img
          src="/work/pioo.png"
          alt="AI-Powered Assistant"
          class="mb-5 mt-12"
        />

        <div
          class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
        >
          <ClientOnly>
            <fa :icon="['fas', 'plus']" />
          </ClientOnly>
        </div>
      </div>
    </div>

    <div class="flex flex-col">
      <h2 class="text-4xl">Pioo</h2>
      <p class="short-desc mt-2.5 mb-6">
        Pioo is an AI-powered assistant developed similar to ChatGPT & DeepSeek,
        with affordable cost.
      </p>

      <div class="flex">
        <div
          class="px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
        >
          AI-Assistant
        </div>
        <div
          class="px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
        >
          AI/ML
        </div>
        <div
          class="px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
        >
          Web Development
        </div>
      </div>

      <div
        class="grid grid-cols-1 md:grid-cols-2 gap-x-10 md:gap-x-20 gap-y-10 md:gap-y-[88px] mt-20"
      >
        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[540px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/muslim-times-pro.png"
              alt="Muslim Times Pro"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Muslim Times Pro</h2>
            <p class="short-desc mt-2.5 mb-6">
              Muslim Times Pro is a mobile application designed to assist
              Muslims in practicing their faith with precision and convenience.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                App Development
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Flutter
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                iOS & Android
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[716px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/ai-leap.png"
                alt="AILEAP"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">AILEAP</h2>
              <p class="short-desc mt-2.5 mb-6">
                AILEAP is a comprehensive AI platform that provides users with
                access to AI tools, news, videos, and custom GPT models.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  AI/ML
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-fit md:justify-self-end h-auto overflow-hidden md:col-span-2 box-up-animation"
        >
          <div class="relative h-fit lg:h-[640px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/analytics-tools.png"
              alt="analytics tools"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Analytics Tools</h2>
            <p class="short-desc mt-2.5 mb-6">
              This is a reporting platform for the Service & Maintenance
              department, enabling data import via Excel/CSV to track
              maintenance schedules, service activities, contracts, travel time,
              and technician performance through summary tables and visual
              reports.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Web Development
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                React
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Full Stack Development
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[716px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/farenow.png"
              alt="Web & Mobile Application Development"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">FareNow</h2>
            <p class="short-desc mt-2.5 mb-6">
              FareNow is a platform that connects users with a wide range of
              local service providers, including contractors, cleaners,
              landscapers, and more.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Full Stack Development
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                AWS
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                App Development
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[540px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/vue-storefront.png"
                alt="e-commerce website"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Vue Storefront</h2>
              <p class="short-desc mt-2.5 mb-6">
                Vue Storefront is an e-commerce website that offers a versatile
                platform for selling a wide range of goods.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  E-Commerce
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Next.js
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Web Development
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-fit md:justify-self-end h-auto overflow-hidden md:col-span-2 box-up-animation"
        >
          <div class="relative h-fit lg:h-[640px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/hospital-note1.png"
              alt="Full Stack Development"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Hospital Management System</h2>
            <p class="short-desc mt-2.5 mb-6">
              Hospital Management System appears to be associated with My Health
              & Wellbeing Clinic. This platform serves as a portal for patients
              and healthcare professionals to access services and manage
              health-related information.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                HealthCare
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Full Stack Development
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/erpgap.png"
              alt="open-source technologies enterprise resource planning (ERP) solutions"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">ERPGAP</h2>
            <p class="short-desc mt-2.5 mb-6">
              ERPGAP specializes in open-source technologies, enterprise
              resource planning (ERP) solutions, and headless eCommerce
              platforms.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Astro.build
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Odoo
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                ERP
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Django
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/e-commerce-pos-system.png"
                alt="POS is a point-of-sale system designed to streamline business transactions"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">E-commerce POS System</h2>
              <p class="short-desc mt-2.5 mb-6">
                E-commerce POS System is a point-of-sale system designed to
                streamline business transactions, inventory management, and
                customer interactions.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Retail
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  App Development
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  React-Native
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/sk8y.png"
              alt="social platform for skateboarders to find nearby skateparks Full stack development"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Sk8y</h2>
            <p class="short-desc mt-2.5 mb-6">
              A social platform for skateboarders to find nearby skateparks,
              connect with local skaters, chat, follow profiles, and share
              experiences.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Social platform
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                App design
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Full Stack Development
              </div>
            </div>
          </div>
        </div> -->

        <!-- <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/ascent-u.png"
                alt="services and practices designed to support mental, emotional, spiritual, and physical well-being, Full stack development"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Ascent U</h2>
              <p class="short-desc mt-2.5 mb-6">
                Service Provides a variety of services and practices designed to
                support mental, emotional, spiritual, and physical well-being.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Web Design
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Laravel
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  React
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/secnt-q.png"
              alt="ScentQ is a subscription service offering monthly perfume samples for as low as possible"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">ScentQ</h2>
            <p class="short-desc mt-2.5 mb-6">
              ScentQ is a subscription service offering monthly perfume samples
              for as low as possible, allowing users to discover new fragrances
              without committing to full-sized bottles.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                E-commerce
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Vue
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Laravel
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/archiving-system.png"
                alt="Compliance & Security Enterprise Solutions"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Archiving System</h2>
              <p class="short-desc mt-2.5 mb-6">
                Archiving System securely backs up and archives communications
                for compliance and easy access.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Python/Django
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Vue.js/Nuxt.js
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  IT & Cybersecurity
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/international-wellbeing-system.png"
              alt="App Development"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">International Wellbeing System</h2>
            <p class="short-desc mt-2.5 mb-6">
              International Wellbeing System is a versatile platform designed
              for administrators, HR managers, and Employees, offering
              comprehensive tools for HR management and company administration.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Benefit Management
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Laravel
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                HR Management
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/complete-e-commerce-system.png"
                alt="Web Development"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Complete E-commerce System</h2>
              <p class="short-desc mt-2.5 mb-6">
                Complete E-commerce System is a marketplace for buying and
                selling various electronic devices while promoting environmental
                responsibility.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Marketplace
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  E-Commerce
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Web Development
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/go-velo.png"
              alt="Web Development Mobility Solutions Transportation"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Go-Velo</h2>
            <p class="short-desc mt-2.5 mb-6">
              Go-Velo is an online bike rental platform that connects bike
              owners and renters. Bike owners can create a shop to list their
              bikes and bike parts for rent, while people can conveniently rent
              these bikes and parts.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Mobility Solutions
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Docker
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Web Development
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/finding-thief.png"
                alt="Safety Flutter Website Design Transportation"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out transform-origin-center"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Finding Thief</h2>
              <p class="short-desc mt-2.5 mb-6">
                Finding thief is a mobile application designed to enhance
                personal safety by allowing users to mark unsafe zones and
                inform their nearest contacts of their live location.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Safety
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Flutter
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Mobile App
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/customer-experience-system.png"
              alt="Full Stack Development Vue.js/Nuxt.js AWS"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Customer Experience System</h2>
            <p class="short-desc mt-2.5 mb-6">
              Customer Experience System is a collaborative platform to improve
              and co-innovate customer experience (CX) across various
              industries. It connects CX and digital professionals worldwide to
              exchange best practices and knowledge and drive innovation.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Digital Innovation
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Docker
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Social Communication
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/SKMobileSchool.png"
                alt="App & Web Design and Development Online Educational Platform"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out transform-origin-center"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">SK Mobile School</h2>
              <p class="short-desc mt-2.5 mb-6">
                SK Mobile School provides training in mobile servicing through
                online and hands-on courses.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Edu-Tech
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Research
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  App & Web Design
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/HrPayroll.png"
              alt="attendance tracking, payroll automation, and benefits administration C# Angular"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">HR & Payroll System</h2>
            <p class="short-desc mt-2.5 mb-6">
              A comprehensive platform designed to streamline HR management and
              payroll processing. It enables employee record management,
              attendance tracking, payroll automation, and benefits
              administration.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                ERP/HRM
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                CRM
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                ASP.NET
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/bastion.png"
                alt="Google Product Flutter Website Design"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out transform-origin-center"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Bastion Admin</h2>
              <p class="short-desc mt-2.5 mb-6">Admin Panel</p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Research
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Shadcn
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Google Product
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/newspaper.png"
              alt="Full Stack Development Web Development Mobility Solutions"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Newspaper</h2>
            <p class="short-desc mt-2.5 mb-6">
              Newspaper is a prominent daily news website based in Haiti,
              providing comprehensive coverage of local and international news
              since 1898. The platform offers a wide range of content, including
              articles on politics, economy, culture, sports, etc.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                News-Portal
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Laravel
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Node.js
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/automatic-parking-system.png"
                alt="Animation Flutter Website Design"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out transform-origin-center"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Automatic Parking System</h2>
              <p class="short-desc mt-2.5 mb-6">
                Automatic Parking System optimizes parking with a
                space-efficient, automated pallet system.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Manufacture
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  React Gatsby
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Animation
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/Proeducare.png"
              alt="Edu-Tech | E-Learning Management | Web Development
"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">Pro Edu care</h2>
            <p class="short-desc mt-2.5 mb-6">
              ProEdu Care provides professional development courses online and
              in-person, focusing on practical skills enhancement and career
              growth.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                E-Learning
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Edu-Tech
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Web Development
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/parking-management-system.png"
                alt="3D Design Flutter Website Design React.js"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out transform-origin-center"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Parking Management system</h2>
              <p class="short-desc mt-2.5 mb-6">
                Modern Parking Management System
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Manufacture
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  UI Design
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  3D Design
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full h-auto overflow-hidden box-up-animation">
          <div class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/O-e-Health.png"
              alt="HealthCare | Django | React.js/Next.js | Full Stack Development"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">O-e-Health</h2>
            <p class="short-desc mt-2.5 mb-6">
              TeethWallet is a digital platform designed to improve oral health
              by centralizing and streamlining medical and dental clinical
              records.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                HealthCare
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                React.js/Next.js
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Web Development
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full h-auto flex md:justify-end overflow-hidden box-up-animation"
        >
          <div class="w-fit">
            <div
              class="relative h-fit lg:h-[624px] overflow-hidden lg:mb-4 mb-3"
            >
              <img
                src="/work/patient-monitoring-system.png"
                alt="Health Care | Full Stack Development  Website Design"
                class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out transform-origin-center"
              />

              <div
                class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
              >
                <ClientOnly>
                  <fa :icon="['fas', 'plus']" />
                </ClientOnly>
              </div>
            </div>

            <div>
              <h2 class="text-3xl md:text-4xl">Patient Monitoring System</h2>
              <p class="short-desc mt-2.5 mb-6">
                Patient monitoring System: Live healthier for longer! DoctorBox
                is your digital health marketplace that makes it easier for you
                to access medical diagnostics.
              </p>
              <div class="flex">
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Health Care
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Mobile App
                </div>
                <div
                  class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
                >
                  Full Stack Development
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          class="w-full md:justify-self-end h-auto overflow-hidden md:col-span-2 box-up-animation"
        >
          <div class="relative h-fit overflow-hidden lg:mb-4 mb-3">
            <img
              src="/work/pos-system.png"
              alt="washburn"
              class="case-study-image object-cover h-full w-full hover:scale-110 transition-all duration-300 ease-in-out"
            />

            <div
              class="absolute bottom-5 right-5 bg-white text-black size-12 rounded-full text-[36px] flex items-center justify-center hidden"
            >
              <ClientOnly>
                <fa :icon="['fas', 'plus']" />
              </ClientOnly>
            </div>
          </div>

          <div>
            <h2 class="text-3xl md:text-4xl">POS System</h2>
            <p class="short-desc mt-2.5 mb-6">
              POS system repairs and solutions, offering services like
              diagnostics, cleaning, imaging, data destruction, and hardware
              procurement.
            </p>
            <div class="flex">
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-l-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Inventory
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Odoo
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                PHP
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Python
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Django
              </div>
              <div
                class="px-2 md:px-4 py-1 bg-gray-100 rounded-r-full text-[11px] sm:text-sm text-gray-600 h-9 md:h-10 content-center"
              >
                Full Stack Development
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.subtitle {
  @apply max-w-[981px] py-[32px] text-xl md:text-[28px] lg:text-[32px] md:leading-[36px] lg:leading-[42px] text-[#999];
}

.short-desc {
  color: #9b9b9a;
  font-size: 18px;
}

.box-up-animation {
  transition: all 0.9s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}

.box-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

<script setup>
const seoTitle = " MVP, SaaS & End-to-End Development Services";
const seoDescription =
  " Accelerate your startup with our MVP, SaaS, and end-to-end development solutions. We build scalable software specific to your business needs.";
//const websiteLogo = `${config.public.siteUrl}/logo.svg`;

useSeoMeta({
  title: () => seoTitle,
  ogTitle: () => seoTitle,
  description: () => seoDescription.slice(0, 300),
  ogDescription: () => seoDescription.slice(0, 300),
});

const heroTitle = `<p class="max-w-[1114px]"><span class="instrument-italic">MVP, SaaS & End-to-End </span> <span class="text-[#999]">Development That Launches and Scales Products Fast</span></p>`;
const heroDes = `<p class="max-w-[890px]">Devxhub helps organizations transform ideas into high-impact software products from MVPs to full-scale SaaS platforms, ready for launch, growth, and continuous improvement.
</p>`;

const showImage = false;

const heading = `<span class="text-[36px] font-bold leading-[48px] text-[#999]"><span class="instrument-italic">MVP, SaaS & End-to-End</span> Development Services</span>`;
const features = ref([
  {
    id: 1,
    icon: "/images/homepage/hero/circle.svg",
    title: "Full Transparency",
  },
  {
    id: 2,
    icon: "/images/homepage/hero/infinite.svg",
    title: "Expert Team",
  },
  {
    id: 3,
    icon: "/images/homepage/hero/graph.svg",
    title: "Daily & Weekly Reports",
  },
  {
    id: 4,
    icon: "/images/homepage/hero/loop.svg",
    title: "Flexible & On-Demand",
  },
]);
const expertiseAreas = ref([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/AI-Powered Mobile Apps.png",
    text: "MVP Development",
    description: `
      <ul>
        <li>- Product ideation and validation</li>
        <li>- Market research and competitor analysis</li>
        <li>- Rapid prototyping</li>
        <li>- UI/UX design focused on core features</li>
        <li>- Agile development cycles</li>
        <li>- Basic backend and frontend development</li>
        <li>- User testing and feedback integration</li>
        <li>- Deployment on cloud or app stores</li>
        <li>- Post-launch support for iteration and scaling</li>
      </ul>`,
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/iOS App Development.png",
    text: "SaaS Development",
    description: `
      <ul>
        <li>- Multi-tenant architecture design</li>
        <li>- Subscription and billing integration</li>
        <li>- Scalable cloud infrastructure setup</li>
        <li>- User management and authentication</li>
        <li>- API development and third-party integrations</li>
        <li>- Data security and compliance (GDPR, HIPAA, etc.)</li>
        <li>- Continuous deployment and monitoring</li>
        <li>- Analytics and reporting dashboards</li>
        <li>- Customer support tools integration</li>
        <li>- Mobile and web responsive design</li>
      </ul>`,
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/Android App Development.png",
    text: "End-to-End Development",
    description: `
      <ul>
        <li>- Full product lifecycle management</li>
        <li>- Business analysis and requirements gathering</li>
        <li>- Comprehensive UI/UX design</li>
        <li>- Frontend and backend development</li>
        <li>- Quality assurance and automated testing</li>
        <li>- DevOps and CI/CD pipeline setup</li>
        <li>- Cloud infrastructure and database management</li>
        <li>- Security audits and compliance</li>
        <li>- Post-launch maintenance and feature updates</li>
        <li>- Marketing and product growth consulting</li>
      </ul>`,
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const benefitstitle1 = `<span class="instrument-italic">Benefits</span> <span class="text-[#999]">of</span>`;
const benefitstitle2 = `MVP, SaaS & End-to-End Development`;
const benefits = ref([
  {
    img: "/services/benefits/Asset_1.svg",
    title: "Faster Time to Market",
    description: `We help you launch quicker, test faster, and iterate based on honest feedback—giving you a head start on your competition.`,
  },
  {
    img: "/services/benefits/Asset_2.svg",
    title: "Flexible & Lean Start",
    description: `Build only what's essential for launch. Reduce upfront investment while keeping room for iterative enhancements post-validation.`,
  },
  {
    img: "/services/benefits/Asset_3.svg",
    title: "Built to Scale",
    description: `Our SaaS architecture supports growth, multi-tenancy, global reach, and evolving business models as your user base expands.`,
  },
  {
    img: "/services/benefits/Asset_4.svg",
    title: "Better User Retention",
    description: `UX-first design, seamless onboarding, and performance-focused engineering mean happier users and lower churn.`,
  },
  {
    img: "/services/benefits/Asset_5.svg",
    title: "Reduced Technical Debt",
    description: `We build right from the start. Clean code, modular systems, and cloud-native setups reduce long-term maintenance pain.`,
  },
  {
    img: "/services/benefits/Asset_6.svg",
    title: "Access to Cutting-Edge Tech",
    description: `Subscription models, trials, analytics, and admin tools are baked in, so you're ready to acquire and monetize from launch.`,
  },
]);

const extensionProcesses = ref([
  {
    point: "01",
    title: "Discovery Call",
    description:
      "Tell us your idea. We’ll listen, ask questions, and brainstorm together.",
  },
  {
    point: "02",
    title: "Scope & Planning",
    description: "Define features, timelines, and budget with full clarity.",
  },
  {
    point: "03",
    title: "Design & Development",
    description: "Watch your app come to life, step by step",
  },
  {
    point: "04",
    title: "Testing & Refinement",
    description: "We fix bugs before your users ever see them.",
  },
]);
const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "What's the difference between an MVP and a complete SaaS build?",
    description: `<p>An MVP is the leanest version of your product to validate its concept. A full SaaS build includes complete functionality, scalability, and monetization tools.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "Is this service only for startups?",
    description: `<p>No, we help enterprises launch new products or transform internal tools through SaaS and end-to-end development services.</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "How fast can you build an MVP?",
    description: `<p>Most MVPs take 4 to 10 weeks, depending on complexity. We aim for speed without sacrificing quality.</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "Do you offer design and branding, too?",
    description: `<p>Yes. We provide UI/UX design, product branding, and design systems to support a professional,polished launch.</p>`,
  },
  {
    id: 5,
    slectedItem: false,
    title: "Can you help us raise investment after the MVP?",
    description: `<p>We build investor-ready MVPs with pitch decks, analytics, and product documentation to support your fundraising.</p>`,
  },
  {
    id: 6,
    slectedItem: false,
    title: "What platforms do you build SaaS on?",
    description: `<p>We use scalable stacks like React, Node.js, Python, PostgreSQL, AWS, Firebase, and more tailored to your product's needs.</p>`,
  },
  {
    id: 7,
    slectedItem: false,
    title: "Do you support our product after launch?",
    description: `<p>Absolutely. We offer ongoing support, maintenance, and feature expansion to keep your product evolving and secure.</p>`,
  },
]);
const ourServicesHeight = () => {
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>

<template>
  <div class="md:pt-[196px] pt-[100px]">
    <div class="container-fluid">
      <OurworkingprocessHeroSection
        :heroTitle="heroTitle"
        :heroDes="heroDes"
        :showImage="showImage"
        :titleWrapper="`flex justify-center items-center`"
        :titleClass="`text-center`"
        :descWrapper="`flex justify-center items-center`"
        :descClass="`text-center`"
      >
        <template #button>
          <div
            class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
          >
            <NuxtLink
              to="/contact-us"
              class="text-up-animation md:px-[42px] px-[32px] h-[46px] text-[18px] font-medium leading-[28px] flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
              >Click For Discussion</NuxtLink
            >
          </div>
        </template>
      </OurworkingprocessHeroSection>
    </div>
    <div class="container-fluid">
      <div class="pb-[95px] border-b border-[#ffffff1a] max-w-[1280px]"></div>
      <div
        class="grid md:hidden grid-cols-2 md:grid-cols-4 text-[#999999] gap-x-10 gap-y-4 justify-items-start z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="mobile"
        />
      </div>
      <div
        class="hidden md:flex text-[#999999] gap-10 justify-center items-center z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="desktop"
        />
      </div>

      <div
        class="md:mt-[200px] mt-[100px] grid min-[992px]:grid-cols-[522px,1fr] min-[992px]:gap-[128px] gap-10 grid-cols-1 min-[992px]:items-center"
      >
        <img
          src="/public/services/mobile-app/new image.png"
          alt="about-mobile-application-development"
          class="max-h-[473px] max-w-[100%] mx-auto"
        />
        <div>
          <h2 class="sixth-h-tag">
            <span class="text-[#999]">What is Devxhub </span>
            <span class="instrument-italic"> MVP, SaaS & End-to-End</span>
            <span class="text-[#999]"> Development?</span>
          </h2>
          <p class="primary-p-tag mt-5 max-w-[630px]">
            Devxhub MVP, SaaS & End-to-End Development is a comprehensive
            service built to validate your product idea, design intuitive user
            experiences, and launch scalable SaaS platforms. We help businesses
            grow after launch, whether they are startups with a concept or
            established companies ready to expand.
          </p>
          <div
            class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
          >
            <NuxtLink
              to="/contact-us"
              class="text-up-animation md:px-[42px] px-[32px] h-[46px] text-[18px] font-medium leading-[28px] flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
            >
              Let's Talk
            </NuxtLink>
          </div>
        </div>
      </div>

      <div class="md:mt-[180px] mt-[100px]">
        <h2 class="text-up-animation" v-html="heading"></h2>
        <!-- <p class="pt-5 text-[#999] text-2xl font-normal text-up-animation">
          Our exclusive services are:
        </p> -->
      </div>

      <ServicesFullStackCommonDevelopment :expertiseAreas="expertiseAreas" />
    </div>

    <ServicesFullStackTechnologyStack class="container-fluid" />
    <ServicesMvpSassWhyChoose />

    <div class="container-fluid">
      <ServicesCommonSection
        class="pt-[184px]"
        :title1="benefitstitle1"
        :title2="benefitstitle2"
        :fullTimes="benefits"
      />

      <!-- <ServicesMobileAppCulture /> -->
      <div class="w-full mt-[180px]">
        <h1
          class="text-[48px] leading-[56px] font-bold text-[#999] text-center"
        >
          <span class="instrument-italic">Our Process: </span>Simple, Smart,
          Strategic
        </h1>
        <p
          class="max-w-[846px] mx-auto text-center pt-5 text-[24px] leading-[32px] font-normal text-[#999]"
        >
          Work with us simply! You don't just get mobile apps from us; we
          deliver digital solutions that grow your business.
        </p>
      </div>
      <div class="w-fill flex flex-col items-center mt-[80px]">
        <div class="four-grid-item">
          <div
            class="text-left-animation"
            v-for="(extensionProcess, index) in extensionProcesses"
            :key="index"
          >
            <div class="flex flex-col space-y-2.5">
              <div
                class="flex"
                :class="[
                  (index + 1) % 2 === 0 ? 'space-x-[11px]' : 'space-x-[12px]',
                ]"
              >
                <p class="primary-p-tag">
                  {{ extensionProcess.point }}
                </p>
                <div>
                  <h2
                    class="third-h-tag"
                  >
                    {{ extensionProcess.title }}
                  </h2>
                  <p class="secondary-p-tag">
                    {{ extensionProcess.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ServicesOurBenefits class="md:pt-[180px] pt-[64px]" />
    </div>

    <HomeFaq :faqData="ourServices" />
  </div>
</template>

<style scoped>
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.four-grid-item {
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid-item {
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}

.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

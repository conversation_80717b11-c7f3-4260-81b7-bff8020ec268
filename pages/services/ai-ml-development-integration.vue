<script setup>
const title = "AI/ML Development & Integration";
const intro = {
  title: "Best Custom Software Solutions for Enterprise Growth",
  description:
    " Automate your operations with our custom enterprise software. Our scalable solutions help you achieve your business goals.",
};

useSeoMeta({
  title: () => title,
  ogTitle: () => intro.title,
  description: () => intro.description.slice(0, 300),
  ogDescription: () => intro.description.slice(0, 300),
});
const heroTitle = `<p class="max-w-[1114px]"><span class="instrument-italic">AI/ML </span> <span class="text-[#999]">Development & <span class="instrument-italic">Integration </span> That Turns Data Into Actionable Intelligence </span></p>`;
const heroDes = `<p class="max-w-[890px]">Devxhub boosts your business performance through intelligent decisions, automated processes, and personalized user experiences.</p>`;

const showImage = false;

const heading = `<span class="text-[36px] font-bold leading-[48px] text-[#999]"><span class="instrument-italic">Mobile App Development</span> services</span>`;

const features = ref([
  {
    id: 1,
    icon: "/images/homepage/hero/circle.svg",
    title: "Full Transparency",
  },
  {
    id: 2,
    icon: "/images/homepage/hero/infinite.svg",
    title: "Expert Team",
  },
  {
    id: 3,
    icon: "/images/homepage/hero/graph.svg",
    title: "Daily & Weekly Reports",
  },
  {
    id: 4,
    icon: "/images/homepage/hero/loop.svg",
    title: "Flexible & On-Demand",
  },
]);

const expertiseAreas = ref([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/AI-Powered Mobile Apps.png",
    text: "AI-Powered Mobile Apps",
    description:
      "We use artificial intelligence to make your app smarter and improve user experience. AI also helps with automation and personal features.",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/iOS App Development.png",
    text: "iOS App Development",
    description:
      "Our apps are written in Swift and Objective-C. These apps are fast, secure, and work great on all Apple devices.",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/Android App Development.png",
    text: "Android App Development",
    description:
      "Our Android apps use Java and Kotlin and run smoothly on Android phones and tablets. We also help you publish on the Google Play Store.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/Cross-Platform App Development.png",
    text: "Cross-Platform App Development",
    description:
      "Having one app for iOS and Android saves time and money. We use tools that let us write code once and run it on both platforms.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea5",
    image: "/services/full-stack/Flutter App Development.png",
    text: "Flutter App Development",
    description:
      "With Flutter, we build apps that look and feel like native apps. It’s fast, modern, and works on both iOS and Android.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea6",
    image: "/services/full-stack/React Native App Development.png",
    text: "React Native App Development",
    description:
      "React Native helps us build apps quickly. We use JavaScript and reusable code, so you get a fast app that works on all devices.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea7",
    image: "/services/full-stack/Progressive Web Apps (PWAs).png",
    text: "Progressive Web Apps (PWAs)",
    description:
      "PWAs are websites that act like mobile apps. They work offline, load fast, and don’t need to be downloaded from an app store.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea8",
    image: "/services/full-stack/UI_UX Design for Mobile Apps.png",
    text: "UI/UX Design for Mobile Apps",
    description:
      "We design apps that are easy to use and pleasing to the eye. Good design helps users enjoy the app and stay longer.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea9",
    image: "/services/full-stack/Mobile App Maintenance & Support.png",
    text: "Mobile App Maintenance & Support",
    description:
      "Our team keeps your app up-to-date. We fix bugs, improve performance, and make sure it works on all new devices.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea10",
    image: "/services/full-stack/App Store Deployment & Optimization.png",
    text: "App Store Deployment & Optimization",
    description:
      "App publishing is what we do for you. We also use App Store Optimization (ASO) to improve visibility and get more downloads.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea11",
    image: "/services/full-stack/Enterprise Mobility Solutions.png",
    text: "Enterprise Mobility Solutions",
    description:
      "We build custom apps for businesses. These apps help teams work better, stay connected, and keep data safe.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea12",
    image: "/services/full-stack/Mobile Backend Development.png",
    text: "Mobile Backend Development",
    description:
      "Your app needs a strong backend. We build cloud-based systems, APIs, and databases to power your app’s features.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea13",
    image: "/services/full-stack/App Integration Services.png",
    text: "App Integration Services",
    description:
      "We connect your app to other tools. This includes payment systems, third-party APIs, and business software.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const mobileApplicationTitle = `<span>Devxhub Is Your </span><span class="instrument-italic text-nowrap">#1 Mobile App Development</span> Partner! How?`;

const augmentation = ref([
  {
    img: "/services/mobile-app/Asset_1.svg",
    title: "Team You Can Trust",
    description: `We hand-pick the top 2% developers with 1 to 25 years of experience. You get only the best, every time.`,
  },
  {
    img: "/services/mobile-app/Asset_2.svg",
    title: "Trusted and Top-Rated",
    description: `Five-star reviews on Clutch and Upwork show our clients love working with us and keep coming back.`,
  },
  {
    img: "/services/mobile-app/Asset_3.svg",
    title: "Always On Time",
    description: `98% of our projects hit the deadline. We respect your time and always deliver as promised.`,
  },
  {
    img: "/services/mobile-app/Asset_4.svg",
    title: "No Secrets",
    description: `We keep you in the loop from start to finish. No surprises, just clear, honest updates`,
  },
  {
    img: "/services/mobile-app/Asset_5.svg",
    title: "Speed & Ease",
    description: `Our apps are lightning-fast, responsive, and reliable. Your users will feel the difference.`,
  },
  {
    img: "/services/mobile-app/Asset_6.svg",
    title: "Global Reach",
    description: `Headquartered in Bangladesh, trusted globally. Our 50+ expert team partners with clients across the world.`,
  },
]);

const extensionProcesses = ref([
  {
    point: "01",
    title: "Talent Requirements",
    description:
      "Share your talent requirements and team size for the ultimate augmentation.",
  },
  {
    point: "02",
    title: "Contract Signing",
    description: "Contract signing for your IT Staff Augmentation.",
  },
  {
    point: "03",
    title: "Talent Allocation",
    description: "Provide ready-to-go teams as per your requirements.",
  },
  {
    point: "04",
    title: "Project Continues",
    description:
      "Augmented team will seamlessly align with your in-house team to ensure on-time project delivery.",
  },
]);
const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "How do you ensure my app is user-friendly?",
    description: `<p>We prioritize user experience (UX) from the beginning. All of our apps are designed and developed to be intuitive, easy to navigate, and exceptional.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "Do you provide app prototyping or MVP development?",
    description: `<p>Yes! We help you test and validate your app ideas with real users before going live with full-scale development. This reduces risks and ensures your app resonates with your target audience.</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "Can you build an app with augmented reality (AR) features?",
    description: `<p>Absolutely! Our developers are skilled in creating immersive AR experiences for your mobile apps. With real-time object tracking and 3D models, we can make your app awesome.</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "Do you work with international clients?",
    description: `<p>Yes, of course! While we're based in Bangladesh, we work with clients from all over the world. No matter where you are, we offer easy communication and top-quality apps.</p>`,
  },
  {
    id: 5,
    slectedItem: false,
    title: "How does your team ensure my app is secure?",
    description: `<p>Security is a top priority for us. We implement industry-standard encryption, secure data storage, and authentication protocols to protect your app and user data.</p>`,
  },
  {
    id: 6,
    slectedItem: false,
    title: "Can you integrate my app with existing systems or APIs?",
    description: `<p>Yes! We can integrate your mobile app with any existing systems or third-party APIs, making your app work smoothly with your current infrastructure.</p>`,
  },
  {
    id: 7,
    slectedItem: false,
    title: "How much does it cost to develop a mobile app with Devxhub?",
    description:
      "The cost depends on the complexity and features of your app. While we guarantee competitive rates, our quality is often 10 times better than that of US—or EU-based developers.",
  },
  {
    id: 8,
    slectedItem: false,
    title: "How do you ensure my app performs well across all devices?",
    description:
      "We perform extensive testing on 1,000+ devices to ensure cross-device compatibility. Across all major devices, your app will perform consistently and smoothly.",
  },
  {
    id: 9,
    slectedItem: false,
    title: "How can I track the progress of my app development?",
    description:
      "We believe in transparent communication. With regular updates, milestone reviews, and project management tools, you'll always know exactly where your app is.",
  },
]);
const ourServicesHeight = () => {
  // Loop through expertiseAreas to calculate heights
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>

<template>
  <div class="md:pt-[196px] pt-[100px]">
    <div class="container-fluid">
      <OurworkingprocessHeroSection
        :heroTitle="heroTitle"
        :heroDes="heroDes"
        :showImage="showImage"
        :titleWrapper="`flex justify-center items-center`"
        :titleClass="`text-center`"
        :descWrapper="`flex justify-center items-center`"
        :descClass="`text-center`"
      >
        <template #button>
          <!-- <div
            class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
          >
            <NuxtLink
              to="/contact-us"
              class="text-up-animation md:px-[42px] px-[32px] h-[46px] text-[18px] font-medium leading-[28px] flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
              >Let’s Build Your Custom Software</NuxtLink
            >
          </div> -->
        </template>
      </OurworkingprocessHeroSection>
    </div>
    <div class="container-fluid">
      <div class="pb-[95px] border-b border-[#ffffff1a] max-w-[1280px]"></div>
      <div
        class="grid md:hidden grid-cols-2 md:grid-cols-4 text-[#999999] gap-x-10 gap-y-4 justify-items-start z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="mobile"
        />
      </div>
      <div
        class="hidden md:flex text-[#999999] gap-10 justify-center items-center z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="desktop"
        />
      </div>

      <div
        class="md:mt-[180px] mt-[100px] grid min-[992px]:grid-cols-[522px,1fr] min-[992px]:gap-[128px] gap-10 grid-cols-1 min-[992px]:items-center"
      >
        <img
          src="/public/services/aiml/ai-ml.png"
          alt="about-mobile-application-development"
          class="max-h-[473px] max-w-[100%] mx-auto"
        />
        <div>
          <h2 class="sixth-h-tag">
            <p>
              <span class="text-[#999]">What is Devxhub </span>
              <span class="instrument-italic"> AI/ML</span>
              <span class="text-[#999]">
                Development &
                <span class="instrument-italic">Integration</span>?</span
              >
            </p>
          </h2>
          <p class="primary-p-tag mt-5 max-w-[630px]">
            Devxhub AI/ML Development & Integration is a comprehensive service
            that leverages artificial intelligence and machine learning to solve
            complex problems and automate key processes. We provide end-to-end
            development services, starting with data strategy, modeling,
            deployment, and optimization. With Devxhub, your software becomes
            smarter, faster, and more impactful.
          </p>
        </div>
      </div>
    </div>

    <ServicesAiMlDevelopment class="container-fluid" />

    <ServicesFullStackTechnologyStack class="container-fluid" />
    <ServicesAiMlWhyChoose />
    <ServicesAiMlCulture class="container-fluid" />

    <div class="pt-[176px] container-fluid">
      <h2 class="sixth-h-tag">
        <p class="text-[#999]">AI/ML Development</p>
        <p class="text-[#999]">
          <span class="instrument-italic"> Process</span>
        </p>
      </h2>
      <p class="fourth-p-tag mt-8 max-w-[630px]">
        We are committed to providing quality services. Our goal is to help our
        clients achieve their business.
      </p>
      <NuxtLink
        to="/contact-us#appointment"
        class="text-up-animation w-[197px] h-[46px] text-lg font-medium flex justify-center items-center text-[#1D1A20] mt-[40px] bg-[#FFD700] rounded-full self-start final"
        >Schedule a Call</NuxtLink
      >
      <div class="w-fill flex flex-col items-center mt-[80px]">
        <div class="four-grid">
          <div
            class="text-left-animation"
            v-for="(extensionProcess, index) in extensionProcesses"
            :key="index"
          >
            <div class="flex flex-col space-y-4">
              <div class="flex space-x-[10px]">
                <p class="primary-p-tag">
                  {{ extensionProcess.point }}
                </p>
                <h2 class="third-h-tag">
                  {{ extensionProcess.title }}
                </h2>
              </div>
              <p class="secondary-p-tag">
                {{ extensionProcess.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ServicesAiMlIndustries class="container-fluid" />
    <ServicesOurBenefits class="container-fluid md:pt-[180px] pt-[64px]" />

    <ServicesAiMlFaq class="container-fluid" />
  </div>
</template>

<style scoped>
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.four-grid-item {
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid-item {
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}

.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.four-grid {
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid {
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}
</style>

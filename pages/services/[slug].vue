<script setup>
const slug = useRoute().params.slug;
const { data: webApplicationDevelopment } = useFetch(`/api/services/${slug}`);
</script>

<template>
  <div
    v-if="webApplicationDevelopment"
    class="bg-[#1A1139] pt-[180px] container-fluid w-full h-full"
  >
    <div
      class="intro w-full lg:max-h-[1180px] lg:min-h-full min-h-screen flex justify-center"
    >
      <div
        class="flex flex-col w-full h-full max-w-[1600px] items-center justify-between pb-[110px]"
      >
        <div
          class="flex lg:flex-row lg:space-x-10 space-x-0 lg:space-y-0 space-y-10 flex-col w-full lg:justify-between lg:items-start items-center justify-center"
        >
          <div
            class="flex flex-col space-y-4 text-white lg:max-w-[629px] lg:w-1/2 w-full lg:items-start items-center"
          >
            <h1
              v-if="webApplicationDevelopment.serviceTitle"
              class="md:text-[56px] text-[40px] text-[#FDB21D] md:leading-[60px] leading-[50px] lg:text-left text-center font-semibold"
            >
              {{ webApplicationDevelopment.serviceTitle }}
            </h1>
            <p
              v-if="webApplicationDevelopment.subServiceTitle"
              class="md:text-2xl text-xl lg:text-left text-center"
            >
              {{ webApplicationDevelopment.subServiceTitle }}
            </p>
            <p
              v-if="webApplicationDevelopment.serviceDes"
              class="text-[#F0F0F0] md:text-xl text-base lg:text-left text-center"
            >
              {{ webApplicationDevelopment.serviceDes }}
            </p>
            <div class="w-full flex lg:justify-start justify-center !mt-[50px]">
              <NuxtLink
                to="/#ourServices"
                class="bg-[#FDB21D] text-[#1A1139] text-xl font-semibold rounded-full flex justify-center items-center px-[40px] py-3 max-h-[50px]"
              >
                Our Services
              </NuxtLink>
            </div>
          </div>
          <div class="lg:w-1/2 lg:max-w-[655px] w-full video-player-section">
            <img
              v-if="webApplicationDevelopment.image"
              :src="webApplicationDevelopment.image"
              :alt="webApplicationDevelopment.serviceTitle"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="container-section">
      <div
        v-if="webApplicationDevelopment.archivingSolutionsSection"
        class="flex flex-col items-center"
      >
        <h2
          v-if="webApplicationDevelopment.archivingSolutionsSection[0].title"
          class="text-[#FDB21D] text-5xl leading-[72px] max-w-[1032px] text-center font-semibold"
        >
          {{ webApplicationDevelopment.archivingSolutionsSection[0].title }}
        </h2>
        <div
          v-if="
            webApplicationDevelopment.archivingSolutionsSection[0]
              .archivingSolutions
          "
          class="grid grid-cols-2 lg:gap-x-[126px] gap-x-12 lg:gap-y-[110px] gap-y-[70px] mt-[110px]"
        >
          <div
            class="md:col-span-1 col-span-2 second-section flex flex-col p-8 space-y-4 md:items-start items-center md:text-left text-center cursor-pointer"
            v-for="(archivingSolution, index) in webApplicationDevelopment
              .archivingSolutionsSection[0].archivingSolutions"
            :key="index"
          >
            <div
              class="w-[92px] h-[92px] rounded-full bg-[#FDB21D] border-[0.3px] border-[#656565] flex justify-center items-center"
            >
              <ClientOnly v-if="archivingSolution.type === 'icon'">
                <fa
                  v-if="archivingSolution.img"
                  class="text-[34.55px]"
                  :icon="['fas', archivingSolution.img]"
                />
              </ClientOnly>
              <img
                v-else-if="
                  archivingSolution.img && archivingSolution.type !== 'icon'
                "
                class="w-[44px] h-[44px]"
                :src="archivingSolution.img"
                :alt="archivingSolution.title"
              />
            </div>
            <h2
              v-if="archivingSolution.title"
              class="text-[#FFFFFF] text-3xl font-medium !mt-[24px]"
            >
              {{ archivingSolution.title }}
            </h2>
            <p
              v-if="archivingSolution.description"
              class="text-[#FFFFFF] text-xl"
            >
              {{ archivingSolution.description }}
            </p>
          </div>
        </div>
      </div>
      <div class="mt-[150px] flex flex-col space-y-8">
        <h2
          class="text-[#FDB21D] md:text-[50px] text-[40px] md:leading-[60px] leading-[50px] text-center font-bold"
        >
          Ready to Learn More?
        </h2>
        <NuxtLink
          to="/#ourServices"
          class="text-[#1A1139] font-medium bg-[#FDB21D] text-xl rounded-full flex justify-center items-center px-[40px] py-3 max-h-[50px] mx-auto !mt-[50px]"
        >
          Our Services
        </NuxtLink>
      </div>
      <div
        v-if="webApplicationDevelopment.popularSectorsSection"
        class="mt-[150px] flex flex-col items-center space-y-[90px]"
      >
        <h2
          v-if="webApplicationDevelopment.popularSectorsSection[0].title"
          class="text-[#FDB21D] text-5xl leading-[72px] max-w-[1032px] text-center font-semibold"
        >
          {{ webApplicationDevelopment.popularSectorsSection[0].title }}
        </h2>
        <div
          v-if="webApplicationDevelopment.popularSectorsSection[0].popularSectors"
          class="flex flex-col space-y-[130px] text-white"
        >
          <div
            class="grid grid-cols-2 xl:gap-x-[227px] gap-x-[100px] md:gap-x-[50px] md:gap-y-0 gap-y-[50px] items-center"
            v-for="(popularSector, index) in webApplicationDevelopment
              .popularSectorsSection[0].popularSectors"
            :key="index"
          >
            <div
              v-if="index % 2 !== 0 && popularSector.img"
              class="md:col-span-1 col-span-2 md:block hidden"
            >
              <img
                width="725"
                height="414"
                :src="popularSector.img"
                :alt="popularSector.title"
              />
            </div>
            <div
              v-if="popularSector.img"
              class="md:col-span-1 col-span-2 md:hidden"
            >
              <img
                width="725"
                height="414"
                :src="popularSector.img"
                :alt="popularSector.title"
              />
            </div>
            <div class="md:col-span-1 col-span-2">
              <h2
                v-if="popularSector.title"
                class="lg:text-3xl text-2xl font-medium"
              >
                {{ popularSector.title }}
              </h2>
              <p
                v-if="popularSector.description"
                class="lg:text-xl text-lg mt-4"
              >
                {{ popularSector.description }}
              </p>
            </div>
            <div
              v-if="index % 2 === 0 && popularSector.img"
              class="md:col-span-1 col-span-2 md:block hidden"
            >
              <img
                width="725"
                height="414"
                :src="popularSector.img"
                :alt="popularSector.title"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-col items-center mt-[150px] container mx-auto">
        <HomeTrustedCompanies />
        <ClientOnly>
          <carousel
            v-if="show"
            v-bind="settings"
            :wrap-around="true"
            :autoplay="3000"
            :transition="500"
            :pauseAutoplayOnHover="true"
            :breakpoints="breakpoints"
          >
            <slide
              class="flex flex-col items-center justify-center overflow-hidden>"
              v-for="(company, index) in companies"
              :key="index"
            >
              <div
                class="w-full flex flex-col justify-between h-full rounded-lg px-8"
              >
                <div
                  class="flex items-center justify-center flex-grow w-full h-full"
                >
                  <div class="align-middle flex flex-col justify-center">
                    <img
                      class="w-full max-w-[192px] aspect-square object-contain"
                      :src="company.image"
                      :alt="company.text"
                    />
                  </div>
                </div>
              </div>
            </slide>
          </carousel>
        </ClientOnly>
      </div>
      <div class="mt-[150px] flex flex-col space-y-8 pb-[130px]">
        <h2
          class="text-[#FDB21D] md:text-[50px] text-[40px] md:leading-[60px] leading-[50px] text-center font-bold"
        >
          Ready to Learn More?
        </h2>
        <NuxtLink
          to="/#ourServices"
          class="text-[#1A1139] font-medium bg-[#FDB21D] text-xl rounded-full flex justify-center items-center px-[40px] py-3 max-h-[50px] mx-auto !mt-[50px]"
        >
          Our Services
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.intro {
  background-color: #1a1139;
}
.box-section {
  width: 282px;
  height: 256px;
  /* UI Properties */
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 7px 10px #00000029;
  border-radius: 20px;
}
.second-section:hover {
  border-radius: 30px;
  background: linear-gradient(145deg, #2a1e56 11.97%, #3d2c79 89.93%);
  box-shadow: 0px 16px 42px 0px rgba(0, 30, 108, 0.3);
}
// .monitoring_img {
//   width: var(--width);
// }
</style>

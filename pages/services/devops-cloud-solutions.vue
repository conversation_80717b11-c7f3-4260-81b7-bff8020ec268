<script setup>
const seoTitle =
  "DevOps and Cloud Solution to Deliver Smarter, Faster, and Safer";
const seoDescription =
  "Devxhub builds agile, cloud-native infrastructure designed to grow with your business. Your team will deploy faster, safer, and smarter with us.";
//const websiteLogo = `${config.public.siteUrl}/logo.svg`;

useSeoMeta({
  title: () => seoTitle,
  ogTitle: () => seoTitle,
  description: () => seoDescription.slice(0, 300),
  ogDescription: () => seoDescription.slice(0, 300),
});

const heroTitle = `<p class="max-w-[1114px]"><span class="text-[#999]">Modern </span><span class="instrument-italic">DevOps and Cloud </span> <span class="text-[#999]">Solution to Deliver Smarter, Faster</span></p>`;
const heroDes = `<p class="max-w-[890px]">Devxhub builds agile, cloud-native infrastructure designed to grow with your business. Your
team will deploy faster, safer, and smarter with us.
</p>`;

const showImage = false;

const heading = `<span class="text-[36px] font-bold leading-[48px] text-[#999]"><span class="instrument-italic">DevOps & Cloud</span>  Services</span>`;
const features = ref([
  {
    id: 1,
    icon: "/images/homepage/hero/circle.svg",
    title: "Full Transparency",
  },
  {
    id: 2,
    icon: "/images/homepage/hero/infinite.svg",
    title: "Expert Team",
  },
  {
    id: 3,
    icon: "/images/homepage/hero/graph.svg",
    title: "Daily & Weekly Reports",
  },
  {
    id: 4,
    icon: "/images/homepage/hero/loop.svg",
    title: "Flexible & On-Demand",
  },
]);
const expertiseAreas = ref([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/AI-Powered Mobile Apps.png",
    text: "Cloud Infrastructure Setup & Migration",
    description:
      "We help you move from on-premise to cloud or optimize your current setup. AWS, Azure, GCP—we’ve got you covered with scalable, secure configurations.​",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/iOS App Development.png",
    text: "CI/CD Pipeline Automation",
    description:
      "Speed up releases with continuous integration and deployment pipelines. We automate testing, build, and delivery processes for reliable, repeatable rollouts.​",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/Android App Development.png",
    text: "Infrastructure as Code (IaC)",
    description:
      "Manage infrastructure like software. We use tools like Terraform and AWS CloudFormation to enable scalable, consistent, and auditable environments.​",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/Cross-Platform App Development.png",
    text: "Cloud-Native Application Development",
    description:
      "Build apps that are born for the cloud—containerized, microservice-based, and auto-scaling by design.​",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea5",
    image: "/services/full-stack/Flutter App Development.png",
    text: "Kubernetes & Container Orchestration​",
    description:
      "Deploy and manage containers at scale using Kubernetes or other orchestration tools. Ideal for high-availability, dynamic workloads.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea6",
    image: "/services/full-stack/React Native App Development.png",
    text: "DevOps Consulting & Strategy",
    description:
      "Not sure where to start? We assess your current DevOps maturity and build a strategy aligned with your business goals and tech stack.​",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea7",
    image: "/services/full-stack/Progressive Web Apps (PWAs).png",
    text: "Monitoring & Logging Solutions",
    description:
      "Stay informed with real-time insights. We implement tools like Prometheus, Grafana, ELK, and more to track metrics, logs, and performance.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea8",
    image: "/services/full-stack/UI_UX Design for Mobile Apps.png",
    text: "Cloud Cost Optimization",
    description:
      "Cut cloud waste. We analyze your usage and recommend adjustments to reduce costs while maintaining performance and scalability.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea9",
    image: "/services/full-stack/Mobile App Maintenance & Support.png",
    text: "Security & Compliance",
    description:
      "We integrate security practices directly into your pipelines and cloud infrastructure—ensuring compliance with industry standards and regulations.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea10",
    image: "/services/full-stack/App Store Deployment & Optimization.png",
    text: "Disaster Recovery & Backup Planning",
    description:
      "Be prepared. We design fault-tolerant architectures and backup systems to keep your data safe and your business running.​",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea11",
    image: "/services/full-stack/Enterprise Mobility Solutions.png",
    text: "Multi-Cloud & Hybrid Cloud Solutions",
    description:
      "Avoid vendor lock-in. We help you deploy workloads across cloud providers and on-prem for flexibility and resilience.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea12",
    image: "/services/full-stack/Mobile Backend Development.png",
    text: "Managed DevOps & Cloud Services",
    description:
      "Need ongoing support? We manage your cloud and DevOps systems so you can focus on building your product, not maintaining infrastructure.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const benefitstitle1 = `<span class="instrument-italic">Benefits</span> <span class="text-[#999]">of</span>`;
const benefitstitle2 = `DevOps & Cloud Solutions`;
const benefits = ref([
  {
    img: "/services/benefits/Asset_1.svg",
    title: "Speed & Efficiency",
    description: `Faster deployments and quicker feedback loops.`,
  },
  {
    img: "/services/benefits/Asset_2.svg",
    title: "Improved Reliability",
    description: `DevOps practices ensure more stable releases and quicker rollback or recovery from issues.`,
  },
  {
    img: "/services/benefits/Asset_3.svg",
    title: "​Elastic Scalability",
    description: `Cloud-native architectures grow with your traffic and workload without re-architecting.`,
  },
  {
    img: "/services/benefits/Asset_4.svg",
    title: "Cost Control",
    description: `Optimize usage and reduce waste with smart provisioning, autoscaling, and efficient resource management.`,
  },
  {
    img: "/services/benefits/Asset_5.svg",
    title: "​Enhanced Security",
    description: `Security is embedded from the start—via IaC, monitoring, and compliance-focused configurations.`,
  },
  {
    img: "/services/benefits/Asset_6.svg",
    title: "Team Collaboration",
    description: `DevOps unites developers and operations under shared tools and processes—improving transparency and productivity.`,
  },
]);

const extensionProcesses = ref([
  {
    point: "01",
    title: "Discovery Call",
    description:
      "Tell us your idea. We’ll listen, ask questions, and brainstorm together.",
  },
  {
    point: "02",
    title: "Scope & Planning",
    description: "Define features, timelines, and budget with full clarity.",
  },
  {
    point: "03",
    title: "Design & Development",
    description: "Watch your app come to life, step by step",
  },
  {
    point: "04",
    title: "Testing & Refinement",
    description: "We fix bugs before your users ever see them.",
  },
]);
const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "What cloud providers do you support?",
    description: `<p>We work with AWS, Google Cloud, Azure, DigitalOcean, and others based on your needs and budget.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "Do I need to move fully to the cloud to benefit from DevOps?",
    description: `<p>No. We support hybrid environments, on-prem/cloud blends, and phased migrations to suit your timeline.​</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "Can Devxhub build and manage our CI/CD pipelines?​",
    description: `<p>Yes. We build, optimize, and maintain pipelines using GitHub Actions, GitLab CI, Jenkins, CircleCI, and others.​</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "How long does it take to migrate to the cloud?​",
    description: `<p>Depending on scope, most migrations take 4–12 weeks, including planning, execution, and post-deployment testing.​</p>`,
  },
  {
    id: 5,
    slectedItem: false,
    title: "Is DevOps just for large companies?",
    description: `<p>Not at all. Even startups benefit from faster releases, automated tests, and scalable infrastructure.</p>`,
  },
  {
    id: 6,
    slectedItem: false,
    title: "What about security and compliance?",
    description: `<p>We integrate DevSecOps practices embedding security into pipelines and aligning with standards like GDPR, SOC 2, and HIPAA.​</p>`,
  },
  {
    id: 7,
    slectedItem: false,
    title: "Will I get support after setup?​",
    description: `<p>Yes. We offer long-term managed services including monitoring, optimization, and issue resolution.</p>`,
  },
]);
const ourServicesHeight = () => {
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>

<template>
  <div class="md:pt-[196px] pt-[100px]">
    <div class="container-fluid">
      <OurworkingprocessHeroSection
        :heroTitle="heroTitle"
        :heroDes="heroDes"
        :showImage="showImage"
        :titleWrapper="`flex justify-center items-center`"
        :titleClass="`text-center`"
        :descWrapper="`flex justify-center items-center`"
        :descClass="`text-center`"
      >
        <template #button>
          <div
            class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
          >
            <NuxtLink
              to="/contact-us"
              class="text-up-animation md:px-[42px] px-[32px] h-[46px] text-[18px] font-medium leading-[28px] flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
              >Join With Us</NuxtLink
            >
          </div>
        </template>
      </OurworkingprocessHeroSection>
    </div>
    <div class="container-fluid">
      <div class="pb-[95px] border-b border-[#ffffff1a] max-w-[1280px]"></div>
      <div
        class="grid md:hidden grid-cols-2 md:grid-cols-4 text-[#999999] gap-x-10 gap-y-4 justify-items-start z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="mobile"
        />
      </div>
      <div
        class="hidden md:flex text-[#999999] gap-10 justify-center items-center z-[1] py-10"
      >
        <HomeHeroFeatureIcon
          v-for="feature in features"
          :key="feature.id"
          :icon="feature.icon"
          :title="feature.title"
          size="desktop"
        />
      </div>

      <div
        class="md:mt-[200px] mt-[100px] grid min-[992px]:grid-cols-[522px,1fr] min-[992px]:gap-[128px] gap-10 grid-cols-1 min-[992px]:items-center"
      >
        <img
          src="/public/services/mobile-app/new image.png"
          alt="about-mobile-application-development"
          class="max-h-[473px] max-w-[100%] mx-auto"
        />
        <div>
          <h2 class="sixth-h-tag">
            <span class="text-[#999]">What is Devxhub </span>
            <span class="instrument-italic">DevOps & Cloud</span>
            <span class="text-[#999]"> Solutions?</span>
          </h2>
          <p class="primary-p-tag mt-5 max-w-[630px]">
            Devxhub DevOps & Cloud Solutions is a comprehensive service designed
            to unify development and operations while leveraging cloud
            infrastructure to optimize delivery speed, reliability, and
            cost-efficiency. From CI/CD automation to multi-cloud strategies, we
            help businesses of all sizes build resilient systems, deploy faster,
            and scale smarter.
          </p>
        </div>
      </div>

      <div class="md:mt-[180px] mt-[100px]">
        <h2 class="text-up-animation" v-html="heading"></h2>
        <p class="pt-5 text-[#999] text-2xl font-normal text-up-animation">
          From startups modernizing their stack to enterprises optimizing
          infrastructure—we deliver solutions tailored to your needs:​
        </p>
      </div>

      <ServicesFullStackCommonDevelopment :expertiseAreas="expertiseAreas" />
    </div>

    <ServicesDevopsCloudWhyChoose />

    <ServicesDevopsCloudTechnologyStack class="container-fluid" />

    <div class="container-fluid">
      <ServicesCommonSection
        class="pt-[184px]"
        :title1="benefitstitle1"
        :title2="benefitstitle2"
        :fullTimes="benefits"
      />
      <!-- our process -->
      <div class="w-full mt-[180px]">
        <h1
          class="text-[48px] leading-[56px] font-bold text-[#999] text-center"
        >
          <span class="instrument-italic">Our Process: </span>Simple, Smart,
          Strategic
        </h1>
        <p
          class="max-w-[846px] mx-auto text-center pt-5 text-[24px] leading-[32px] font-normal text-[#999]"
        >
          Work with us simply! You don't just get mobile apps from us; we
          deliver digital solutions that grow your business.
        </p>
      </div>
      <div class="w-fill flex flex-col items-center mt-[80px]">
        <div class="four-grid-item">
          <div
            class="text-left-animation"
            v-for="(extensionProcess, index) in extensionProcesses"
            :key="index"
          >
            <div class="flex flex-col space-y-2.5">
              <div
                class="flex"
                :class="[
                  (index + 1) % 2 === 0 ? 'space-x-[11px]' : 'space-x-[12px]',
                ]"
              >
                <p class="primary-p-tag">
                  {{ extensionProcess.point }}
                </p>
                <div>
                  <h2 class="third-h-tag">
                    {{ extensionProcess.title }}
                  </h2>
                  <p class="secondary-p-tag">
                    {{ extensionProcess.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ServicesOurBenefits class="md:pt-[180px] pt-[64px]" />
      <ServicesDevopsCloudConfidence class="container-fluid" />
    </div>

    <HomeFaq :faqData="ourServices" />
  </div>
</template>

<style scoped>
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.four-grid-item {
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid-item {
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}

.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
</style>

<script setup>
const metaTitle = "End-to-End Full Stack Development Services";
const description = `Devxhub delivers expert full stack development services, creating scalable, robust web and mobile apps with seamless front-end and back-end integration.`;
const keyWords =
  "IT staff augmentation, full-stack development, mobile app development, AI/ML integration, DevOps services";

useSeoMeta({
  title: () => metaTitle,
  ogTitle: () => metaTitle,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const heroTitle = `<p class="max-w-[1143px]"><span class="instrument-italic">Full Stack</span> <span class="text-[#999]">Software Development That Delivers </span><span class="instrument-italic">Complete</span> <span class="text-[#999]">Product</span> <span class="instrument-italic">Solutions</span></p>`;
const heroDes = `<p class="max-w-[1064px]">Devxhub provides full-stack end-to-end discovery-to-launch solutions, delivering secure, scalable, and innovative web and mobile application development designed to grow any digital business.</p>`;
const showImage = false;

const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "What is Full-Stack Development at Devxhub?",
    description: `<p>Devxhub’s Full-Stack Development service covers both front-end and back-end solutions—from web and mobile app development to enterprise-grade applications—delivering scalable, cost-effective, and integrated digital solutions.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "What does the Discovery & Definition phase involve?",
    description: `<p>In this phase, Devxhub conducts an in-depth project audit to define system requirements, capture user roles and personas, and select the optimal technologies, laying a solid foundation for the project.</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "How is the Planning & Design phase executed?",
    description: `<p>Devxhub crafts a visual and functional blueprint by modeling the user experience, mapping out the user journey, and prioritizing key features while setting a clear project scope and team logistics.</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "What Agile methodologies are employed during Execution & Delivery?",
    description: `<p>Devxhub uses iterative sprints, continuous integration, daily stand-ups, and regular sprint reviews to refine development-ready specifications, establish time and cost estimates, and ensure a smooth launch.</p>`,
  },
  {
    id: 5,
    slectedItem: false,
    title:
      "How does Devxhub ensure Quality Assurance (QA) in full-stack development?",
    description: `<p>QA is integrated throughout the process with continuous testing, automated quality checks, and robust DevOps practices. This guarantees high performance, strong security, and a seamless user experience.</p>`,
  },
  {
    id: 6,
    slectedItem: false,
    title: "What support is provided post-launch?",
    description: `<p>After launch, Devxhub offers ongoing maintenance, detailed documentation, and comprehensive knowledge transfer, ensuring the application remains scalable, secure, and easy to update over time.</p>`,
  },
]);

const ourServicesHeight = () => {
  // Loop through expertiseAreas to calculate heights
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>

<template>
  <div class="md:pt-[180px] pt-[160px]">
    <div class="container-fluid">
      <OurworkingprocessHeroSection
        :heroTitle="heroTitle"
        :heroDes="heroDes"
        :showImage="showImage"
        :titleWrapper="`flex justify-center items-center`"
        :titleClass="`text-center`"
        :descWrapper="`flex justify-center items-center`"
        :descClass="`text-center`"
      >
        <template #button>
          <div
            class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
          >
            <NuxtLink
              to="/contact-us"
              class="text-up-animation md:px-[42px] px-[32px] h-[46px] text-base flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
              >Book Your Free Consultation Now</NuxtLink
            >
          </div>
        </template>
      </OurworkingprocessHeroSection>
    </div>

    <div
      class="container-fluid md:mt-[200px] mt-[100px] grid min-[992px]:grid-cols-[455px,1fr] min-[992px]:gap-[87px] grid-cols-1"
    >
      <img
        src="/public/services/full-stack/about-full-stack.png"
        alt="about-full-stack"
        class="max-h-[458px] max-w-[100%] mx-auto"
      />
      <div>
        <h2 class="sixth-h-tag">
          <p>
            <span class="text-[#999]">What is Devxhub</span>
            <span class="instrument-italic"> Full Stack Development?</span>
          </p>
        </h2>
        <p class="primary-p-tag mt-5 max-w-[737px]">
          Devxhub Full Stack Development is a comprehensive service offering
          that covers every layer of the software development process—from
          designing the user interface (UI) to building the front-end and
          back-end systems. This approach ensures that clients receive a
          complete, end-to-end solution for their digital projects.
        </p>
        <NuxtLink
          to="/contact-us#appointment"
          class="text-up-animation w-[196px] h-[46px] text-base flex justify-center items-center text-[#1D1A20] mt-16 bg-[#FFD700] rounded-full self-start final"
          >Hire the Best team</NuxtLink
        >
      </div>
    </div>

    <ServicesFullStackDevelopment class="container-fluid" />

    <ServicesFullStackTechnologyStack class="container-fluid" />

    <ServicesFullStackWhyChoose class="" />

    <ServicesFullStackProcess class="container-fluid" />

    <ServicesFullStackIndustries class="container-fluid" />

    <!-- <ServicesOurBenefits class="container-fluid" /> -->

    <ServicesOurBenefits class="container-fluid md:pt-40 pt-20" />
    <HomeFaq :faqData="ourServices" />
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 129px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  /* grid-template-columns: 0.7fr 1.3fr; */
  grid-template-columns: minmax(0, 413px) 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
@media screen and (max-width: 1023px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
}
</style>

<script setup>
import { useRuntimeConfig } from "nuxt/app";

const config = useRuntimeConfig();
const title = "Custom Software Solutions & Services";
const description = `Discover Devxhub’s software services - including IT staff augmentation, full-stack, mobile apps, AI/ML integration, DevOps, and more.`;
const keyWords =
  "IT staff augmentation, full-stack development, mobile app development, AI/ML integration, DevOps services";

useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const whyChooseUs = [
  {
    image: "/services/service/icon/icon-1.webp",
    title: "Full Transparency",
    description:
      "Get real-time updates on hours spent & task management, clear communication, and complete project visibility at every stage.",
  },
  {
    image: "/services/service/icon/icon-2.webp",
    title: "Dedicated Expert Team",
    description:
      "Work with a highly skilled team of developers, designers, and engineers, each with unique talents and insights committed to your success.",
  },
  {
    image: "/services/service/icon/icon-3.webp",
    title: "Scalable & Flexible Solutions",
    description:
      "Easily scale your team and adapt resources based on your project needs and business growth. Scale up or down, pause, or cancel anytime.",
  },
  {
    image: "/services/service/icon/icon-4.webp",
    title: "No Hiring Downtime",
    description:
      "Skip the hiring grind, Instantly onboard top-tier developers and accelerate your project without recruitment delays. We assemble team so you don’t have to.",
  },
  {
    image: "/services/service/icon/icon-5.webp",
    title: "Proven Team Experience",
    description:
      "Our team's collective experience ensures innovative solutions tailored to your needs across various industries.",
  },
  {
    image: "/services/service/icon/icon-6.webp",
    title: "Agile Development Process",
    description:
      "Fast, transparent, and iterative process. Speed up development cycles with our agile methodology, ensuring quick iterations and faster time-to-market.",
  },
];
const headerTitle = ref(`<h2 class="md:text-xl text-lg title-up-animation">
        <p class="text-[#999] font-medium">Why Partner</p>
        <p class="instrument-italic text-[#F1F1F2]">with Devxhub?</p>
      </h2>`);
</script>

<template>
  <div class="w-full h-full">
    <ServicesHero />
    <ServicesService />
    <!-- <HomeIndustryExpertise class="py-[100px] container-fluid" /> -->
    <ServicesFullStackIndustries
      class="container-fluid lg:pb-[180px] md:pb-[84px] pb-[64px]"
    />
    <ServicesWhyChooseDevxhub
      :header-title="headerTitle"
      :core-values="whyChooseUs"
    />
  </div>
</template>

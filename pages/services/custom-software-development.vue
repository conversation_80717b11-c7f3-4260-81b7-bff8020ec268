<script setup>
const title = "Best Custom Software Solutions for Enterprise Growth";
const intro = {
  title: "Best Custom Software Solutions for Enterprise Growth",
  description:
    " Automate your operations with our custom enterprise software. Our scalable solutions help you achieve your business goals.",
};

useSeoMeta({
  title: () => title,
  ogTitle: () => intro.title,
  description: () => intro.description.slice(0, 300),
  ogDescription: () => intro.description.slice(0, 300),
});

const heroTitle = `<p class="max-w-[1143px] text-[#999]">Custom & Enterprise <span class="instrument-italic"> Software Development</span> <span class="text-[#999]"> That Drives </span>Business <span class="text-[#999]">Innovation</span> </p>`;
const heroDes = `<p class="max-w-[1064px]">Devxhub designs, builds, and delivers reliable, scalable, and future-proof software to optimize workflows and accelerate business results.</p>`;
const showImage = false;

const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "What is Full-Stack Development at Devxhub?",
    description: `<p>Devxhub's Full-Stack Development service covers both front-end and back-end solutions—from web and mobile app development to enterprise-grade applications—delivering scalable, cost-effective, and integrated digital solutions.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "What does the Discovery & Definition phase involve?",
    description: `<p>In this phase, Devxhub conducts an in-depth project audit to define system requirements, capture user roles and personas, and select the optimal technologies, laying a solid foundation for the project.</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "How is the Planning & Design phase executed?",
    description: `<p>Devxhub crafts a visual and functional blueprint by modeling the user experience, mapping out the user journey, and prioritizing key features while setting a clear project scope and team logistics.</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "What Agile methodologies are employed during Execution & Delivery?",
    description: `<p>Devxhub uses iterative sprints, continuous integration, daily stand-ups, and regular sprint reviews to refine development-ready specifications, establish time and cost estimates, and ensure a smooth launch.</p>`,
  },
  {
    id: 5,
    slectedItem: false,
    title:
      "How does Devxhub ensure Quality Assurance (QA) in full-stack development?",
    description: `<p>QA is integrated throughout the process with continuous testing, automated quality checks, and robust DevOps practices. This guarantees high performance, strong security, and a seamless user experience.</p>`,
  },
  {
    id: 6,
    slectedItem: false,
    title: "What support is provided post-launch?",
    description: `<p>After launch, Devxhub offers ongoing maintenance, detailed documentation, and comprehensive knowledge transfer, ensuring the application remains scalable, secure, and easy to update over time.</p>`,
  },
]);
const heading = `<span class="md:text-[36px] text-[24px]  font-bold leading-[48px] text-[#999]"><span class="instrument-italic">Custom Software Development
</span> Services</span>`;

const heading2 = `<span class="md:text-[36px] text-[24px]  font-bold leading-[48px] text-[#999]"><span class="instrument-italic">Enterprise Software Development 
</span> Services</span>`;

// Update ref type
const expertiseAreas = ref([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/end-to-end-development.png",
    text: "Custom Web Application Development",
    description:
      "Customizable and flexible web apps built to streamline processes and enhance user experience.",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/frontend-development.png",
    text: "Mobile App Development (iOS, Android, Cross-platform)",
    description:
      "High-performance mobile apps for all platforms to engage users on the go.",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/backend-development.png",
    text: "Desktop Application Development",
    description:
      "High-performance desktop applications for offline use and enhanced system control.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/mobile-app-development.png",
    text: "Cloud-Based Software Solutions",
    description:
      "Secure, scalable cloud software for easy access, storage, and collaboration.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea5",
    image: "/services/full-stack/mvp-saas.png",
    text: "API Development & Integration",
    description:
      "Custom APIs to enable smooth data exchange between systems and apps.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea6",
    image: "/services/full-stack/ai-ml-devops.png",
    text: "UI/UX Design & Development",
    description:
      "Easy-to-use interfaces and user-friendly designs that elevate digital experiences.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea7",
    image: "/services/full-stack/maintenance-support.png",
    text: "Legacy System Modernization",
    description:
      "Upgrade outdated systems with modern technologies for better performance and security.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea8",
    image: "/services/full-stack/maintenance-support.png",
    text: "Custom CRM/ERP Development",
    description:
      "Customized CRM and ERP platforms to efficiently manage customers, resources, and operations.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea9",
    image: "/services/full-stack/maintenance-support.png",
    text: "SaaS Product Development",
    description:
      "Build and expand subscription-based software products for web or mobile platforms.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea10",
    image: "/services/full-stack/maintenance-support.png",
    text: "Custom eCommerce Solutions",
    description:
      "Flexible, expandable eCommerce platforms designed to maximize online sales and customer satisfaction.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const anotherExpertiseAreas = ref([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/end-to-end-development.png",
    text: "Enterprise Resource Planning (ERP) Systems",
    description:
      "Integrated platforms for managing core business processes like finance, inventory, and HR.",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/frontend-development.png",
    text: "Customer Relationship Management (CRM) Software",
    description:
      "Tools to track leads, sales, and customer interactions for better relationship management.",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea3",
    image: "/services/full-stack/backend-development.png",
    text: "Enterprise Content Management (ECM)",
    description:
      "Centralized systems for storing, organizing, and retrieving digital content securely.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/mobile-app-development.png",
    text: "Business Intelligence (BI) & Analytics Software",
    description:
      "Data-driven tools that turn raw data into actionable business insights.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea5",
    image: "/services/full-stack/mvp-saas.png",
    text: "Workflow Automation Solutions",
    description:
      "Automated systems that eliminate manual tasks and improve process efficiency.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea6",
    image: "/services/full-stack/ai-ml-devops.png",
    text: "Human Resource Management Systems (HRMS)",
    description:
      "Comprehensive tools for managing employee data, payroll, recruitment, and performance.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea7",
    image: "/services/full-stack/maintenance-support.png",
    text: "Enterprise Mobile App Development",
    description:
      "Mobile applications designed to support enterprise operations and improve workforce productivity.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea8",
    image: "/services/full-stack/maintenance-support.png",
    text: "Supply Chain Management (SCM) Software",
    description:
      "Solutions to manage logistics, inventory, suppliers, and order fulfillment across the supply chain.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea9",
    image: "/services/full-stack/maintenance-support.png",
    text: "Enterprise Data Management Solutions",
    description:
      "Systems to organize, govern, and protect large volumes of enterprise data.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea10",
    image: "/services/full-stack/maintenance-support.png",
    text: "Custom Intranet/Portal Development",
    description:
      "Private networks and portals that improve internal communication and collaboration.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const extensionProcesses = ref([
  {
    point: "01",
    title: "Talent Requirements",
    description:
      "Share your talent requirements and team size for the ultimate augmentation.",
  },
  {
    point: "02",
    title: "Contract Signing",
    description: "Contract signing for your IT Staff Augmentation.",
  },
  {
    point: "03",
    title: "Talent Allocation",
    description: "Provide ready-to-go teams as per your requirements.",
  },
  {
    point: "04",
    title: "Project Continues",
    description:
      "Augmented team will seamlessly align with your in-house team to ensure on-time project delivery.",
  },
]);

const ourServicesHeight = () => {
  // Loop through expertiseAreas to calculate heights
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>

<template>
  <div class="md:pt-[180px] pt-[160px]">
    <div class="container-fluid">
      <OurworkingprocessHeroSection
        :heroTitle="heroTitle"
        :heroDes="heroDes"
        :showImage="showImage"
        :titleWrapper="`flex justify-center items-center`"
        :titleClass="`text-center`"
        :descWrapper="`flex justify-center items-center`"
        :descClass="`text-center`"
      >
        <template #button>
          <div
            class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
          >
            <NuxtLink
              to="/contact-us"
              class="text-up-animation md:px-[42px] px-[32px] h-[46px] text-base flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
              >Book a Free Consultation</NuxtLink
            >
          </div>
        </template>
      </OurworkingprocessHeroSection>
    </div>

    <div
      class="container-fluid md:mt-[200px] mt-[100px] grid min-[992px]:grid-cols-[455px,1fr] min-[992px]:gap-[87px] grid-cols-1 gap-10"
    >
      <img
        src="/public/services/custom-software-development/custom-software-development.png"
        alt="about-full-stack"
        class="max-h-[458px] max-w-[100%] mx-auto"
      />
      <div>
        <h2 class="sixth-h-tag max-w-[630px]">
          <p class="text-[#999]">
            What is Devxhub
            <span class="instrument-italic"> Custom </span>
            &
            <span class="instrument-italic">Enterprise</span>
            Software Development?
          </p>
        </h2>
        <p class="primary-p-tag mt-5 max-w-[737px]">
          Devxhub Custom & Enterprise Software Development is designed to build
          scalable, secure, high-performing software solutions that meet
          specific business needs. No matter your size or industry, this service
          provides end-to-end development, from initial strategy and
          architecture through deployment and long-term support.
        </p>
        <NuxtLink
          to="/contact-us#appointment"
          class="text-up-animation w-[196px] h-[46px] text-base flex justify-center items-center text-[#1D1A20] mt-16 bg-[#FFD700] rounded-full self-start final"
          >Please Make a Call</NuxtLink
        >
      </div>
    </div>

    <!-- <ServicesCustomSoftwareDevelopment class="container-fluid" />
    <ServicesCustomSoftwareAnotherDevelopment class="container-fluid" /> -->

    <div class="md:mt-[180px] mt-[100px] container-fluid">
      <h2 class="text-up-animation" v-html="heading"></h2>
      <ServicesFullStackCommonDevelopment :expertiseAreas="expertiseAreas" />
    </div>

    <div class="md:mt-[180px] mt-[100px] container-fluid">
      <h2 class="text-up-animation" v-html="heading2"></h2>
      <p
        class="pt-5 text-[#999] text-2xl font-normal text-up-animation max-w-[787px]"
      >
        Advanced software solutions that support large-scale operations and
        complex business needs.
      </p>
      <ServicesFullStackCommonDevelopment
        :expertiseAreas="anotherExpertiseAreas"
      />
    </div>

    <ServicesFullStackTechnologyStack class="container-fluid" />

    <ServicesCustomSoftwareWhyChoose class="" />

    <ServicesCustomSoftwareCulture class="container-fluid" />

    <div class="pt-[176px] container-fluid">
      <h2 class="sixth-h-tag">
        <p class="text-[#999]">Custom & Enterprise</p>
        <p class="text-[#999]">
          Software Development <span class="instrument-italic">Process</span>
        </p>
      </h2>
      <p class="fourth-p-tag mt-8 max-w-[630px]">
        We are committed to providing quality services. Our goal is to help our
        clients achieve their business.
      </p>
      <NuxtLink
        to="/contact-us#appointment"
        class="text-up-animation w-[197px] h-[46px] text-lg font-medium flex justify-center items-center text-[#1D1A20] mt-[40px] bg-[#FFD700] rounded-full self-start final"
        >Schedule a Call</NuxtLink
      >
      <div class="w-fill flex flex-col items-center mt-[80px]">
        <div class="four-grid">
          <div
            class="text-left-animation"
            v-for="(extensionProcess, index) in extensionProcesses"
            :key="index"
          >
            <div class="flex flex-col space-y-4">
              <div class="flex space-x-[10px]">
                <p class="primary-p-tag">
                  {{ extensionProcess.point }}
                </p>
                <h2 class="third-h-tag">
                  {{ extensionProcess.title }}
                </h2>
              </div>
              <p class="secondary-p-tag">
                {{ extensionProcess.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ServicesCustomSoftwareIndustries class="container-fluid" />

    <ServicesOurBenefits class="container-fluid md:pt-[180px] pt-[64px]" />

    <ServicesCustomSoftwareFaq />
  </div>
</template>

<style scoped>
.case-grid-item {
  grid-column-gap: 129px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  /* grid-template-columns: 0.7fr 1.3fr; */
  grid-template-columns: minmax(0, 413px) 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}
@media screen and (max-width: 1023px) {
  .case-grid-item {
    grid-template-columns: 1fr;
  }
}
.four-grid {
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid {
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}
</style>

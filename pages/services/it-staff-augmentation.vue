<script setup>
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";

const metaTitle = "Scale Your Team with IT Staff Augmentation Services";
const description = `Scale your software team with Devxhub’s IT staff augmentation services. Get skilled developers for faster, flexible project delivery.`;
const keyWords =
  "IT staff augmentation, full-stack development, mobile app development, AI/ML integration, DevOps services";

useSeoMeta({
  title: () => metaTitle,
  ogTitle: () => metaTitle,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const breakpoints = useBreakpoints(breakpointsTailwind);
const isDesktop = breakpoints.greaterOrEqual("lg");

const heroTitle = `<span class="text-[#999]">We </span><span class="instrument-italic">build, extend or even replace</span> <span class="text-[#999]">your</span> <span class="instrument-italic">development team</span>`;
const heroDes = `<p class="third-h-tag mt-10"><span class="instrument-italic">Build</span> <span class="text-[#999]">Your</span> <span class="instrument-italic">In-house Team</span> <span class="text-[#999]">with</span> <span class="instrument-italic">Devxhub's Team Augmentation</span> <span class="text-[#999]">Services</span></p><p class="pt-5 max-w-[890px]">Devxhub provides full-stack end-to-end discovery-to-launch solutions, delivering secure, scalable, and innovative web and mobile application development designed to grow any digital business.</p>`;
const showImage = false;
const augmentationtitle1 = `<span class="instrument-italic">Why</span> <span class="text-[#999]">Devxhub</span>`;
const augmentationtitle2 = `Team Augmentation?`;
const features = ref([
  {
    id: 1,
    icon: "/images/homepage/hero/circle.svg",
    title: "Full Transparency",
  },
  {
    id: 2,
    icon: "/images/homepage/hero/infinite.svg",
    title: "Expert Team",
  },
  {
    id: 3,
    icon: "/images/homepage/hero/graph.svg",
    title: "Daily & Weekly Reports",
  },
  {
    id: 4,
    icon: "/images/homepage/hero/loop.svg",
    title: "Flexible & On-Demand",
  },
]);
const augmentation = ref([
  {
    img: "/services/augmentation/Asset_1.svg",
    title: "Access to Global Talent",
    description: `Expand your team with highly skilled tech professionals from around the world without geographical limitations.`,
  },
  {
    img: "/services/augmentation/Asset_2.svg",
    title: "Cost-Effective Scaling",
    description: `Avoid the high costs of hiring full-time employees while still getting access to top-tier talent.`,
  },
  {
    img: "/services/augmentation/Asset_3.svg",
    title: "Faster Hiring Process",
    description: `Skip the lengthy recruitment cycles and onboard developers quickly to meet project deadlines.`,
  },
  {
    img: "/services/augmentation/Asset_4.svg",
    title: "Full Control & Flexibility",
    description: `Maintain complete control over team composition, project direction, and workflows while benefiting from external expertise.`,
  },
  {
    img: "/services/augmentation/Asset_5.svg",
    title: "Seamless Integration",
    description: `Augmented teams blend into your existing workflow, ensuring smooth collaboration and productivity.`,
  },
  {
    img: "/services/augmentation/Asset_6.svg",
    title: "Expertise on Demand",
    description: `Gain specialized skills that may not be available in-house, enhancing your team’s capabilities for specific projects.`,
  },
]);
const benefitstitle1 = `<span class="instrument-italic">Benefits</span> <span class="text-[#999]">of</span>`;
const benefitstitle2 = `Team Augmentation`;
const benefits = ref([
  {
    img: "/services/benefits/Asset_1.svg",
    title: "Increased Productivity",
    description: `Reinforce your in-house team with skilled professionals, ensuring faster project completion and higher efficiency.
`,
  },
  {
    img: "/services/benefits/Asset_2.svg",
    title: "Reduced Recruitment Burden",
    description: ` Save time and resources by eliminating the hassle of traditional hiring, training, and onboarding.`,
  },
  {
    img: "/services/benefits/Asset_3.svg",
    title: "Scalability & Agility",
    description: `Easily scale your team up or down based on project needs, ensuring flexibility in resource allocation`,
  },
  {
    img: "/services/benefits/Asset_4.svg",
    title: "Lower Operational Costs",
    description: `Reduce expenses related to full-time salaries, benefits, and infrastructure while maintaining high-quality output.`,
  },
  {
    img: "/services/benefits/Asset_5.svg",
    title: "Focus on Core Business",
    description: `Free up internal teams to concentrate on strategic initiatives while augmented teams handle technical execution.`,
  },
  {
    img: "/services/benefits/Asset_6.svg",
    title: "Access to Cutting-Edge Tech",
    description: `Leverage the latest tools, frameworks, and development methodologies through specialized professionals.`,
  },
]);
const extensionProcesses = ref([
  {
    point: "01",
    title: "Talent Requirements",
    description:
      "Share your talent requirements and team size for the ultimate augmentation.",
  },
  {
    point: "02",
    title: "Contract Signing",
    description: "Contract signing for your IT Staff Augmentation.",
  },
  {
    point: "03",
    title: "Talent Allocation",
    description: "Provide ready-to-go teams as per your requirements.",
  },
  {
    point: "04",
    title: "Project Continues",
    description:
      "Augmented team will seamlessly align with your in-house team to ensure on-time project delivery.",
  },
]);

const title = `<p class="text-[#999]">Team <br /><span class="instrument-italic">Augmentation</span></p>`;
const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "When should a company consider team augmentation services?",
    description: `<p>The decision to use team augmentation services should be considered when a company lacks experienced software developers internally or needs a complete IT team to handle the increased workload and technical requirements. Team augmentation is particularly beneficial in these scenarios, as it allows the company to access skilled technical resources without investing significant time and money into hiring and managing the team on its own.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "Is team augmentation a cost-effective model?",
    description: `<p>IT team augmentation can significantly reduce software development costs by eliminating expenses associated with recruiting, training, and managing individual team members. Additionally, it removes the need for office space, health insurance, and other overhead costs associated with an in-house development team.</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "What industries benefit the most from team augmentation?",
    description: `<p>IT team augmentation services are most beneficial for industries undergoing digital transformation or those with cyclical IT needs. This includes sectors like finance, healthcare, and manufacturing, where comprehensive IT solutions are crucial, but maintaining a full-time, specialized IT department may not be feasible.</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "How to choose the right team augmentation provider?",
    description: `<p>When choosing your provider, the main thing is to check if your potential partner can offer well-rounded teams with complementary skills that match your project needs. Also, consider their team’s collective experience, communication processes, track record of successful projects, and flexibility in scaling resources up or down.</p>`,
  },
]);

const heading = `<span class="text-[36px] font-bold leading-[48px] text-[#999]"><span class="instrument-italic">Team Augmentation</span> services?</span>`;

const expertiseAreas = ref([
  {
    id: "expertiseArea1",
    image: "/services/full-stack/frontend-development.png",
    text: "Frontend Developers",
    description:
      "Enhance your team with expert Frontend Developers who craft responsive, user-centric interfaces using the latest technologies like React, Vue, and Angular.",
    backgroundColor: "#6D4410",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea2",
    image: "/services/full-stack/backend-development.png",
    text: "Backend Developers",
    description:
      "Boost your team with skilled Backend Developers specializing in building robust, scalable server-side solutions using Node.js, Python, Ruby, and more.",
    backgroundColor: "#000000",
    margin: 1.8,
    slectedItem: false,
  },
  // {
  //   id: "expertiseArea3",
  //   image: "/services/full-stack/backend-development.png",
  //   text: "Backend Development",
  //   description:
  //     "We create secure and scalable backend solutions to ensure your applications run smoothly, efficiently, and reliably.",
  //   backgroundColor: "#662D90",
  //   margin: 1.8,
  //   slectedItem: false,
  // },
  {
    id: "expertiseArea3",
    image: "/industryExperties/expertiseArea/mobile-app-developmet.png",
    text: "Mobile App Developers",
    description:
      "Augment your team with expert Mobile App Developers skilled in building high-performance iOS, Android, and cross-platform apps using Swift, Kotlin, and Flutter.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea4",
    image: "/services/full-stack/mvp-saas.png",
    text: "MERN Stack Developers",
    description:
      "Scale your team with skilled MERN Stack developers who integrate seamlessly and accelerate full-stack development using MongoDB, Express, React, and Node.js.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea5",
    image: "/industryExperties/expertiseArea/Full stack development.png",
    text: "Full Stack Developers",
    description:
      "Boost your team with expert Full Stack Developers skilled in both front-end and back-end technologies. Scale your projects efficiently with on-demand talent.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea6",
    image: "/industryExperties/expertiseArea/java developer .png",
    text: "Java Developers",
    description:
      "Hire skilled Java Developers to build robust, scalable applications. Drive your projects forward with expert developers on-demand.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea7",
    image: "/industryExperties/expertiseArea/NET Core Developers.png",
    text: ".NET Core Developers",
    description:
      "Hire expert .NET Core Developers to build high-performance, scalable applications. Accelerate your project delivery with on-demand talent.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea8",
    image: "/industryExperties/expertiseArea/Software Architects.png",
    text: "Software Architects",
    description:
      "Hire experienced Software Architects to design scalable, high-performance systems. Shape your project's success with expert architectural guidance.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea9",
    image: "/industryExperties/expertiseArea/AIML.png",
    text: "AI/ML Engineers",
    description:
      "Hire skilled AI/ML Engineers to build intelligent, data-driven solutions. Drive innovation and enhance your projects with expert machine learning talent.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
  {
    id: "expertiseArea10",
    image: "/industryExperties/expertiseArea/DevOps & Cloud Engineers.png",
    text: "DevOps & Cloud Engineers",
    description:
      "Optimize your infrastructure and streamline operations with expert DevOps and Cloud Engineers. Ensure seamless deployments and scalability with on-demand talent.",
    backgroundColor: "#662D90",
    margin: 1.8,
    slectedItem: false,
  },
]);

const ourServicesHeight = () => {
  // Loop through expertiseAreas to calculate heights
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
const ourSolutions = ref([
  {
    id: 1,
    point: "01",
    title1: "Skills Shortage",
    description1:
      "A lack of specialized skills within your in-house team can slow down development and innovation.",
    title2: "Solution",
    description2: `Devxhub gives you access to a <span class="font-bold">global pool of top-tier IT professionals</span>, ensuring you get the expertise you need without long hiring cycles.`,
    onHover: false,
  },
  {
    id: 2,
    point: "02",
    title1: "Lengthy & Inefficient Recruitment Process",
    description1:
      "Finding and hiring the right talent is time-consuming and can delay project timelines.",
    title2: "Solution",
    description2: `We provide <span class="font-bold">pre-vetted, experienced professionals</span> who can integrate seamlessly into your team, <span class="font-bold">eliminating the hassle of recruitment</span>.`,
    onHover: false,
  },
  {
    id: 3,
    point: "03",
    title1: "Scalability Challenges",
    description1:
      "Businesses struggle to scale their teams up or down based on project demands.",
    title2: "Solution",
    description2: `With <span class="font-bold">on-demand team augmentation</span>, you can quickly <span class="font-bold">expand or reduce</span> your workforce without the complexities of traditional hiring.`,
    onHover: false,
  },
  {
    id: 4,
    point: "04",
    title1: "Delayed Time-to-Market",
    description1:
      "A shortage of skilled developers or a slow hiring process can lead to product launch delays.",
    title2: "Solution",
    description2: `Our <span class="font-bold">ready-to-deploy experts</span> ensure <span class="font-bold">faster development cycles</span>, helping you bring products to market <span class="font-bold">more efficiently</span>.`,
    onHover: false,
  },
  {
    id: 5,
    point: "05",
    title1: "High Operational & Hiring Costs",
    description1:
      "Hiring full-time employees comes with high salaries, benefits, and infrastructure costs.",
    title2: "Solution",
    description2: `<span class="font-bold">Reduce costs</span> by hiring <span class="font-bold">skilled IT professionals only when needed</span>, without long-term financial commitments.`,
    onHover: false,
  },
  {
    id: 6,
    point: "06",
    title1: "Integration & Workflow Disruptions",
    description1:
      "Adding new team members can be disruptive and time-consuming.",
    title2: "Solution",
    description2: `Our <span class="font-bold">developers quickly adapt</span> to your existing tools, methodologies, and company culture for <span class="font-bold">seamless collaboration</span>.`,
    onHover: false,
  },
]);
const setMouseOnOver = (id) => {
  ourSolutions.value.forEach((ourSolution) => {
    if (ourSolution.id === id) {
      ourSolution.onHover = !ourSolution.onHover;
    }
  });
};
</script>

<template>
  <div class="container-fluid pt-[196px]">
    <OurworkingprocessHeroSection
      :heroTitle="heroTitle"
      :heroDes="heroDes"
      :showImage="showImage"
      :titleWrapper="`flex justify-center items-center`"
      :titleClass="`text-center max-w-[854px]`"
      :descWrapper="`flex justify-center items-center`"
      :descClass="`text-center`"
    >
      <template #button>
        <div
          class="text-up-animation max-w-[1280px] flex flex-col items-center w-full"
        >
          <NuxtLink
            to="/contact-us"
            class="text-up-animation w-[220px] h-[46px] text-xl font-medium flex justify-center items-center text-[#1D1A20] mt-[64px] bg-[#FFD700] rounded-full final"
            >Hire the Best Team</NuxtLink
          >
          <div
            class="grid md:hidden grid-cols-2 md:grid-cols-4 text-[#999999] gap-x-10 gap-y-4 justify-items-start z-[1] py-10"
          >
            <HomeHeroFeatureIcon
              v-for="feature in features"
              :key="feature.id"
              :icon="feature.icon"
              :title="feature.title"
              size="mobile"
            />
          </div>
          <div
            class="hidden md:flex text-[#999999] gap-10 justify-center items-center z-[1] py-10"
          >
            <HomeHeroFeatureIcon
              v-for="feature in features"
              :key="feature.id"
              :icon="feature.icon"
              :title="feature.title"
              size="desktop"
            />
          </div>
        </div>
      </template>
    </OurworkingprocessHeroSection>

    <div class="md:pt-[223px] pt-[80px]">
      <div
        class="flex min-[1024px]:flex-row flex-col min-[1024px]:space-y-0 space-y-10 justify-between"
      >
        <!-- <div class="overflow-hidden">
          <p
            id="animatedTextOne"
            class="max-w-[900px] animated_text_one sixth-h-tag font-bold !text-[#999]"
          >
            What are
            <span class="text-[#f1f1f2]">Team Augmentation</span> services?
          </p>
        </div> -->
        <!-- <p
          class="xl:text-2xl md:text-[22px] md:leading-[32px] text-xl font-medium text-[#999] min-[1024px]:max-w-[450px] w-full"
        >
          Don’t let talent shortages slow you down. Maximize your team’s
          performance and accelerate your business goals with
          <span class="text-[#f1f1f2] font-bold"
            >Devxhub’s IT Team Augmentation services</span
          >.
        </p> -->
      </div>
      <div
        class="grid min-[992px]:grid-cols-[0.8fr_1fr] min-[992px]:gap-[128px] grid-cols-1"
      >
        <div>
          <div class="overflow-hidden min-[992px]:mb-[123px] max-w-[522px]">
            <p
              id="animatedTextOne"
              class="max-w-[522px] animated_text_one sixth-h-tag font-bold !text-[#999]"
            >
              What are
              <span class="text-[#f1f1f2] instrument-italic"
                >Team Augmentation</span
              >
              services?
            </p>
          </div>
          <p
            class="min-[992px]:hidden mt-10 mb-20 text-xl font-medium text-[#999] w-full"
          >
            Don’t let talent shortages slow you down. Maximize your team’s
            performance and accelerate your business goals with
            <span class="text-[#f1f1f2] font-medium"
              >Devxhub’s IT Team Augmentation services</span
            >.
          </p>
          <div class="max-w-[522px]">
            <img
              class="w-full h-full"
              src="/public/services//characterizes.webp"
              alt="characterizes"
            />
          </div>
        </div>
        <div>
          <p
            class="min-[992px]:block hidden mb-10 text-xl font-medium text-[#999] w-full"
          >
            Don’t let talent shortages slow you down. Maximize your team’s
            performance and accelerate your business goals with
            <span class="text-[#f1f1f2] font-medium"
              >Devxhub’s IT Team Augmentation services</span
            >.
          </p>
          <p class="text-xl font-medium text-[#999]">
            Team augmentation is a flexible outsourcing model that allows
            businesses to expand their development teams with skilled tech
            professionals—on-demand and hassle-free. Whether you’re a tech
            company looking for top-tier global talent or a business aiming to
            enhance your IT capabilities quickly, our team augmentation
            solutions provide the expertise you need without the complexities of
            traditional hiring. <br />
            <br />With Devxhub, you stay in full control—selecting team members,
            reviewing resumes, and directing your project—while we handle the
            recruitment, onboarding, and seamless integration of your extended
            team. Focus on your vision, architecture, and goals, and let us take
            care of the rest.
          </p>
          <p class="mt-10 text-[#f1f1f2] text-xl font-medium">
            Scale fast. Work smarter. Build better.
            <NuxtLink class="border-b border-[#FFD700]" to="/contact-us"
              ><span class="text-[#999] font-normal">Partner with</span> Devxhub
              <span class="text-[#999] font-normal">today!</span></NuxtLink
            >
          </p>
        </div>
      </div>
    </div>
    <div class="md:mt-[180px] mt-[100px]">
      <h2 class="text-up-animation" v-html="heading"></h2>
      <p class="pt-5 text-[#999] text-2xl font-normal text-up-animation">
        Devxhub provides below dedicated resources.
      </p>
    </div>
    <ServicesFullStackCommonDevelopment :expertiseAreas="expertiseAreas" />
    <ServicesCommonSection
      class="pt-[180px]"
      :title1="augmentationtitle1"
      :title2="augmentationtitle2"
      :fullTimes="augmentation"
    />
    <ServicesCommonSection
      class="pt-[184px]"
      :title1="benefitstitle1"
      :title2="benefitstitle2"
      :fullTimes="benefits"
    />
    <div class="pt-[176px]">
      <h2 class="sixth-h-tag">
        <p class="text-[#999]">Staff Augmentation</p>
        <p class="instrument-italic">Process</p>
      </h2>
      <p class="fourth-p-tag mt-8 max-w-[630px]">
        We are committed to providing quality services. Our goal is to help our
        clients achieve their business.
      </p>
      <NuxtLink
        to="/contact-us#appointment"
        class="text-up-animation w-[197px] h-[46px] text-lg font-medium flex justify-center items-center text-[#1D1A20] mt-[40px] bg-[#FFD700] rounded-full self-start final"
        >Schedule a Call</NuxtLink
      >
      <div class="w-fill flex flex-col items-center mt-[80px]">
        <div class="four-grid-item">
          <div
            class="text-left-animation"
            v-for="(extensionProcess, index) in extensionProcesses"
            :key="index"
          >
            <div class="flex flex-col space-y-4">
              <div class="flex space-x-[10px]">
                <p class="primary-p-tag">
                  {{ extensionProcess.point }}
                </p>
                <h2 class="third-h-tag">
                  {{ extensionProcess.title }}
                </h2>
              </div>
              <p class="secondary-p-tag">
                {{ extensionProcess.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-[204px]">
      <h2 class="sixth-h-tag">
        <p class="text-[#999]">
          Your pain points
          <span class="instrument-italic text-[#f1f1f2]">– Our solutions</span>
        </p>
      </h2>
      <p class="fourth-p-tag text-[#999] w-full mt-8">
        At <span class="text-[#f1f1f2] font-medium">Devxhub</span>, we
        understand the challenges businesses face in scaling their IT teams. Our
        <span class="text-[#f1f1f2] font-medium"
          >IT Staff & Team Augmentation Services</span
        >
        provide flexible, efficient, and cost-effective solutions to help you
        overcome these obstacles and achieve your business goals.
      </p>
      <div class="grid sm:grid-cols-[1fr_1fr] grid-cols-[1fr] gap-[26px] mt-10">
        <div
          class="bg-white px-10 pt-[34px] pb-[60px] overflow-hidden rounded-[20px] relative"
          v-for="ourSolution in ourSolutions"
          :key="ourSolution.id"
          @mouseenter="isDesktop ? setMouseOnOver(ourSolution.id) : ''"
          @mouseleave="isDesktop ? setMouseOnOver(ourSolution.id) : ''"
        >
          <div class="w-full grid grid-cols-1 grid-rows-1 items-center">
            <div
              class="flex !space-x-2 items-center col-span-full row-span-full transition-all duration-500 ease-in-out"
              :class="
                !ourSolution.onHover
                  ? 'visible opacity-100'
                  : ' invisible opacity-0'
              "
            >
              <div class="w-3 h-3 rounded-full bg-[#fe342d]"></div>
              <p class="text-[#333333]">{{ ourSolution.point }}.</p>
            </div>
            <svg
              class="w-[22px] h-[22px] col-span-full row-span-full transition-all duration-500 ease-in-out"
              :class="
                ourSolution.onHover
                  ? 'visible opacity-100'
                  : 'invisible opacity-0'
              "
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              fill="none"
              viewBox="0 0 22 22"
            >
              <circle cx="11" cy="11" r="11" fill="#55CD2B"></circle>
              <path
                stroke="#fff"
                stroke-linecap="round"
                stroke-width="3"
                d="M6 10.95 10.025 15m.031 0L17 8"
              ></path>
            </svg>
          </div>
          <div
            class="w-8 h-8 transition-all duration-500 ease-in-out flex justify-center items-center rounded-full absolute top-[1.75rem] right-[1.5rem] border border-black/10"
            :class="
              !ourSolution.onHover
                ? 'visible opacity-100'
                : ' invisible opacity-0'
            "
            @click.stop="setMouseOnOver(ourSolution.id)"
          >
            <svg
              class="size-26"
              xmlns="http://www.w3.org/2000/svg"
              width="29"
              height="30"
              fill="none"
              viewBox="0 0 29 30"
            >
              <path
                stroke="currentcolor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m10.016 19.125 8.25-8.25M10.016 10.875h8.25v8.25"
              ></path>
            </svg>
          </div>
          <div
            class="w-8 h-8 transition-all duration-500 ease-in-out flex justify-center items-center rounded-full absolute top-[1.75rem] right-[1.5rem] border border-black/10"
            :class="
              ourSolution.onHover
                ? 'visible opacity-100'
                : ' invisible opacity-0'
            "
            @click.stop="setMouseOnOver(ourSolution.id)"
          >
            <svg
              class="size-24"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                stroke="currentColor"
                stroke-width="1.25"
                d="m6 6 12 12m0-12L6 18"
              ></path>
            </svg>
          </div>
          <div class="grid box grid-cols-1 grid-rows-1 mt-7">
            <div
              class="transform transition-all duration-500 ease-in-out"
              :class="
                !ourSolution.onHover
                  ? 'translate-y-0 visible opacity-100'
                  : '-translate-y-[125%] invisible opacity-0'
              "
            >
              <h2 class="forth-h-tag !text-black !font-normal">
                {{ ourSolution.title1 }}
              </h2>
              <p class="pt-7 text-[#333333]">
                {{ ourSolution.description1 }}
              </p>
            </div>
            <div
              class="transform transition-all duration-500 ease-in-out"
              :class="
                !ourSolution.onHover
                  ? 'translate-y-[100%] invisible opacity-0'
                  : 'translate-y-0 visible opacity-100'
              "
            >
              <h2 class="forth-h-tag !text-black !font-normal">
                {{ ourSolution.title2 }}
              </h2>
              <p
                class="pt-7 text-[#333333]"
                v-html="ourSolution.description2"
              ></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <ServicesOurBenefits /> -->
    <HomeFaq :faqData="ourServices" />
  </div>
</template>

<style scoped>
.text-up-animation,
.title-up-animation {
  transition: all 0.8s ease-in-out;
  transform: translateY(45px);
  opacity: 0;
}
.text-up-animation.final,
.title-up-animation.final {
  transform: translateY(0px);
  opacity: 1;
}

.four-grid-item {
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media screen and (max-width: 1200px) {
  .four-grid-item {
    grid-column-gap: 20px;
    grid-row-gap: 40px;
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 14px;
    grid-row-gap: 40px;
  }
}
.box {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}
.box > div {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
}
.our-benefit-image {
  width: 374.65625px;
}
@media (max-width: 1439px) {
  .our-benefit-image {
    width: 26vw;
  }
}
@media (max-width: 1279px) {
  .our-benefit-image {
    width: 25vw;
  }
}
@media (max-width: 991px) {
  .our-benefit-image {
    width: 24vw;
  }
}
@media (max-width: 767px) {
  .our-benefit-image {
    width: 38vw;
  }
}
@media (max-width: 767px) {
  .our-benefit-image {
    width: 81vw;
  }
}
</style>

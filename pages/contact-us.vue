<script setup lang="ts">
const title = "Contact Us for Expert IT Solutions and Support";
const description =
  "Email: <EMAIL>, <EMAIL>, <EMAIL>. Phone: +880 1912-027073. Address: 158/27, Boalia, Kazla, Rajshahi-6204, Bangladesh.";

useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

const route = useRoute();

const viewPortController = () => {
  const dropEmail = document.getElementById("dropEmail");
  const helpEmail = document.querySelectorAll(".help_email");

  // First Observer
  const observer = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0px 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  // Second Observer
  const observerTwo = new window.IntersectionObserver(
    (entries, observerInstance) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("final");
          // observerInstance.disconnect();
        }
        // else {
        //   entry.target.classList.remove("final");
        // }
      });
    },
    {
      root: null, // The viewport is the root
      rootMargin: "0px 0px 0px 0px", // Trigger when the element's top is 50% into the viewport
      threshold: 0, // Trigger when 50% of the element is visible
    }
  );

  helpEmail.forEach((item) => {
    observerTwo.observe(item);
  });

  // Start observing with the first observer
  observer.observe(dropEmail);
};

onMounted(() => {
  viewPortController();
});
</script>

<template>
  <section class="h-full pt-[196px] pb-0 contact_body">
    <h1
      class="container-fluid text-center font-bold text-[#F1F1F2] primary-h-tag"
    >
      Let's work
      <p class="flex justify-center items-center space-x-4">
        <img
          class="lg:ml-6 ml-[40px]"
          src="/contacts/Rectangle.svg"
          alt="dash"
        /><span class="text-[#FFD700]">together.</span>
      </p>
    </h1>
    <HomeOurSolution
      class="container-fluid !pt-[64px] mb-[180px] flex justify-center"
    />
    <AboutAppointment id="appointment" />
    <div class="section-2 flex justify-center">
      <div
        class="w-full container-fluid md:py-20 py-5 flex flex-col items-start"
      >
        <p
          id="dropEmail"
          class="font-bold max-w-[550px] !text-[#F1F1F2] sixth-h-tag"
        >
          <span class="instrument-italic"
            >Drop us an email to explore for Business</span
          >
          <span class="text-[#999999]"> collaborations</span
          ><span class="instrument-italic"> or career</span>
          <span class="text-[#999999]"> opportunities.</span>
        </p>
        <div
          class="flex lg:flex-row flex-col justify-start md:ml-auto mt-[141px] lg:space-x-[130px] lg:space-y-0 space-y-10"
        >
          <div id="helpEmailOne" class="help_email flex flex-col space-y-2.5">
            <p
              class="text-[#F1F1F2] md:text-[32px] md:leading-[36px] text-2xl font-bold underline"
            >
              <EMAIL>
            </p>
            <p class="text-[#999] md:text-2xl text-xl">
              Projects, collaborations and queries.
            </p>
          </div>
          <div id="helpEmailTwo" class="help_email flex flex-col space-y-2.5">
            <p
              class="text-[#F1F1F2] md:text-[32px] md:leading-[36px] text-2xl font-bold underline"
            >
              <EMAIL>
            </p>
            <p class="text-[#999] md:text-2xl text-xl">Join our dynamic Team</p>
            <NuxtLink
              to="/career"
              class="w-[206px] h-[46px] whitespace-nowrap flex justify-center items-center bg-[#FFD700] text-[#1D1A20] text-lg font-medium !mt-[32px] px-6 py-3 rounded-full"
            >
              View open positions
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
    <div class="container-fluid flex justify-center">
      <div
        class="w-full flex lg:flex-row justify-between items-center flex-col lg:space-x-5 lg:space-y-0 space-y-20 mt-[180px]"
      >
        <div class="w-full flex flex-col space-y-10 max-w-[522px]">
          <div class="flex flex-col space-y-6 address-box md:p-10 p-5 w-full">
            <h2 class="text-[#FFD700] text-3xl md:text-4xl font-bold">
              Bangladesh
            </h2>
            <div class="text-[#999] md:text-2xl text-xl">
              <p>158/27, Boalia, Kazla,</p>
              <p>Rajshahi-6204, Bangladesh</p>
            </div>
            <p class="md:text-2xl text-xl text-[#F1F1F2]">+880 1326 506464</p>
          </div>
          <div class="flex flex-col space-y-6 address-box md:p-10 p-5 w-full">
            <h2 class="text-[#FFD700] text-3xl md:text-4xl font-bold">USA</h2>
            <div class="text-[#999] md:text-2xl text-xl">
              <p>903 1st Street North #1061 Hopkins,</p>
              <p>MN 55343, United States</p>
            </div>
            <p class="md:text-2xl text-xl text-[#F1F1F2]">+1 612 300-7711</p>
          </div>
          <div class="flex flex-col space-y-6 address-box md:p-10 p-5 w-full">
            <h2 class="text-[#FFD700] text-3xl md:text-4xl font-bold">
              Finland
            </h2>
            <div class="text-[#999] md:text-2xl text-xl">
              <p>Viulutie 1 A 1, 00420 Helsinki,</p>
              <p>Uusimaa, Finland</p>
            </div>
            <p class="md:text-2xl text-xl text-[#F1F1F2]">+358 402545717</p>
          </div>
        </div>
        <div class="w-full xl:w-auto max-w-[738px]">
          <img class="w-full" src="/contacts/office_map.svg" alt="office map" />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
@import "~/assets/scss/pages/contact.scss";
.section-2 {
  background: black;
  // box-shadow: 0px 16px 42px 0px rgba(0, 30, 108, 0.3);
}
#dropEmail,
.help_email {
  opacity: 0;
  transform: translateY(100px);
  transition: all 0.8s ease-in-out;
}

#dropEmail.final,
.help_email.final {
  opacity: 1;
  transform: translateY(0px);
}
</style>

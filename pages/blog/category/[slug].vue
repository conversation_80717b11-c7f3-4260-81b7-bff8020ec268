<script setup>
import { storeToRefs } from "pinia";
import { useBlogStore } from "~/stores/pages/blog/blog";

const title = "Devxhub Blog | Insights on Software Development & Technology";
const description =
  "Stay updated with the latest trends, tips, and insights in software development, AI/ML, DevOps, and more from Devxhub’s experts.";
const keyWords =
  "software development blog, technology insights, AI/ML articles, DevOps tips";

useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const nuxtApp = useNuxtApp();

// pinia store
const { setPost } = useBlogStore();
const { posts } = storeToRefs(useBlogStore());
const { setSearchBlogsPost } = useBlogStore();
// setSearchBlogsPost(searchText);

setTimeout(() => {
  if (posts.value?.results?.length === 0) {
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: "There have no blog post.",
      className: "alert_error",
    });
  }
}, 500);

// data
const currentPage = ref(1);
if (posts.value === null) {
  setPost(currentPage.value);
}

const callMoreFunction = computed(() => {
  if (
    posts.value?.meta.pagination.offset + posts.value?.meta.pagination.limit >=
    posts.value?.meta.pagination.count
  ) {
    return lessMore();
  } else {
    return lodeMore();
  }
});
const getText = computed(() => {
  return posts.value?.meta.pagination.offset +
    posts.value?.meta.pagination.limit >=
    posts.value?.meta.pagination.count
    ? "Less more"
    : "See more";
});
const lessMore = () => {
  currentPage.value = 1;
  setPost(currentPage.value);
};
const lodeMore = () => {
  currentPage.value = currentPage.value + 1;
  setPost(currentPage.value);
};
onMounted(async () => {
  await nextTick();
  await setSearchBlogsPost(useRoute().params.slug);
  window.scrollTo(0, 0);
});
</script>

<template>
  <div>
    <div class="container-fluid pt-26 md:pt-36 lg:pt-[196px]">
      <div class="flex flex-col text-center space-y-2">
        <h1 class="primary-h-tag text-center text-[#F1F1F2]">
          <span class="text-[#999]">Devxhub</span
          ><span class="instrument-italic"> Blog</span>
        </h1>
        <p class="primary-p-tag !text-[#999] pt-5 !mt-0">
          Insights, tips, and tricks to help you grow your business with our
          service.
        </p>
      </div>
      <div class="md:pt-16 pt-8"></div>
      <BlogsFeatured />
    </div>
    <div class="container-fluid pt-32 md:pt-[180px] pb-[30px]">
      <div
        class="w-full flex md:flex-row md:space-y-0 space-y-[30px] flex-col md:justify-between items-center lg:mb-[42px] mb-[30px]"
      >
        <h2 class="sixth-h-tag font-[700] text-[#F1F1F2]">
          <span class="text-[#999]">Read </span>
          <span class="instrument-italic">Blogs</span>
        </h2>
        <form
          class="bg-white p-2 flex items-center rounded-full w-full sm:max-w-[260px]"
        >
          <input
            class="h-9 text-[#030307] w-full px-3 text-base rounded-full outline-none border border-transparent placeholder:text-[#8A8A8A]"
            type="text"
            placeholder="Search…"
          />
          <button
            aria-label="Search Blogs"
            class="flex justify-center items-center rounded-full min-w-9 h-9 bg-[#FFD700]"
          >
            <fa class="text-white" :icon="['fa-solid', 'search']" />
          </button>
        </form>
      </div>
      <BlogsContainer :posts="posts?.results" />
      <!-- <div
        class="w-full bg-[#F2F2F3] h-20 sticky bottom-0 left-0 z-10 flex items-center"
      > -->
      <button
        class="w-[150px] h-10 mt-16 bg-[#FFD700] text-lg text-[#1D1A20] rounded-full flex justify-center items-center font-medium"
        @click="callMoreFunction"
      >
        {{ getText }}
      </button>
      <!-- </div> -->
      <!-- <BlogPagination :totalPost="posts" /> -->
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

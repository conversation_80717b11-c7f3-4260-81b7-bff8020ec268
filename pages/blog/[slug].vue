<script setup lang="ts">
import { storeToRefs } from "pinia";
import defaultImage from "~/assets/img/blog/image-not-found.webp";
import { useBlogStore } from "~/stores/pages/blog/blog";
import type { singlePost } from "~/types/posts";

// api call
const { data: post } = await useFetch<singlePost>(
  `/api/blogs/posts/${useRoute().params.slug}`
);
const { posts } = storeToRefs(useBlogStore());
const { setPost } = useBlogStore();

useSeoMeta({
  title: () => `${post?.value?.title} | Blog | Devxhub`,
  ogTitle: () => `${post?.value?.title} | Blog | Devxhub`,
  description: () => post?.value?.description,
  ogDescription: () => post?.value?.description,
  ogImage: () => post?.value?.image,
  twitterCard: "summary_large_image",
});

const relatedPosts = computed(() => {
  return posts.value?.results?.slice(0, 3);
});

const dateFormat = (date: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  return new Date(date).toLocaleDateString(undefined, options);
};

onMounted(() => {
  nextTick(() => {
    if (!posts.value) {
      setPost(1);
    }
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 1000);
  });
});

// const removeEmptyPTag = () => {
//   const paragraphs = document.querySelectorAll(".blog-content p");
//   console.log(paragraphs, 'paragraphs')
//   // Loop through each <p> element
//   paragraphs.forEach((p) => {
//     // Check if the <p> is empty (either completely empty or only contains whitespace)
//     if (!p.textContent.trim()) {
//       p.remove(); // Remove the empty <p> tag
//     }
//   });
// };
</script>

<template>
  <div v-if="post" class="w-full bg-white">
    <div
      class="pt-36 lg:pt-48 flex flex-col text-center space-y-6 !bg-[#f3f1ee] pb-[90px]"
    >
      <div class="container-fluid">
        <div class="flex space-x-4 items-center">
          <NuxtLink to="/blog" class="primary-p-tag !text-[#000000b3]"
            >Blog</NuxtLink
          >
          <BaseIconRightArrow
            extraClass="text-sm text-black"
            icon="chevron-right"
          />
          <p class="primary-p-tag !text-black text-left">
            {{ post.title }}
          </p>
        </div>
        <div class="flex flex-col space-y-8 lg:space-y-10 mt-5 text-left">
          <h1
            class="primary-h-tag !text-black font-bold break-all md:break-normal"
          >
            {{ post.title }}
          </h1>
          <div
            class="flex md:flex-row flex-col md:space-x-5 md:space-y-0 space-y-4 md:items-center items-start"
          >
            <div class="flex space-x-5 items-center">
              <img
                class="size-10 lg:size-20 rounded-full"
                src="/public/images/assets/img/blank-profile-picture.png"
                :alt="post?.author.name"
              />
              <p class="primary-p-tag !text-black font-medium">
                {{ post?.author.name }}
              </p>
            </div>
            <div
              class="flex sm:flex-row flex-col sm:space-y-0 space-x-0 space-y-4 sm:space-x-5 sm:items-center items-start"
            >
              <div
                class="bg-white rounded-full py-2 px-5 text-[#7a7a7a] text-base leading-6 font-medium"
              >
                {{ post.categories[0].name }}
              </div>
              <div class="flex space-x-[12px] items-center">
                <div class="dot-sign sm:block hidden"></div>
                <div
                  v-if="post?.created_at"
                  class="primary-p-tag !text-[#7a7a7a]"
                >
                  {{ dateFormat(post.created_at) }}
                </div>
                <div class="dot-sign"></div>
                <div
                  v-if="post.read_time"
                  class="primary-p-tag !text-[#7a7a7a]"
                >
                  {{ post.read_time }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="container-fluid -mt-12">
      <img
        class="w-full h-full max-h-[80vh] rounded-[10px]"
        :src="post?.image ? post.image : defaultImage"
        :alt="post.title"
      />
    </div>
    <div class="mt-[96px] relative">
      <div class="container-fluid">
        <div class="resource-toc-component hidden">
          <h2 class="text-sm leading-[120%]">Table of content</h2>
          <div class="mt-[18px] flex space-y-[1.25rem] text-[#000] text-xl">
            <NuxtLink>Table of content</NuxtLink>
          </div>
        </div>
        <BlogsPostToSocial class="flex space-x-2.5 mb-5 md:hidden" />
        <div class="blog-contents">
          <BlogsPostToSocial
            class="sticky-block md:flex hidden flex-col space-y-2.5"
          />
          <div>
            <div
              v-if="post?.content"
              class="resources-rt w-richtext fourth-p-tag !text-black overflow-hidden blog-content"
              v-html="post?.content"
            ></div>
            <div
              v-if="post?.comment_set"
              v-for="(comment_set, index) in post.comment_set"
              :key="index"
              class="p-6 rounded-lg bg-[#f3f1ee] text-[#000]"
            >
              <div class="flex space-x-[12px] items-center">
                <img
                  class="w-8 h-8 rounded-full"
                  src="/public/images/assets/img/blank-profile-picture.png"
                  :alt="post?.author.name"
                />
                <p class="text-[#000]">{{ post?.author.name }}</p>
              </div>
              <p class="mt-6">
                {{ comment_set.description }}
              </p>
            </div>
            <BlogsPostToSocial class="md:hidden flex space-x-3 my-8" />
            <BlogsBookFreeCall class="md:hidden bg-black p-6 rounded-[10px]" />
          </div>
          <div class="sticky-block md:flex hidden">
            <BlogsBookFreeCall class="bg-black p-6 rounded-[10px]" />
          </div>
        </div>
      </div>
    </div>
    <div class="mt-[90px] pb-[80px] !bg-[#f3f1ee]">
      <div class="container-fluid flex flex-col space-y-16 pt-[90px]">
        <div class="flex justify-between items-center">
          <h2 class="sixth-h-tag font-[700] text-[#F1F1F2]">
            <span class="text-black">Similar </span>
            <span class="instrument-italic !text-black">articles</span>
          </h2>
          <NuxtLink
            to="/blog"
            class="flex justify-start items-center space-x-2"
          >
            <p class="text-base font-medium text-black">Explore all</p>
            <div
              class="border border-black size-5 rounded-full flex justify-center items-center"
            >
              <svg
                width="5"
                height="8"
                viewBox="0 0 5 8"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="size-2.5"
              >
                <path
                  d="M0.829161 7.87638C0.621153 8.0582 0.305296 8.03696 0.123472 7.82913C-0.0581686 7.62113 -0.0369284 7.30527 0.170896 7.12345L3.74072 4.00003L0.170713 0.876436C-0.0372946 0.694613 -0.0583517 0.378756 0.123289 0.170931C0.305113 -0.037077 0.62097 -0.0583172 0.828977 0.123507L4.82892 3.62357C4.9375 3.71842 4.99975 3.85575 4.99975 4.00003C4.99975 4.14432 4.9375 4.28147 4.82892 4.3765L0.829161 7.87638Z"
                  fill="#000000"
                ></path>
              </svg>
            </div>
          </NuxtLink>
        </div>
        <div class="four-grid-item">
          <NuxtLink
            v-for="(relatedPost, index) in relatedPosts"
            :key="index"
            :to="`/blog/${relatedPost.slug}`"
            class="h-full flex flex-col"
          >
            <BlogsCard :post="relatedPost" titelClass="text-black" />
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.dot-sign {
  width: 5px;
  height: 5px;
  background-color: #7a7a7a;
  border-radius: 50%;
}
.blog-contents {
  column-gap: 80px;
  grid-template-rows: auto;
  grid-template-columns: 44px auto 305px;
  grid-auto-columns: 1fr;
  justify-content: start;
  align-items: start;
  width: 100%;
  display: grid;
}
.sticky-block {
  width: 100%;
  position: sticky;
  top: 160px;
}
.resource-toc-component {
  background-color: #f3f1ee;
  border-radius: 8px;
  width: 100%;
  padding: 24px;
}
.four-grid-item {
  grid-column-gap: 24px;
  grid-row-gap: 60px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}
@media (max-width: 1023px) {
  .blog-contents {
    grid-template-columns: 44px auto auto;
    column-gap: 40px;
  }
}
@media (max-width: 767px) {
  .four-grid-item {
    grid-column-gap: 24px;
    grid-row-gap: 60px;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }
  .blog-contents {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 639px) {
  .four-grid-item {
    grid-column-gap: 0px;
    grid-row-gap: 60px;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }
}
</style>

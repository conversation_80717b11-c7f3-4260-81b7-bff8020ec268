<script setup>
const title = "Privacy & Policy | Devxhub";
const description =
  "When you start up for the service with Devxhub, we collect personal information from you including your name, phone / mobile number, e-mail address, billing address etc. We may also collect the URL from which you linked into our site, your IP address, and your browser type and version, all of which are collected in an anonymous manner without being linked to any of your personal information.";
useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});
</script>

<template>
  <div class="container-fluid pt-32 md:pt-48 pb-32 text-light-white">
    <div class="text-center space-y-5 pb-20">
      <h1 class="text-4xl lg:text-5xl font-semibold">Privacy & Policy</h1>
      <!-- <p class="text-lg font-medium">Devxhub</p> -->
    </div>
    <div class="space-y-6">
      <div class="">
        <h2 class="text-xl font-semibold text-primary pb-2">
          Collected of Customer Information
        </h2>
        <p class="text-base leading-7">
          When you start up for the service with Devxhub, we collect personal
          information from you including your name, phone / mobile number,
          e-mail address, billing address etc. We may also collect the URL from
          which you linked into our site, your IP address, and your browser type
          and version, all of which are collected in an anonymous manner without
          being linked to any of your personal information.
        </p>
      </div>
      <div class="">
        <h2 class="text-xl font-semibold text-primary pb-2">
          Use of Customer Information
        </h2>
        <p class="text-base leading-7 pb-4">
          Customer privacy is something we take very seriously at Devxhub. We do
          not sell or disseminate (except in cases where required to by law
          enforcement companies) the personal information of our customers to
          any parties outside of Devxhub or its subsidiary companies, for any
          purpose other than communicating messages provided by Devxhub. We use
          customer information for the purposes of creating and maintaining
          individual customer accounts, contacting customers in case of problems
          with their account(s), sending personal information, for statistical
          purposes, to administer our systems, to conduct surveys, to administer
          drawings or contests, to provide product and marketing updates, and to
          provide technical support.
        </p>
        <p class="text-base leading-7">
          Devxhub may engage with companies or companies that may come in
          contact with some customer data only in cases where that access is
          required to support Devxhub’s own goals and initiatives. Some optional
          partner offers and services may require customer information to be
          provided to third parties as a necessary part of establishing service
          with them. Please note that the contact information that you provide
          to us is used to register your domain name and is, by the very nature
          of the domain registration system, available for public viewing in
          many places on the Internet via the use of the ‘whois’ tool.
        </p>
      </div>
      <div class="">
        <h2 class="text-xl font-semibold text-primary pb-2">
          Whois Privacy Services
        </h2>
        <p class="text-base leading-7">
          Whois Privacy as offered by Devxhub does not act as a shield from
          legal liability in conjunction with Domain Registration. In order to
          preserve neutrality in disputes between 3rd parties and domain
          registrants, Devxhub will act in accordance with ICANN Registrar
          Accreditation agreement section ******* upon receipt of actionable
          evidence of illegal activity. This means that identifying information
          will be provided to law enforcement and officials of the court
          including attorneys as situations require in order preserving
          Devxhub’s neutrality in any litigation.
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

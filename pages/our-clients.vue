<script setup>
const title = "Our Clients | Devxhub";
const description =
  "Some of our clients are CX Brainstorm, University of Maat, Sharp Archive, LiveCX, Now Meta, Go <PERSON>, Le No<PERSON>lliste, Khemetic Nation, Hospital Note, Farenow";

useSeoMeta({
  title: () => title,
  ogTitle: () => title,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

const config = useRuntimeConfig();

const cards = ref([
  {
    src: "/ourClients/ibm-logo.png",
    text: "IBM",
    siteLink: "https://www.ibm.com/",
  },
  {
    src: "/ourClients/sharpArchive.svg",
    text: "Sharp Archive",
    siteLink: "https://sharparchive.com/",
  },
  {
    src: "/ourClients/parkolay.webp",
    text: "Parkolay",
    siteLink: "https://www.upbrasil.com/",
  },
  {
    src: "/ourClients/khemetic.webp",
    text: "Khemetic Nation",
    siteLink: "https://www.khemetic.com/",
  },
  {
    src: "/ourClients/brainStorm.svg",
    text: "CX Brainstorm",
    siteLink: "https://cxbrainstorm.com/",
  },
  {
    src: "/ourClients/hospitalNote.png",
    text: "Hospital Note",
    siteLink: "https://hospitalnote.com/",
  },
  {
    src: "/ourClients/tanto_app_2x.webp",
    text: "Tanto App",
    siteLink: "https://www.upbrasil.com/",
  },
  {
    src: "/ourClients/upBrasil.png",
    text: "Upbrasil",
    siteLink: "https://www.upbrasil.com/",
  },
  {
    src: "/ourClients/nowMetaLogo.png",
    text: "Now Meta",
    siteLink: "https://nowmeta.io/",
  },
  {
    src: "/ourClients/goVelo.png",
    text: "Go Velo",
    siteLink: "https://go-velo.cc/",
  },
  {
    src: "/ourClients/liveCx.svg",
    text: "LiveCX",
    siteLink: "https://livecx.com/",
  },
  {
    src: "/ourClients/healing.svg",
    text: "One World Family",
    siteLink: "https://oneworldfamily.com/",
  },
  {
    src: "/ourClients/maatUniversity.svg",
    text: "University of Maat",
    siteLink: "https://maatk12.com/",
  },
  {
    src: "/ourClients/farenow.svg",
    text: "Farenow",
    siteLink: "https://www.farenow.com/",
  },
  {
    src: "/ourClients/sk8y_app_2x.png",
    text: "SK8Y App",
    siteLink: "https://oneworldfamily.com/",
  },
  {
    src: "/ourClients/leNouvelliste.svg",
    text: "Le Nouvelliste",
    siteLink: "https://lenouvelliste.com/",
  },
  {
    src: "/ourClients/sk_mobile_school_2x.webp",
    text: "SK Mobile School",
    siteLink: "https://oneworldfamily.com/",
  },
  
]);
onMounted(() => {
  setTimeout(() => {
    const allAboutUs = document.querySelectorAll(".ourClientSection");
    const observer = new window.IntersectionObserver((entries) => {
      entries.forEach((singleDiv) => {
        if (singleDiv.isIntersecting) {
          singleDiv.target.classList.remove("notInViewPort");
          singleDiv.target.classList.add("nowInViewPort");
        } else if (!singleDiv.isIntersecting) {
          singleDiv.target.classList.add("notInViewPort");
          singleDiv.target.classList.remove("nowInViewPort");
        }
      });
    });
    allAboutUs.forEach((item) => {
      observer.observe(item);
    });
  }, 700);
});
</script>

<template>
  <section class="container-fluid">
    <div class="pt-[100px] ourClientSection">
      <div>
        <h1
          class="text-center mb-8 font-semibold text-primary mt-16 text-[42px] sm:text-[52px] md:text-7xl lg:text-8xl lg:leading-[100.8px] md:leading-[82px] sm:leading-[58px] leading-[42px]"
        >
          Our Clients
        </h1>
        <p class="text-[#F0F0F0] text-lg text-center"></p>
      </div>

      <div class="flex flex-wrap justify-center">
        <div
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 justify-center gap-6 md:gap-8 lg:gap-9 pb-[150px] mx-auto mt-20 lg:hidden"
        >
          <template v-for="(card, index) in cards">
            <div
              v-if="
                (config.public.workflow === 'live' &&
                  card.text !== 'Upbrasil' &&
                  card.text !== 'IBM') ||
                config.public.workflow === 'dev'
              "
              :key="index"
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[300px]"
              :class="
                index === 0 || index === 2 || index === 4 ? 'lg:mt-20' : ''
              "
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="h-[150px] flex flex-col justify-center">
                      <img
                        :src="card.src"
                        :alt="card.text"
                        class="h-full aspect-square object-contain"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      {{ card.text }}
                    </p>
                  </div>
                  <!-- 
                  <NuxtLink
                    :to="card.siteLink"
                    target="_blank"
                    class="text-white bottom-0 text-center cursor-pointer"
                  >
                    Visit Now >
                  </NuxtLink> -->
                </div>
              </div>
            </div>
          </template>
        </div>

        <div class="hidden lg:flex space-x-10 mt-20 md:my-20">
          <!-- Column 1 lg:mt-24-->
          <div class="flex flex-col space-y-10 lg:mt-24">
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/ibm-logo.png"
                        alt="IBM_Logo"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      IBM
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-[200px] aspect-square object-contain"
                        src="/ourClients/khemetic.webp"
                        alt="Khemetic Nation"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Khemetic Nation
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/tanto_app_2x.webp"
                        alt="Tanto App"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Tanto App
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <img
                      class="h-[200px] aspect-square object-contain"
                      src="/ourClients/goVelo.png"
                      alt="Go Velo"
                    />
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Go Velo
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <img
                      class="h-[200px] aspect-square object-contain"
                      src="/ourClients/maatUniversity.svg"
                      alt="University of Maat"
                    />
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      University of Maat
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="h-[200px] flex flex-col justify-center">
                      <img
                        class="h-[200px] aspect-square object-contain"
                        src="/ourClients/leNouvelliste.svg"
                        alt="Le Nouvelliste"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Le Nouvelliste
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Column 2 -->
          <div class="flex flex-col space-y-10">
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <img
                      class="h-[200px] aspect-square object-contain"
                      src="/ourClients/sharpArchive.svg"
                      alt="SharpArchive"
                    />
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Sharp Archive
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <img
                      class="h-[200px] aspect-square object-contain"
                      src="/ourClients/brainStorm.svg"
                      alt="CX Brainstorm"
                    />
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      CX Brainstorm
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="h-[200px] flex flex-col justify-center">
                      <img
                        class="h-[200px] aspect-square object-contain"
                        src="/ourClients/upBrasil.png"
                        alt="Up Brasil"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Upbrasil
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <img
                      class="h-[200px] aspect-square object-contain"
                      src="/ourClients/liveCx.svg"
                      alt="LiveCX"
                    />
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      LiveCX
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/farenow.svg"
                        alt="Farenow"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Farenow
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/sk_mobile_school_2x.webp"
                        alt="SKMobile School"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      SK Mobile School
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Column 3 lg:mt-24-->
          <div class="flex flex-col space-y-10 lg:mt-24">
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/parkolay.webp"
                        alt="Parkolay"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Parkolay
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/hospitalNote.png"
                        alt="Hospital Note"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Hospital Note
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/nowMetaLogo.png"
                        alt="Now Meta"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      Now Meta
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="h-[200px] flex flex-col justify-center">
                      <img
                        class="h-[200px] aspect-square object-contain"
                        src="/ourClients/healing.svg"
                        alt="One World Family"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      One World Family
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex justify-center items-center from-[#2A1E56] to-[#3D2C79] w-[300px] lgx:w-[350px] 2xl:w-[405px] h-[400px]"
            >
              <div
                class="w-full h-full rounded-[30px] cardBg from-[#2A1E56] to-[#3D2C79] flex justify-center items-center z-[1]"
              >
                <div class="flex flex-col h-full p-10 justify-between">
                  <div class="flex flex-col space-y-10">
                    <div class="flex flex-col justify-center h-[200px]">
                      <img
                        class="h-full aspect-square object-contain"
                        src="/ourClients/sk8y_app_2x.png"
                        alt="SK8Y App"
                      />
                    </div>
                    <p
                      class="text-center text-primary font-semibold text-xl lg:text-3xl"
                    >
                      SK8Y App
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.nowInViewPort {
  @apply transition-all ease-in duration-1000 opacity-100;
}
.notInViewPort {
  @apply transition-all ease-in duration-300 opacity-0;
  transform: translate3d(0, 100px, 0) scale(0.6);
}
.cardBg {
  background: linear-gradient(145.09deg, #2a1e56 11.97%, #3d2c79 89.93%);
  box-shadow: 0px 16px 42px rgba(0, 30, 108, 0.3);
  border-radius: 30px;
}
</style>

<script setup>
const metaTitle = "Explore Our Development Process";
const description =
  "Understand Devxhub’s agile and collaborative approach to delivering high-quality software solutions tailored to your business needs.";
const keyWords =
  "software development process, agile methodology, Devxhub process, project management";

useSeoMeta({
  title: () => metaTitle,
  ogTitle: () => metaTitle,
  description: () => description.slice(0, 300),
  ogDescription: () => description.slice(0, 300),
});

useHead({
  meta: [
    {
      name: "keywords",
      content: keyWords,
    },
  ],
});

const heroTitle = `<span class="text-[#999]">A Process —</span><span class="instrument-italic"> that Leads to Success</span>`;
const heroDes = `Our proven approach combines innovation, collaboration, and advanced AI / ML technology to deliver software solutions designed specifically for your needs. From initial consultation to final deployment, our process ensures quality, scalability, security, and success aligned with your business goals.`;
const showImage = true;

const title = `<h2 class="">Our <span class="instrument-italic">Services</span></h2>`;
const ourServices = ref([
  {
    id: 1,
    slectedItem: true,
    title: "IT Staff Augmentation",
    description: `<p>Looking to scale your software development team without the overhead of full-time hires?
We connect you with a dedicated remote team of highly skilled top developers from Bangladesh
at a lower budget. Our IT staff augmentation services provide experienced professionals to fill
critical gaps, accelerate project timelines, and enhance your in-house capabilities. We match you
with top talent tailored to your project requirements.</p>
<p class="pt-10">With full flexibility, budget efficiency, and a deep dive into a global pool of experts, ensure your
team stays agile and competitive.
Optimize productivity, grow your revenue, and drive success with our reliable and scalable IT
staffing solutions.</p>`,
  },
  {
    id: 2,
    slectedItem: false,
    title: "Full Stack Development | Web, Mobile Application",
    description: `<p>We specialize in Full-Stack Development, delivering end-to-end solutions customized to meet the unique needs of any industry. Our expertise spans Frontend Design, Backend and API Development, Database Architecture, and Server Management, enabling us to create scalable, dynamic, and efficient web and mobile applications.</p><p class="pt-10">Our approach focuses on building seamless digital experiences, from intuitive, user-friendly interfaces to powerful, secure backend systems that drive business growth and innovation.</p>`,
  },
  {
    id: 3,
    slectedItem: false,
    title: "Custom & Enterprise Software Development",
    description: `<p>Our Custom & Enterprise Software Development services are designed to create scalable, secure, and user-friendly large and complex systems and applications that drive growth and streamline your operations.</p><p>We got covered a wide range of industries like healthcare, finance, retail, Govt. & IT.</p><p class="pt-10">From building custom CRMs, ERPs, HIMs, and HRMs for industries & platforms, mobile apps, and AI-powered tools, we deliver solutions tailored to your unique needs. Whether you’re modernizing legacy systems or developing innovative modern tools, our expertise ensures your software aligns seamlessly with your business goals—helping you stay ahead in a competitive market.</p>`,
  },
  {
    id: 4,
    slectedItem: false,
    title: "MVP, SaaS, End-to-End Development",
    description: `<p>We specialize in building MVPs to validate your ideas quickly, creating robust SaaS platforms for scalable business models, and delivering comprehensive end-to-end solutions tailored to your needs. From ideation and design to development, deployment, and ongoing support, we handle the entire lifecycle to ensure seamless execution.</p><p class="pt-10">With a focus on innovation, scalability, and user-centric design, our solutions empower your business to launch faster, grow smarter, and stay ahead in a competitive market.</p>`,
  },
  {
    id: 5,
    slectedItem: false,
    title: "AI/ML Development & Integration",
    description: `<p>We help you utilize the power of artificial intelligence and machine learning to automate processes, uncover actionable insights, and deliver personalized user experiences, from developing intelligent AI software and chatbots to integrating AI-powered tools into your existing or current software.</p><p class="pt-10">Our AI/ML services are designed to drive scalability, precision, and innovation, enabling your business to make informed decisions, boost efficiency, and maintain a competitive edge.</p>`,
  },
  {
    id: 6,
    slectedItem: false,
    title: "DevOps & Cloud Solutions",
    description: `<p>We specialize in automating workflows, enhancing collaboration, and optimizing infrastructure to accelerate delivery cycles and ensure scalability. From cloud migration and management to CI/CD pipelines and containerization, we deliver customized solutions to help your business stay agile and efficient. With a focus on reliability, security, and performance, reducing costs and increasing operational efficiency.</p><p class="pt-10">Using AWS, Google Cloud, Digital Ocean, Linux, Windows Server, Docker, Ansible, and Kubernetes.</p><p>We have certified AWS & Cloud Solution Architects.</p>`,
  },
]);

const ourServicesHeight = () => {
  // Loop through expertiseAreas to calculate heights
  ourServices.value.forEach((ourService) => {
    const element = document.getElementById(ourService.id);
    if (element) {
      ourService.height = element.getBoundingClientRect().height;
    }
  });
};
const specificValuesSelect = (id) => {
  ourServices.value.forEach((ourService) => {
    if (ourService.id === id) {
      ourService.slectedItem = !ourService.slectedItem;
    } else {
      ourService.slectedItem = false;
    }
  });
};
</script>

<template>
  <div class="container-fluid md:pt-[180px] pt-[160px]">
    <OurworkingprocessHeroSection
      :heroTitle="heroTitle"
      :heroDes="heroDes"
      :showImage="showImage"
      :titleClass="`max-w-[805px]`"
    />
    <OurworkingprocessAgileProcess />
    <OurworkingprocessTeamExtensionProcess />
    <OurworkingprocessBenefitTeamExtension />
    <OurworkingprocessImplementationProcess />
    <OurworkingprocessBenefitsAgileDevelopment />
    <OurworkingprocessWhatToExpect />
    <OurworkingprocessOurServices
      :title="title"
      :ourServices="ourServices"
      :ourServicesHeight="ourServicesHeight"
      :specificValuesSelect="specificValuesSelect"
    />
  </div>
</template>

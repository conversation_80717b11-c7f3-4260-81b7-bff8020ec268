import { defineStore, Store } from "pinia";

export const useHeaderStore = defineStore("header", () => {
  // state
  const isMobileMenuRef = ref<boolean>(false);
  const isMobileMenuItemRef = ref<boolean>(false);

  const isMobileMenu = computed(() => isMobileMenuRef.value);
  const isMobileMenuItem = computed(() => isMobileMenuItemRef.value);

  // actions
  const setMobileMenu = (payload: boolean) => {
    if (payload) {
      isMobileMenuRef.value = payload;
      // isMobileMenuItemRef.value = payload
      setTimeout(() => {
        isMobileMenuItemRef.value = payload;
      }, 600);
    } else {
      isMobileMenuItemRef.value = payload;
      setTimeout(() => {
        isMobileMenuRef.value = payload;
      }, 600);
    }
  };
  return {
    isMobileMenu,
    isMobileMenuItem,
    setMobileMenu,
  };
});
